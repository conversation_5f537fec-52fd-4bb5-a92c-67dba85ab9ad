# SNMP配置管理系统

## 概述

本系统将原本硬编码在 `SFOid.java` 中的SNMP客户端IP和端口号改为可视化配置管理，支持动态修改和实时生效。

## 功能特性

### 1. 可视化配置界面
- 提供Web界面进行配置管理
- 支持快速配置SNMP客户端IP和端口
- 实时显示当前生效的配置
- 支持连接测试功能

### 2. 数据库存储
- 配置信息存储在数据库中
- 支持配置的增删改查
- 支持配置启用/禁用状态管理

### 3. 动态刷新
- 配置修改后立即生效
- 无需重启应用程序
- 自动同步到 `SFOid` 类中的静态变量

## 使用方法

### 1. 访问配置页面
- 在告警管理页面点击"SNMP配置"按钮
- 或直接访问：`http://localhost:8080/snmp-config.html`

### 2. 快速配置
1. 在"快速配置"区域输入新的IP地址和端口号
2. 点击"快速更新"按钮
3. 系统会自动更新配置并刷新当前生效配置

### 3. 高级配置管理
1. 在"配置管理"区域可以查看所有配置项
2. 点击"添加配置"可以新增配置项
3. 点击"编辑"可以修改现有配置
4. 点击"删除"可以删除配置项

### 4. 测试连接
1. 输入要测试的IP地址和端口号
2. 点击"测试连接"按钮
3. 系统会验证连接是否可用

## API接口

### 配置管理接口

#### 1. 获取当前生效配置
```
GET /api/snmp-config/current
```

#### 2. 分页查询配置
```
POST /api/snmp-config/page
```

#### 3. 保存配置
```
POST /api/snmp-config/save
```

#### 4. 批量更新配置
```
POST /api/snmp-config/batch-update
```

#### 5. 删除配置
```
DELETE /api/snmp-config/{id}
```

#### 6. 刷新配置
```
POST /api/snmp-config/refresh
```

#### 7. 测试连接
```
POST /api/snmp-config/test-connection
```

## 数据库表结构

### snmp_config 表
```sql
CREATE TABLE `snmp_config` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` varchar(500) NOT NULL COMMENT '配置值',
  `description` varchar(200) DEFAULT NULL COMMENT '配置描述',
  `config_type` varchar(20) DEFAULT 'STRING' COMMENT '配置类型',
  `enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `sort_order` int(11) DEFAULT 1 COMMENT '排序',
  `enable_flag` tinyint(1) DEFAULT 1 COMMENT '删除标识',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
);
```

## 默认配置

系统会自动初始化以下默认配置：

| 配置键 | 默认值 | 描述 | 类型 |
|--------|--------|------|------|
| snmp.client.ip | ************* | SNMP客户端IP地址 | IP |
| snmp.client.port | 162 | SNMP客户端端口号 | PORT |

## 配置生效机制

1. **应用启动时**：自动从数据库加载配置到 `SFOid` 类
2. **配置修改时**：立即更新 `SFOid` 类中的静态变量
3. **手动刷新**：可通过API或界面手动刷新配置

## 注意事项

1. **配置键唯一性**：每个配置键在系统中必须唯一
2. **IP地址格式**：IP地址配置请确保格式正确
3. **端口号范围**：端口号请使用1-65535范围内的有效值
4. **配置生效**：配置修改后会立即生效，无需重启应用

## 扩展说明

如需添加新的配置项：

1. 在数据库中添加新的配置记录
2. 在 `SFOid` 类中添加对应的静态变量
3. 在 `SnmpConfigServiceImpl.refreshSFOidConfig()` 方法中添加配置刷新逻辑

## 故障排除

### 1. 配置不生效
- 检查配置是否启用状态
- 尝试手动刷新配置
- 查看应用日志是否有错误信息

### 2. 连接测试失败
- 检查IP地址和端口号是否正确
- 确认网络连接是否正常
- 检查防火墙设置

### 3. 界面无法访问
- 确认应用已正常启动
- 检查端口是否被占用
- 查看浏览器控制台是否有错误信息
