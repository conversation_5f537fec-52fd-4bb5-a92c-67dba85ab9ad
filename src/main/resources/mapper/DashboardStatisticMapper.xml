<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.key.win.biz.dashboard.statistic.dao.DeviceCountStatisticDao">

    <select id="deviceCountStatistic" resultType="com.key.win.biz.dashboard.statistic.vo.DeviceCountStatisticVO">
        SELECT *
        from sf_statistic_device_count_vo
    </select>

    <select id="currentDayAlarmCount" resultType="map">
        select count(1) as alarmNum, 'currentDay' as timeDesc
        from real_time_warn
        where alarm_status = 0
          and alarm_time &gt; DATE_FORMAT(NOW(), '%Y-%m-%d 00:00:00')
          and enable_flag = 1
        UNION
        select count(1) as alarmNum, 'alarmCount' as timeDesc
        from real_time_warn
        where alarm_status = 0
          and enable_flag = 1
        UNION
        select count(1) as alarmNum, 'clearAlarm' as timeDesc
        from real_time_warn
        where alarm_status = 2
          and enable_flag = 1
        UNION
        select count(1) as alarmNum, 'confirmAlarm' as timeDesc
        from real_time_warn
        where alarm_status = 1
          and enable_flag = 1
    </select>


    <select id="statisticGeneralSituation" resultType="map">
        select count(1) as count , 'site' as type
        from site_info site
        where enable_flag = 1
        UNION
        select count(1) as count , 'unit' as type
        from belong_unit site
        where enable_flag = 1
        UNION
        select count(1) as count , 'ant' as type
        from sf_topo_node node
        where node.type in ('10', '1001', '1002', '1003', '1004', '1005')
        UNION
        select count(1) as count , 'probe' as type
        from probe ant
        where enable_flag = 1
    </select>

    <select id="listLineAndStation" resultType="com.key.win.biz.dashboard.statistic.vo.LineStationVO">
        select *
        from sf_line_station_vo
        where line_id = #{lineId}
    </select>

    <!--  统计室分车站个数  -->
    <select id="siteCount" resultType="int">
        select count(1)
        from site_info site
        where enable_flag = 1
    </select>

    <!--  线路和车站获取监控主机个数 -->
    <select id="deviceCountByLineIdAndStationId" parameterType="map" resultType="int">
        select count(1)
        from belong_unit unit
        where enable_flag = 1
          and unit.line_id = #{param.lineId}
          and unit.station_id = #{param.stationId}
    </select>

    <!-- 根据车站编号获取天线个数 -->
    <select id="antCountByStationId" parameterType="string" resultType="int">
        select count(1)
        from sf_topo_node node
        where node.type in ('10', '1001', '1002', '1003', '1004', '1005')
          and station_id = #{stationId}
    </select>

    <!-- 根据车站编号统计探针个数 -->
    <select id="probeCountByStationId" parameterType="string" resultType="int">
        select count(1)
        from sf_probe ant
        where enable_flag = 1
          and ant.host_number in
              (select unit.host_num from belong_unit unit where unit.station_id = #{stationId} and unit.enable_flag = 1)
    </select>


</mapper>