<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.key.win.system.core.dao.SysRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.key.win.system.core.model.SysRole">
        <id column="id" property="id"/>
        <result column="role_name" property="roleName"/>
        <result column="role_description" property="roleDescription"/>
        <result column="role_code" property="roleCode"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , role_name, role_description, role_code
    </sql>

    <select id="permissionsByRole" resultType="com.key.win.system.core.vo.PermissionVO">
        select *
        from sys_role_permission_vo
        where role_id = #{roleId}
    </select>

    <select id="rolesByUserId" resultType="com.key.win.system.core.vo.RoleVO">
        select *
        from sys_user_role_vo
        where user_id = #{userId}
    </select>

    <!-- 根据roleCode获取菜单列表 	 -->
    <select id="resourcesByRoleId" resultType="com.key.win.system.core.vo.RoleRequestPathVO">
        select *
        from sys_role_requestPath_vo
        where role_id = #{roleId}
    </select>

    <!-- 	根据请求的路径RequestPath获取角色列表 	 -->
    <select id="rolesByRequestPath" resultType="com.key.win.system.core.vo.RequestPathVO">
        select *
        from sys_role_requestPath_vo
        where url = #{requestPath}
    </select>

    <!-- 根据角色列表查询所有角色下的可访问菜单资源 	 -->
    <select id="resourcesByRoleIdList" resultType="com.key.win.system.core.model.SysRequestPath">
        SELECT
        rp.id,
        rp.parent_id,
        rp.parent_name,
        rp.url,
        rp.request_path_name,
        rp.sort,
        rp.description,
        rp.type,
        rp.icon_class_name
        FROM
        sys_role_request_path_relation rrpr
        INNER JOIN sys_role r ON rrpr.role_id = r.id
        INNER JOIN sys_request_path rp ON rp.id = rrpr.request_path_id
        WHERE
        1=1

        <if test="roleIds!=null and roleIds.size()>0">
            and
            r.role_code in
            <foreach item="roleId" separator="," open="(" close=")" collection="roleIds">
                #{roleId}
            </foreach>
        </if>
        <if test="roleIds == null or roleIds.size() == 0">
            and
            r.role_code = '999999999999999999999999'
        </if>
        AND rp.type &lt; 3
        AND rp.`enable` = 1
        and rp.parent_id != -1
        GROUP BY
        rp.id,
        rp.parent_id,
        rp.parent_name,
        rp.url,
        rp.request_path_name,
        rp.sort,
        rp.description,
        rp.type,
        rp.icon_class_name
        ORDER BY
        sort
    </select>

</mapper>
