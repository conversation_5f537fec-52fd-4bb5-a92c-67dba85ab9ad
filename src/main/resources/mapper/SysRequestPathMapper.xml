<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.key.win.system.core.dao.SysRequestPathMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.key.win.system.core.model.SysRequestPath">
        <id column="id" property="id"/>
        <result column="url" property="url"/>
        <result column="description" property="description"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , url, description
    </sql>

</mapper>
