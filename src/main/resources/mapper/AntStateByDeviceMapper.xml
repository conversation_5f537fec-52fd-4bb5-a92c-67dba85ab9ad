<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.key.win.biz.dashboard.statistic.dao.NodeAntStateByDeviceDao">
    <select id="queryAntByDeviceId" resultType="com.key.win.biz.dashboard.statistic.vo.NodeAntInfoVo"
            parameterType="String">
        SELECT n.id,
               n.host_num,
               n.line_id,
               n.line_name,
               n.station_id,
               n.station_name,
               n.`name`,
               n.probe_sid,
               n.`type`,
               t.type_name,
               n.flag_true
        FROM `sf_topo_node` n
                 left join sf_device_type t
                           on n.type = t.type_id
        where type in (10, 1001, 1002, 1003, 1004, 1005)
          and host_num = #{deviceId}
    </select>

</mapper>