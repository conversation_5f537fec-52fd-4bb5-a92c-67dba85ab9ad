<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.key.win.biz.baseInfo.dao.BelongUnitDao">
    <select id="isBanByLineIdOrStationId" parameterType="string" resultType="int">
        SELECT count(1) as count
        FROM belong_unit unit
        WHERE unit.station_id IN (SELECT s.station_id
            FROM base_line l
            INNER JOIN base_station s ON l.line_id = s.line_id
            WHERE l.line_operation_state = 1
          AND s.station_type = 1)
          AND unit.enable_flag = 1
          AND unit.host_num = #{deviceId}
    </select>
</mapper>