<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.key.win.system.core.dao.SysUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.key.win.system.core.model.SysUser">
        <id column="id" property="id"/>
        <result column="account" property="account"/>
        <result column="user_name" property="userName"/>
        <result column="password" property="password"/>
        <result column="last_login_time" property="lastLoginTime"/>
        <result column="enabled" property="enabled"/>
        <result column="not_expired" property="notExpired"/>
        <result column="account_not_locked" property="accountNotLocked"/>
        <result column="credentials_not_expired" property="credentialsNotExpired"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , account, user_name, password, last_login_time, enabled, not_expired, account_not_locked, credentials_not_expired, create_time, update_time, create_user, update_user
    </sql>


    <select id="getUserInfoByAccount" resultType="com.key.win.system.core.dto.UserAndRole">
        select su.user_name, sr.role_code, sr.role_name
        from sys_user as su
                 inner join sys_user_role_relation as surl
                            on su.id = surl.user_id
                 inner join sys_role as sr
                            on surl.role_id = sr.id
        where su.account = #{memberAccountByJwtToken}
    </select>


</mapper>
