<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.key.win.system.core.dao.SysRequestPathPermissionRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.key.win.system.core.model.SysRequestPathPermissionRelation">
        <result column="id" property="id"/>
        <result column="url_id" property="urlId"/>
        <result column="permission_id" property="permissionId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , url_id, permission_id
    </sql>

</mapper>
