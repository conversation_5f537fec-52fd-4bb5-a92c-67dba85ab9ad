<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.key.win.biz.dashboard.statistic.dao.DeviceStateDao">
    <select id="queryDeviceListSort" resultType="com.key.win.biz.dashboard.statistic.vo.DeviceInfoVo">
        SELECT id,
               host_num,
               host_type,
               pid,
               line_id,
               line_name,
               station_id,
               station_name,
               device_status
        FROM belong_unit unit
        WHERE unit.station_id IN (SELECT s.station_id
                                  FROM base_line l
                                           INNER JOIN base_station s ON l.line_id = s.line_id
                                  WHERE l.line_operation_state = 1
                                    AND s.station_type = 1)
          AND enable_flag = 1
        ORDER BY line_id ASC,
                 station_id ASC;
    </select>
    <select id="queryDeviceListSortByStationId" resultType="com.key.win.biz.dashboard.statistic.vo.DeviceInfoVo"
            parameterType="String">
        select id,
               host_num,
               host_type,
               pid,
               line_id,
               line_name,
               station_id,
               station_name,
               device_status
        from belong_unit
        where station_id = #{stationId}
          and enable_flag = 1
        order by line_id asc, station_id asc
    </select>
    <select id="queryDeviceListById" resultType="com.key.win.biz.dashboard.statistic.vo.DeviceInfoVo"
            parameterType="String">
        select id,
               host_num,
               host_type,
               pid,
               line_id,
               line_name,
               station_id,
               station_name,
               device_status
        from belong_unit
        where host_num = #{deviceId}
          and enable_flag = 1
        order by line_id asc, station_id asc
    </select>
</mapper>