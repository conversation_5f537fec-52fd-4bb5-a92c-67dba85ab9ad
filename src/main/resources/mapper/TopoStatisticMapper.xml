<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.key.win.biz.statistic.dao.TopoStatisticDao">

    <select id="topoStatistic" resultType="com.key.win.biz.statistic.vo.StatisticVO">
        SELECT IFNULL(t.count, 0) as count,
			t1.type_name,
			t1.type_id
        FROM
            (
            SELECT
            dt.type_name,
            type,
            count ( type ) AS count,
            dt.sort
            FROM
            sf_topo_node node
            RIGHT JOIN sf_device_type dt ON node.type = dt.type_id
            WHERE
            station_id = #{stationId}
            GROUP BY
            type,
            dt.type_name,
            dt.sort
            ) t
            RIGHT JOIN sf_device_type t1
        ON t1.type_id = t.type
        ORDER BY
            t1.sort ASC
    </select>


    <select id="allStationStatistic" resultType="com.key.win.biz.statistic.vo.AllStationStatisticVO">
        SELECT
            t1.line_id,
            t1.line_name,
            t1.station_id,
            t1.station_name,
            t1.type_id,
            t1.type_name,
            IFNULL( t.count, 0 ) AS count
        FROM
            (
            SELECT
            node.station_id,
            node.station_name,
            node.type,
            count( node.type ) AS count
            FROM
            sf_topo_node node
            GROUP BY
            type,
            node.station_id,
            node.station_name
            ) t
            RIGHT JOIN sf_device_type_vo t1 ON t1.type_id = t.type
            AND t1.station_id = t.station_id
        ORDER BY
            t1.line_id,
            t1.station_id,
            t1.sort
    </select>


</mapper>