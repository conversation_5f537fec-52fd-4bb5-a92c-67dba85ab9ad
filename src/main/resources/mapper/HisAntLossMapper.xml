<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.key.win.biz.alarm.dao.HisAntLossDao">

    <select id="countHisLoss" resultType="java.lang.Integer">
        SELECT
           count(1) as num
        FROM (
        <include refid="selectSql" />
        <include refid="condition" />
        ) t
    </select>

    <!-- 分页分组查询历史线损查询 -->
    <select id="selectLatestHisLossByPage"  resultType="com.key.win.biz.alarm.model.HisAntLoss">
        <include refid="selectSql" />
        <include refid="condition" />
        LIMIT  #{pageNo}, #{pageSize}
    </select>

    <sql id="selectSql">
             SELECT
                t1.id,
                t1.ant_name as antName,
                t1.equipment_type as equipmentType,
                t2.count_value as countValue,
                t1.loss,
                t1.update_time,
                t1.belong_line_name as belongLineName,
                t1.belong_station_name as belongStationName,
                t1.probe_sid as probeSid,
                t1.host_number as hostNumber
            FROM his_ant_loss t1
            INNER JOIN ( SELECT t2.ant_name,
                                t2.host_number,
                                COUNT( * ) AS count_value,
                                max( t2.update_time ) AS update_time
                         FROM his_ant_loss t2
                         GROUP BY t2.ant_name, t2.host_number
                       ) t2 ON t1.ant_name = t2.ant_name AND t1.host_number = t2.host_number AND t1.update_time = t2.update_time
            where 1=1
    </sql>

    <sql id="condition">
        <if test="req.hostNumber != null and req.hostNumber != '' ">
            AND t1.host_number LIKE concat('%',#{req.hostNumber},'%')
        </if>
        <if test="req.antName != null and req.antName != '' ">
            and t1.ant_name = #{req.antName}
        </if>
        <if test="req.belongStationName != null and req.belongStationName != '' ">
            AND t1.belong_station_name LIKE concat('%',#{req.belongStationName},'%')
        </if>
        <if test="req.startTime != null and req.startTime != '' ">
            AND t1.update_time <![CDATA[ >= ]]> DATE_FORMAT( #{req.startTime}, '%Y%m%d' )
        </if>
        <if test="req.endTime != null and req.endTime != '' ">
            AND t1.update_time <![CDATA[ <= ]]> DATE_FORMAT(  #{req.endTime}, '%Y%m%d' )
        </if>
    </sql>



</mapper>