<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.key.win.biz.topo.dao.NodeLinkDao">

    <select id="getAntNodeLinks" resultType="int" parameterType="string">
        SELECT count(1) as count
        FROM sf_topo_node node
            INNER JOIN sf_probe probe
        ON (node.probe_sid = probe.number)
        WHERE node.type IN (10
            , 1001
            , 1002
            , 1003
            , 1004
            , 1005)
          AND probe.probe_status = '00'
          and node.host_num = #{hostNumber}
    </select>

</mapper>