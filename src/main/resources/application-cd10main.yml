## 成都30号线 主中心

spring:
  main:
    banner-mode: off
  application:
    name: indoorSystem
  datasource:
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ************************************************************************************************************************************************************
      username: root
      password: indoorSystem#123
      initialSize: 5
      minIdle: 10
      maxActive: 20
      maxWait: 86400
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      maxEvictableIdleTimeMillis: 900000
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        allow:
        url-pattern: /druid/*
        login-username: admin
        login-password: 123456
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 3000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  redis:
    database: 7
    host: 127.0.0.1
    port: 6379
    timeout: 1000
    lettuce:
      pool:
        max-wait: -1
        min-idle: 1
        max-active: 8
        max-idle: 8
  jpa:
    hibernate:
      ddl-auto: update

# 配置mapper xml的位置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  global-config:
    banner: false

sf:
  collection:
    probe:
      addWithoutDbData: true #天线数据如果没有匹配到数据库记录是否执行保存
  cmd:
    param:
      frequencyStep: false #是否开启[频率步进]查询,目前版本已经要求关闭该功能（如果开启请更改为true）
      frequencyStart: false #是否开启[开始频率]指令,对于地铁目前频率值是固定暂且关闭该选项(如果开启请更改位true)
      frequencyEnd: false #是否开启[结束频率]指令,对于地铁目前频率值是固定暂且关闭该选项(如果开启请更改位true)
    setting:
      faultDiagnosisCycle: false
      faultDiagnosisTime: false
      faultDiagnosisNumber: false
    probeRealRead:
      enable: true   # true 开启真实读取； false 关闭真实读取
  tel:
    enable: false
    type: main  # main/backup  和C#面板保持一致
    balanceNodeIp: 127.0.0.1  #配置负载另一个中心的室分jar服务的 ip地址 备中心的ip 125   主中心的 126
    balanceNodePort: 18081 #配置负载另一个中心的室分jar服务的 端口号
    systemCode: 0B
  alarm:
    report: true # true为实时上报  false为延迟上报，当延迟上报时候下方 delay配置生效
    delay: 1800 #时间单位 秒
    reportCount: 1  #在执行路损查询的时候，设备方路损上报的次数 1代机器是3次，2代是1次
  isTest: true
  super_account: debug #调试员账号（最高权限）

  brand: 天奕
  productName: 天奕室分监测系统-服务端程序
  version: cd10-v2.250819.1
  versionDesc: 成都10号线V2-构建时间(********.1)
  deviceVersionDesc: 设备Ⅱ代、设备Ⅰ代