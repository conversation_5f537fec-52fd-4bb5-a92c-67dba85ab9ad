-- SNMP配置表
CREATE TABLE IF NOT EXISTS `snmp_config` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` varchar(500) NOT NULL COMMENT '配置值',
  `description` varchar(200) DEFAULT NULL COMMENT '配置描述',
  `config_type` varchar(20) DEFAULT 'STRING' COMMENT '配置类型(IP,PORT,STRING,NUMBER)',
  `enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用(1:启用,0:禁用)',
  `sort_order` int(11) DEFAULT 1 COMMENT '排序',
  `enable_flag` tinyint(1) DEFAULT 1 COMMENT '删除标识(1:未删除,0:已删除)',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SNMP配置表';

-- 插入默认配置数据
INSERT INTO `snmp_config` (`id`, `config_key`, `config_value`, `description`, `config_type`, `enabled`, `sort_order`) VALUES
('snmp_config_001', 'snmp.client.ip', '*************', 'SNMP客户端IP地址', 'IP', 1, 1),
('snmp_config_002', 'snmp.client.port', '162', 'SNMP客户端端口号', 'PORT', 1, 2)
ON DUPLICATE KEY UPDATE 
  `config_value` = VALUES(`config_value`),
  `description` = VALUES(`description`),
  `config_type` = VALUES(`config_type`),
  `enabled` = VALUES(`enabled`),
  `sort_order` = VALUES(`sort_order`);
