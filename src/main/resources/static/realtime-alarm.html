<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>告警上报联调管理</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .section h3 {
            margin-top: 0;
            color: #2c3e50;
        }

        .button {
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        .button:hover {
            background-color: #2980b9;
        }

        .button.danger {
            background-color: #e74c3c;
        }

        .button.danger:hover {
            background-color: #c0392b;
        }

        .button.success {
            background-color: #27ae60;
        }

        .button.success:hover {
            background-color: #229954;
        }

        .alarm-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
        }

        .alarm-item {
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #eee;
            border-radius: 4px;
            background-color: #f9f9f9;
        }

        .alarm-item.critical {
            border-left: 4px solid #e74c3c;
            background-color: #fdf2f2;
        }

        .alarm-item.important {
            border-left: 4px solid #f39c12;
            background-color: #fef9e7;
        }

        .alarm-item.normal {
            border-left: 4px solid #3498db;
            background-color: #f0f8ff;
        }

        .alarm-item.info {
            border-left: 4px solid #95a5a6;
            background-color: #f8f9fa;
        }

        .statistics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }

        .stat-label {
            color: #7f8c8d;
            margin-top: 5px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        <h1>综合网管告警联调面板</h1>
    </div>


    <div class="section">
        <h3>告警操作区域</h3>
        <button class="button" onclick="sendMockTraps()">批量发送告警Trap</button>
        <button class="button" onclick="sendMockTraps2()">单条发送告警Trap</button>
        <button class="button" onclick="sendAsyncTraps()">主动告警同步Trap</button>
    </div>

    <div class="section">
        <h3>当前本系统告警列表</h3>
        <div class="statistics" id="statistics">
            <!-- 统计信息将在这里显示 -->
        </div>
            <div class="alarm-list" id="alarmList"></div>
    </div>

    <script>
        // 页面加载时获取统计数据
        window.onload = function () {
            loadAllAlarms();
        };

        // 加载所有告警
        function loadAllAlarms() {
            fetch('/api/realtime-alarm/all')
                .then(response => response.json())
                .then(alarms => {
                    const alarmListDiv = document.getElementById('alarmList');
                    if (alarms.length === 0) {
                        alarmListDiv.innerHTML = '<p>暂无告警</p>';
                        return;
                    }

                    alarmListDiv.innerHTML = alarms.map(alarm => {
                        const levelClass = getLevelClass(alarm.alarmSeverity);
                        const statusText = getStatusText(alarm.alarmStatus);
                        const levelText = getLevelText(alarm.alarmSeverity);
                        const typeText = getTypeText(alarm.alarmProperty);

                        return `
                            <div class="alarm-item ${levelClass}">
                                <strong>${alarm.alarmAID}</strong> - ${alarm.alarmAlmText}<br>
                                <small>
                                    级别: ${levelText} | 类型: ${typeText} | 状态: ${statusText}<br>
                                    设备: ${alarm.alarmModule1} (${alarm.alarmEquipID})<br>
                                    位置: ${alarm.alarmFromShelf} | 时间: ${alarm.alarmDateTime}<br>
                                </small>
                                <div style="margin-top: 10px;">
                                    ${alarm.alarmStatus === 1 ?
                            `<button class="button success" onclick="confirmAlarm(${alarm.alarmAID})">确认</button>` : ''}
                                    <button class="button danger" onclick="clearAlarm(${alarm.alarmAID})">清除</button>
                                </div>
                            </div>
                        `;
                    }).join('');
                })
                .catch(error => console.error('获取告警列表失败:', error));
        }

        // 获取级别对应的CSS类
        function getLevelClass(level) {
            switch (level) {
                case 1:
                    return 'critical';
                case 2:
                    return 'important';
                case 3:
                    return 'normal';
                case 4:
                    return 'info';
                default:
                    return 'normal';
            }
        }

        // 获取级别文本
        function getLevelText(level) {
            switch (level) {
                case 1:
                    return '严重';
                case 2:
                    return '重要';
                case 3:
                    return '一般';
                case 4:
                    return '提示';
                default:
                    return '未知';
            }
        }

        // 获取类型文本
        function getTypeText(type) {
            switch (type) {
                case 1:
                    return '设备故障';
                case 2:
                    return '性能告警';
                case 3:
                    return '安全告警';
                case 4:
                    return '其他';
                default:
                    return '未知';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch (status) {
                case 1:
                    return '未确认';
                case 2:
                    return '已确认';
                case 3:
                    return '已清除';
                default:
                    return '未知';
            }
        }

        // 发送模拟告警Trap
        function sendMockTraps() {
            fetch('/api/realtime-alarm/send-mock-traps', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    alert(data.message);
                    if (data.success) {
                        loadStatistics();
                        loadAllAlarms();
                    }
                })
                .catch(error => console.error('发送模拟告警Trap失败:', error));
        }

        // 发送模拟告警Trap-单条
        function sendMockTraps2() {
            fetch('/api/realtime-alarm/send-mock-traps2?id=1957716412684574721', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    alert(data.message);
                    if (data.success) {
                        loadStatistics();
                        loadAllAlarms();
                    }
                })
                .catch(error => console.error('发送模拟告警Trap失败:', error));
        }

        // 主动申请告警同步trap
        function sendAsyncTraps() {
            fetch('/api/realtime-alarm/send-async-traps', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    alert(data.message);
                    if (data.success) {
                        loadStatistics();
                        loadAllAlarms();
                    }
                })
                .catch(error => console.error('申请告警同步Trap失败:', error));
        }
    </script>
</body>
</html>