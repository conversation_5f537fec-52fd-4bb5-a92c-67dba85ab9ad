<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>告警上报联调管理</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1800px;
            width: 95%;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .section h3 {
            margin-top: 0;
            color: #2c3e50;
        }

        .button {
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        .button.small {
            padding: 2px 4px;
            font-size: 10px;
            margin: 1px;
            min-width: 45px;
            white-space: nowrap;
        }

        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .action-row {
            display: flex;
            gap: 2px;
            justify-content: center;
        }

        .button:hover {
            background-color: #2980b9;
        }

        .button.danger {
            background-color: #e74c3c;
        }

        .button.danger:hover {
            background-color: #c0392b;
        }

        .button.success {
            background-color: #27ae60;
        }

        .button.success:hover {
            background-color: #229954;
        }

        .alarm-table-container {
            max-height: 700px;
            overflow-y: auto;
            overflow-x: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-top: 15px;
        }

        .alarm-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
            table-layout: fixed;
        }

        .alarm-table th,
        .alarm-table td {
            padding: 6px 8px;
            text-align: left;
            border-bottom: 1px solid #eee;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            min-width: 80px;
        }

        /* 列宽设置 - 针对1920*1080优化 */
        .alarm-table th:nth-child(1),  /* 复选框 */
        .alarm-table td:nth-child(1) {
            width: 3%;
        }

        .alarm-table th:nth-child(2),  /* 告警ID */
        .alarm-table td:nth-child(2) {
            width: 8%;
        }

        .alarm-table th:nth-child(3),  /* 车站名称 */
        .alarm-table td:nth-child(3) {
            width: 8%;
        }

        .alarm-table th:nth-child(4),  /* 告警名称 */
        .alarm-table td:nth-child(4) {
            width: 15%;
        }

        .alarm-table th:nth-child(5),  /* 网元名称 */
        .alarm-table td:nth-child(5) {
            width: 8%;
        }

        .alarm-table th:nth-child(6),  /* 监控主机编号 */
        .alarm-table td:nth-child(6) {
            width: 8%;
        }

        .alarm-table th:nth-child(7),  /* 设备类型 */
        .alarm-table td:nth-child(7) {
            width: 7%;
        }

        .alarm-table th:nth-child(8),  /* 告警等级 */
        .alarm-table td:nth-child(8) {
            width: 6%;
        }

        .alarm-table th:nth-child(9),  /* 告警类型 */
        .alarm-table td:nth-child(9) {
            width: 8%;
        }

        .alarm-table th:nth-child(10), /* 告警次数 */
        .alarm-table td:nth-child(10) {
            width: 6%;
        }

        .alarm-table th:nth-child(11), /* 告警时间 */
        .alarm-table td:nth-child(11) {
            width: 10%;
        }

        .alarm-table th:nth-child(12), /* 告警状态 */
        .alarm-table td:nth-child(12) {
            width: 6%;
        }

        .alarm-table th:nth-child(13), /* 操作 */
        .alarm-table td:nth-child(13) {
            width: 17%;
            min-width: 140px;
        }

        .alarm-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .alarm-table tbody tr:hover {
            background-color: #f5f5f5;
        }

        .alarm-table tbody tr.selected {
            background-color: #e3f2fd;
        }

        .alarm-grade-critical {
            color: #e74c3c;
            font-weight: bold;
        }

        .alarm-grade-important {
            color: #f39c12;
            font-weight: bold;
        }

        .alarm-grade-normal {
            color: #3498db;
        }

        .alarm-grade-info {
            color: #95a5a6;
        }

        .alarm-status-0 {
            color: #e74c3c;
            font-weight: bold;
        }

        .alarm-status-1 {
            color: #f39c12;
        }

        .alarm-status-2 {
            color: #27ae60;
        }

        .table-controls {
            margin-bottom: 15px;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .selected-count {
            color: #666;
            font-size: 14px;
        }

        /* 针对1920*1080分辨率优化 */
        @media (min-width: 1800px) {
            .container {
                max-width: 1850px;
            }

            .alarm-table {
                font-size: 13px;
            }

            .alarm-table th,
            .alarm-table td {
                padding: 8px 10px;
            }

            .statistics {
                grid-template-columns: repeat(4, 1fr);
                gap: 20px;
            }

            .table-controls {
                margin-bottom: 20px;
                gap: 15px;
            }
        }

        .statistics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }

        .stat-label {
            color: #7f8c8d;
            margin-top: 5px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        <h1>综合网管告警联调面板</h1>
    </div>


    <div class="section">
        <h3>告警操作区域</h3>
        <button class="button" onclick="sendMockTraps()">批量发送告警Trap</button>
        <button class="button" onclick="sendAsyncTraps()">主动告警同步Trap</button>
    </div>

    <div class="section">
        <h3>当前本系统告警列表</h3>
        <div class="statistics" id="statistics">
            <!-- 统计信息将在这里显示 -->
        </div>

        <div class="table-controls">
            <button class="button" onclick="selectAll()">全选</button>
            <button class="button" onclick="selectNone()">取消全选</button>
            <button class="button success" onclick="confirmSelectedAlarms()">批量确认</button>
            <button class="button danger" onclick="clearSelectedAlarms()">批量清除</button>
            <span class="selected-count" id="selectedCount">已选择: 0 条</span>
        </div>

        <div class="alarm-table-container">
            <table class="alarm-table" id="alarmTable">
                <thead>
                    <tr>
                        <th><input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()"></th>
                        <th>告警ID</th>
                        <th>车站名称</th>
                        <th>告警名称</th>
                        <th>网元名称</th>
                        <th>监控主机编号</th>
                        <th>设备类型</th>
                        <th>告警等级</th>
                        <th>告警类型</th>
                        <th>告警次数</th>
                        <th>告警时间</th>
                        <th>告警状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="alarmTableBody">
                    <!-- 告警数据将在这里显示 -->
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // 页面加载时获取统计数据
        window.onload = function () {
            loadAllAlarms();
        };

        // 更新统计信息
        function updateStatistics(alarms) {
            const stats = {
                total: alarms.length,
                realtime: alarms.filter(alarm => alarm.alarmStatus === '0').length,
                confirmed: alarms.filter(alarm => alarm.alarmStatus === '1').length,
                cleared: alarms.filter(alarm => alarm.alarmStatus === '2').length
            };

            const statisticsDiv = document.getElementById('statistics');
            statisticsDiv.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${stats.total}</div>
                    <div class="stat-label">总告警数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #e74c3c;">${stats.realtime}</div>
                    <div class="stat-label">实时告警</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #f39c12;">${stats.confirmed}</div>
                    <div class="stat-label">已确认</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #27ae60;">${stats.cleared}</div>
                    <div class="stat-label">已清除</div>
                </div>
            `;
        }

        // 存储选中的告警ID
        let selectedAlarms = new Set();

        // 加载所有告警
        function loadAllAlarms() {
            fetch('/api/realtime-alarm/all')
                .then(response => response.json())
                .then(alarms => {
                    const tableBody = document.getElementById('alarmTableBody');
                    if (alarms.length === 0) {
                        tableBody.innerHTML = '<tr><td colspan="13" style="text-align: center;">暂无告警</td></tr>';
                        return;
                    }

                    tableBody.innerHTML = alarms.map(alarm => {
                        const gradeClass = getGradeClass(alarm.alarmGrade);
                        const statusClass = getStatusClass(alarm.alarmStatus);
                        const statusText = getStatusText(alarm.alarmStatus);
                        const gradeText = getGradeText(alarm.alarmGrade);

                        return `
                            <tr onclick="toggleRowSelection('${alarm.id}', event)">
                                <td><input type="checkbox" id="checkbox_${alarm.id}" onchange="toggleAlarmSelection('${alarm.id}')"></td>
                                <td title="${alarm.id}">${alarm.id}</td>
                                <td title="${alarm.belongStationName || ''}">${alarm.belongStationName || ''}</td>
                                <td title="${alarm.alarmName || ''}">${alarm.alarmName || ''}</td>
                                <td title="${alarm.networkName || ''}">${alarm.networkName || ''}</td>
                                <td title="${alarm.hostNumber || ''}">${alarm.hostNumber || ''}</td>
                                <td title="${alarm.equipmentType || ''}">${alarm.equipmentType || ''}</td>
                                <td class="${gradeClass}" title="${gradeText}">${gradeText}</td>
                                <td title="${alarm.alarmType || ''}">${alarm.alarmType || ''}</td>
                                <td>${alarm.alarmTimes || 0}</td>
                                <td title="${alarm.alarmTime || ''}">${alarm.alarmTime || ''}</td>
                                <td class="${statusClass}" title="${statusText}">${statusText}</td>
                                <td>
                                    <div class="action-buttons">
                                        <div class="action-row">
                                            <button class="button small" onclick="sendSingleAlarmTrap('${alarm.id}', event)" title="发送告警Trap">告警Trap</button>
                                            <button class="button small" onclick="sendSingleRecoveryTrap('${alarm.id}', event)" title="发送恢复Trap" style="background-color: #27ae60;">恢复Trap</button>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        `;
                    }).join('');

                    // 重置选择状态
                    selectedAlarms.clear();
                    updateSelectedCount();
                    document.getElementById('selectAllCheckbox').checked = false;

                    // 更新统计信息
                    updateStatistics(alarms);
                })
                .catch(error => console.error('获取告警列表失败:', error));
        }

        // 获取告警等级对应的CSS类
        function getGradeClass(grade) {
            switch (grade) {
                case '严重':
                case '1':
                    return 'alarm-grade-critical';
                case '重要':
                case '2':
                    return 'alarm-grade-important';
                case '一般':
                case '3':
                    return 'alarm-grade-normal';
                case '提示':
                case '4':
                    return 'alarm-grade-info';
                default:
                    return 'alarm-grade-normal';
            }
        }

        // 获取告警等级文本
        function getGradeText(grade) {
            if (!grade) return '未知';
            return grade;
        }

        // 获取状态对应的CSS类
        function getStatusClass(status) {
            switch (status) {
                case '0':
                    return 'alarm-status-0';
                case '1':
                    return 'alarm-status-1';
                case '2':
                    return 'alarm-status-2';
                default:
                    return '';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch (status) {
                case '0':
                    return '实时告警';
                case '1':
                    return '已确认';
                case '2':
                    return '已清除';
                default:
                    return '未知';
            }
        }

        // 表格选择相关函数
        function toggleAlarmSelection(alarmId) {
            const checkbox = document.getElementById(`checkbox_${alarmId}`);
            const row = checkbox.closest('tr');

            if (checkbox.checked) {
                selectedAlarms.add(alarmId);
                row.classList.add('selected');
            } else {
                selectedAlarms.delete(alarmId);
                row.classList.remove('selected');
            }

            updateSelectedCount();
            updateSelectAllCheckbox();
        }

        function toggleRowSelection(alarmId, event) {
            // 如果点击的是复选框或按钮，不处理行选择
            if (event.target.type === 'checkbox' || event.target.tagName === 'BUTTON') {
                return;
            }

            const checkbox = document.getElementById(`checkbox_${alarmId}`);
            checkbox.checked = !checkbox.checked;
            toggleAlarmSelection(alarmId);
        }

        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            const checkboxes = document.querySelectorAll('#alarmTableBody input[type="checkbox"]');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
                const alarmId = checkbox.id.replace('checkbox_', '');
                const row = checkbox.closest('tr');

                if (selectAllCheckbox.checked) {
                    selectedAlarms.add(alarmId);
                    row.classList.add('selected');
                } else {
                    selectedAlarms.delete(alarmId);
                    row.classList.remove('selected');
                }
            });

            updateSelectedCount();
        }

        function selectAll() {
            document.getElementById('selectAllCheckbox').checked = true;
            toggleSelectAll();
        }

        function selectNone() {
            document.getElementById('selectAllCheckbox').checked = false;
            toggleSelectAll();
        }

        function updateSelectedCount() {
            document.getElementById('selectedCount').textContent = `已选择: ${selectedAlarms.size} 条`;
        }

        function updateSelectAllCheckbox() {
            const checkboxes = document.querySelectorAll('#alarmTableBody input[type="checkbox"]');
            const checkedCheckboxes = document.querySelectorAll('#alarmTableBody input[type="checkbox"]:checked');
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');

            if (checkboxes.length === 0) {
                selectAllCheckbox.checked = false;
            } else if (checkedCheckboxes.length === checkboxes.length) {
                selectAllCheckbox.checked = true;
            } else {
                selectAllCheckbox.checked = false;
            }
        }
        // 告警操作函数
        function confirmAlarm(alarmId, event) {
            if (event) event.stopPropagation();

            if (!confirm('确认要确认这条告警吗？')) {
                return;
            }

            fetch(`/real/time/warn/makeSuerById/${alarmId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('确认告警成功');
                    loadAllAlarms(); // 重新加载告警列表
                } else {
                    alert('确认告警失败: ' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                console.error('确认告警失败:', error);
                alert('确认告警失败: 网络错误');
            });
        }

        function clearAlarm(alarmId, event) {
            if (event) event.stopPropagation();

            if (!confirm('确认要清除这条告警吗？')) {
                return;
            }

            fetch(`/real/time/warn/clearWarnById/${alarmId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('清除告警成功');
                    loadAllAlarms(); // 重新加载告警列表
                } else {
                    alert('清除告警失败: ' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                console.error('清除告警失败:', error);
                alert('清除告警失败: 网络错误');
            });
        }

        function confirmSelectedAlarms() {
            if (selectedAlarms.size === 0) {
                alert('请先选择要确认的告警');
                return;
            }

            if (!confirm(`确认要确认选中的 ${selectedAlarms.size} 条告警吗？`)) {
                return;
            }

            // 批量确认告警
            const promises = Array.from(selectedAlarms).map(alarmId =>
                fetch(`/real/time/warn/makeSuerById/${alarmId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }).then(response => response.json())
            );

            Promise.all(promises)
                .then(results => {
                    const successCount = results.filter(result => result.success).length;
                    const failCount = results.length - successCount;

                    if (failCount === 0) {
                        alert(`批量确认成功，共确认 ${successCount} 条告警`);
                    } else {
                        alert(`批量确认完成，成功 ${successCount} 条，失败 ${failCount} 条`);
                    }

                    loadAllAlarms(); // 重新加载告警列表
                })
                .catch(error => {
                    console.error('批量确认告警失败:', error);
                    alert('批量确认告警失败: 网络错误');
                });
        }

        function clearSelectedAlarms() {
            if (selectedAlarms.size === 0) {
                alert('请先选择要清除的告警');
                return;
            }

            if (!confirm(`确认要清除选中的 ${selectedAlarms.size} 条告警吗？`)) {
                return;
            }

            // 批量清除告警
            const promises = Array.from(selectedAlarms).map(alarmId =>
                fetch(`/real/time/warn/clearWarnById/${alarmId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }).then(response => response.json())
            );

            Promise.all(promises)
                .then(results => {
                    const successCount = results.filter(result => result.success).length;
                    const failCount = results.length - successCount;

                    if (failCount === 0) {
                        alert(`批量清除成功，共清除 ${successCount} 条告警`);
                    } else {
                        alert(`批量清除完成，成功 ${successCount} 条，失败 ${failCount} 条`);
                    }

                    loadAllAlarms(); // 重新加载告警列表
                })
                .catch(error => {
                    console.error('批量清除告警失败:', error);
                    alert('批量清除告警失败: 网络错误');
                });
        }

        // 发送模拟告警Trap
        function sendMockTraps() {
            fetch('/api/realtime-alarm/send-mock-traps', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    alert(data.message);
                    if (data.success) {
                        loadAllAlarms();
                    }
                })
                .catch(error => console.error('发送模拟告警Trap失败:', error));
        }

        // 发送单条告警Trap
        function sendSingleAlarmTrap(alarmId, event) {
            if (event) event.stopPropagation();

            if (!confirm(`确认要发送告警(ID: ${alarmId})的Trap吗？`)) {
                return;
            }

            fetch(`/api/realtime-alarm/send-mock-traps2?id=${alarmId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('发送告警Trap成功: ' + (data.message || ''));
                    loadAllAlarms();
                } else {
                    alert('发送告警Trap失败: ' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                console.error('发送告警Trap失败:', error);
                alert('发送告警Trap失败: 网络错误');
            });
        }

        // 发送单条恢复告警Trap
        function sendSingleRecoveryTrap(alarmId, event) {
            if (event) event.stopPropagation();

            if (!confirm(`确认要发送告警(ID: ${alarmId})的恢复Trap吗？`)) {
                return;
            }

            fetch(`/api/realtime-alarm/send-Recovery-traps?id=${alarmId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('发送恢复告警Trap成功: ' + (data.message || ''));
                    loadAllAlarms();
                } else {
                    alert('发送恢复告警Trap失败: ' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                console.error('发送恢复告警Trap失败:', error);
                alert('发送恢复告警Trap失败: 网络错误');
            });
        }

        // 主动申请告警同步trap
        function sendAsyncTraps() {
            fetch('/api/realtime-alarm/send-async-traps', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    alert(data.message);
                    if (data.success) {
                        loadAllAlarms();
                    }
                })
                .catch(error => console.error('申请告警同步Trap失败:', error));
        }
    </script>
</body>
</html>