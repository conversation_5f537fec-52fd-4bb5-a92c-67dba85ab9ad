<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SNMP配置管理</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1400px;
            width: 95%;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .section h3 {
            margin-top: 0;
            color: #2c3e50;
        }

        .button {
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        .button:hover {
            background-color: #2980b9;
        }

        .button.success {
            background-color: #27ae60;
        }

        .button.success:hover {
            background-color: #229954;
        }

        .button.danger {
            background-color: #e74c3c;
        }

        .button.danger:hover {
            background-color: #c0392b;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .config-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .config-table th,
        .config-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .config-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .config-table tbody tr:hover {
            background-color: #f5f5f5;
        }

        .current-config {
            background-color: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .current-config h4 {
            margin-top: 0;
            color: #27ae60;
        }

        .config-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .config-value {
            font-weight: bold;
            color: #2c3e50;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: black;
        }

        .status-enabled {
            color: #27ae60;
            font-weight: bold;
        }

        .status-disabled {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        <h1>SNMP配置管理</h1>
    </div>

    <!-- 当前生效配置 -->
    <div class="section">
        <h3>当前生效配置</h3>
        <div class="current-config" id="currentConfig">
            <!-- 当前配置信息将在这里显示 -->
        </div>
        <button class="button" onclick="refreshCurrentConfig()">刷新当前配置</button>
        <button class="button success" onclick="testConnection()">测试连接</button>
    </div>

    <!-- 快速配置 -->
    <div class="section">
        <h3>快速配置</h3>
        <div class="form-row">
            <div class="form-group">
                <label for="quickIp">SNMP客户端IP:</label>
                <input type="text" id="quickIp" placeholder="例如: *************">
            </div>
            <div class="form-group">
                <label for="quickPort">SNMP客户端端口:</label>
                <input type="number" id="quickPort" placeholder="例如: 162">
            </div>
        </div>
        <button class="button success" onclick="quickUpdate()">快速更新</button>
    </div>

    <!-- 配置管理 -->
    <div class="section">
        <h3>配置管理</h3>
        <button class="button" onclick="showAddModal()">添加配置</button>
        <button class="button" onclick="loadConfigs()">刷新列表</button>
        
        <table class="config-table" id="configTable">
            <thead>
                <tr>
                    <th>配置键</th>
                    <th>配置值</th>
                    <th>描述</th>
                    <th>类型</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="configTableBody">
                <!-- 配置数据将在这里显示 -->
            </tbody>
        </table>
    </div>
</div>

<!-- 添加/编辑配置模态框 -->
<div id="configModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeModal()">&times;</span>
        <h3 id="modalTitle">添加配置</h3>
        <form id="configForm">
            <input type="hidden" id="configId">
            <div class="form-group">
                <label for="configKey">配置键:</label>
                <input type="text" id="configKey" required>
            </div>
            <div class="form-group">
                <label for="configValue">配置值:</label>
                <input type="text" id="configValue" required>
            </div>
            <div class="form-group">
                <label for="description">描述:</label>
                <input type="text" id="description">
            </div>
            <div class="form-group">
                <label for="configType">类型:</label>
                <select id="configType">
                    <option value="STRING">字符串</option>
                    <option value="NUMBER">数字</option>
                    <option value="IP">IP地址</option>
                    <option value="PORT">端口号</option>
                </select>
            </div>
            <div class="form-group">
                <label for="enabled">启用状态:</label>
                <select id="enabled">
                    <option value="true">启用</option>
                    <option value="false">禁用</option>
                </select>
            </div>
            <div class="form-group">
                <label for="sortOrder">排序:</label>
                <input type="number" id="sortOrder" value="1">
            </div>
            <button type="submit" class="button success">保存</button>
            <button type="button" class="button" onclick="closeModal()">取消</button>
        </form>
    </div>
</div>

<script>
    // 页面加载时初始化
    window.onload = function() {
        refreshCurrentConfig();
        loadConfigs();
    };

    // 刷新当前生效配置
    function refreshCurrentConfig() {
        fetch('/api/snmp-config/current')
            .then(response => response.json())
            .then(data => {
                console.log(data)
                if (data.code === 0) {
                    const config = data.data;
                    document.getElementById('currentConfig').innerHTML = `
                        <h4>当前生效配置</h4>
                        <div class="config-item">
                            <span>SNMP客户端IP:</span>
                            <span class="config-value">${config.snmpClientIp}</span>
                        </div>
                        <div class="config-item">
                            <span>SNMP客户端端口:</span>
                            <span class="config-value">${config.snmpClientPort}</span>
                        </div>
                    `;
                    
                    // 同时更新快速配置的输入框
                    document.getElementById('quickIp').value = config.snmpClientIp;
                    document.getElementById('quickPort').value = config.snmpClientPort;
                }
            })
            .catch(error => console.error('获取当前配置失败:', error));
    }

    // 加载配置列表
    function loadConfigs() {
        const pageRequest = {
            pageNum: 1,
            pageSize: 100,
            data: {}
        };

        fetch('/api/snmp-config/page', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(pageRequest)
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 0) {
                console.log(data,"***********")
                const configs = data.data;
                const tableBody = document.getElementById('configTableBody');
                
                tableBody.innerHTML = configs.map(config => `
                    <tr>
                        <td>${config.configKey}</td>
                        <td>${config.configValue}</td>
                        <td>${config.description || ''}</td>
                        <td>${config.configType || ''}</td>
                        <td class="${config.enabled ? 'status-enabled' : 'status-disabled'}">
                            ${config.enabled ? '启用' : '禁用'}
                        </td>
                        <td>
                            <button class="button" onclick="editConfig('${config.id}')">编辑</button>
                            <button class="button danger" onclick="deleteConfig('${config.id}')">删除</button>
                        </td>
                    </tr>
                `).join('');
            }
        })
        .catch(error => console.error('加载配置列表失败:', error));
    }

    // 快速更新配置
    function quickUpdate() {
        const ip = document.getElementById('quickIp').value;
        const port = document.getElementById('quickPort').value;

        if (!ip || !port) {
            alert('请填写IP地址和端口号');
            return;
        }

        const configMap = {
            'snmp.client.ip': ip,
            'snmp.client.port': port
        };

        fetch('/api/snmp-config/batch-update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(configMap)
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 0) {
                alert('配置更新成功');
                refreshCurrentConfig();
                loadConfigs();
            } else {
                alert('配置更新失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('快速更新失败:', error);
            alert('配置更新失败: 网络错误');
        });
    }

    // 测试连接
    function testConnection() {
        const ip = document.getElementById('quickIp').value;
        const port = document.getElementById('quickPort').value;

        if (!ip || !port) {
            alert('请填写IP地址和端口号');
            return;
        }

        const testConfig = {
            ip: ip,
            port: port
        };

        fetch('/api/snmp-config/test-connection', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(testConfig)
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 0) {
                alert('连接测试成功');
            } else {
                alert('连接测试失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('连接测试失败:', error);
            alert('连接测试失败: 网络错误');
        });
    }

    // 显示添加配置模态框
    function showAddModal() {
        document.getElementById('modalTitle').textContent = '添加配置';
        document.getElementById('configForm').reset();
        document.getElementById('configId').value = '';
        document.getElementById('configModal').style.display = 'block';
    }

    // 编辑配置
    function editConfig(configId) {
        fetch(`/api/snmp-config/${configId}`)
            .then(response => response.json())
            .then(data => {
                if (data.code === 0) {
                    const config = data.data;
                    document.getElementById('modalTitle').textContent = '编辑配置';
                    document.getElementById('configId').value = config.id;
                    document.getElementById('configKey').value = config.configKey;
                    document.getElementById('configValue').value = config.configValue;
                    document.getElementById('description').value = config.description || '';
                    document.getElementById('configType').value = config.configType || 'STRING';
                    document.getElementById('enabled').value = config.enabled.toString();
                    document.getElementById('sortOrder').value = config.sortOrder || 1;
                    document.getElementById('configModal').style.display = 'block';
                } else {
                    alert('获取配置信息失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('获取配置信息失败:', error);
                alert('获取配置信息失败: 网络错误');
            });
    }

    // 删除配置
    function deleteConfig(configId) {
        if (!confirm('确认要删除这个配置吗？')) {
            return;
        }

        fetch(`/api/snmp-config/${configId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 0) {
                alert('删除成功');
                loadConfigs();
                refreshCurrentConfig();
            } else {
                alert('删除失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('删除配置失败:', error);
            alert('删除失败: 网络错误');
        });
    }

    // 关闭模态框
    function closeModal() {
        document.getElementById('configModal').style.display = 'none';
    }

    // 配置表单提交
    document.getElementById('configForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const configData = {
            id: document.getElementById('configId').value || null,
            configKey: document.getElementById('configKey').value,
            configValue: document.getElementById('configValue').value,
            description: document.getElementById('description').value,
            configType: document.getElementById('configType').value,
            enabled: document.getElementById('enabled').value === 'true',
            sortOrder: parseInt(document.getElementById('sortOrder').value) || 1
        };

        fetch('/api/snmp-config/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(configData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 0) {
                alert('保存成功');
                closeModal();
                loadConfigs();
                refreshCurrentConfig();
            } else {
                alert('保存失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('保存配置失败:', error);
            alert('保存失败: 网络错误');
        });
    });

    // 点击模态框外部关闭
    window.onclick = function(event) {
        const modal = document.getElementById('configModal');
        if (event.target === modal) {
            closeModal();
        }
    };
</script>
</body>
</html>
