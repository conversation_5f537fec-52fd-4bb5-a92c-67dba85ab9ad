package com.key.win.utils;

import com.key.win.mqtt.mq.statilFile.MqParamConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class ByteUtil {
  public static final char TABLE1021[] = {
    /* CRC1021余式 */
    0x0000, 0x1021, 0x2042, 0x3063, 0x4084, 0x50a5, 0x60c6, 0x70e7, 0x8108, 0x9129, 0xa14a, 0xb16b,
    0xc18c, 0xd1ad, 0xe1ce, 0xf1ef, 0x1231, 0x0210, 0x3273, 0x2252, 0x52b5, 0x4294, 0x72f7, 0x62d6,
    0x9339, 0x8318, 0xb37b, 0xa35a, 0xd3bd, 0xc39c, 0xf3ff, 0xe3de, 0x2462, 0x3443, 0x0420, 0x1401,
    0x64e6, 0x74c7, 0x44a4, 0x5485, 0xa56a, 0xb54b, 0x8528, 0x9509, 0xe5ee, 0xf5cf, 0xc5ac, 0xd58d,
    0x3653, 0x2672, 0x1611, 0x0630, 0x76d7, 0x66f6, 0x5695, 0x46b4, 0xb75b, 0xa77a, 0x9719, 0x8738,
    0xf7df, 0xe7fe, 0xd79d, 0xc7bc, 0x48c4, 0x58e5, 0x6886, 0x78a7, 0x0840, 0x1861, 0x2802, 0x3823,
    0xc9cc, 0xd9ed, 0xe98e, 0xf9af, 0x8948, 0x9969, 0xa90a, 0xb92b, 0x5af5, 0x4ad4, 0x7ab7, 0x6a96,
    0x1a71, 0x0a50, 0x3a33, 0x2a12, 0xdbfd, 0xcbdc, 0xfbbf, 0xeb9e, 0x9b79, 0x8b58, 0xbb3b, 0xab1a,
    0x6ca6, 0x7c87, 0x4ce4, 0x5cc5, 0x2c22, 0x3c03, 0x0c60, 0x1c41, 0xedae, 0xfd8f, 0xcdec, 0xddcd,
    0xad2a, 0xbd0b, 0x8d68, 0x9d49, 0x7e97, 0x6eb6, 0x5ed5, 0x4ef4, 0x3e13, 0x2e32, 0x1e51, 0x0e70,
    0xff9f, 0xefbe, 0xdfdd, 0xcffc, 0xbf1b, 0xaf3a, 0x9f59, 0x8f78, 0x9188, 0x81a9, 0xb1ca, 0xa1eb,
    0xd10c, 0xc12d, 0xf14e, 0xe16f, 0x1080, 0x00a1, 0x30c2, 0x20e3, 0x5004, 0x4025, 0x7046, 0x6067,
    0x83b9, 0x9398, 0xa3fb, 0xb3da, 0xc33d, 0xd31c, 0xe37f, 0xf35e, 0x02b1, 0x1290, 0x22f3, 0x32d2,
    0x4235, 0x5214, 0x6277, 0x7256, 0xb5ea, 0xa5cb, 0x95a8, 0x8589, 0xf56e, 0xe54f, 0xd52c, 0xc50d,
    0x34e2, 0x24c3, 0x14a0, 0x0481, 0x7466, 0x6447, 0x5424, 0x4405, 0xa7db, 0xb7fa, 0x8799, 0x97b8,
    0xe75f, 0xf77e, 0xc71d, 0xd73c, 0x26d3, 0x36f2, 0x0691, 0x16b0, 0x6657, 0x7676, 0x4615, 0x5634,
    0xd94c, 0xc96d, 0xf90e, 0xe92f, 0x99c8, 0x89e9, 0xb98a, 0xa9ab, 0x5844, 0x4865, 0x7806, 0x6827,
    0x18c0, 0x08e1, 0x3882, 0x28a3, 0xcb7d, 0xdb5c, 0xeb3f, 0xfb1e, 0x8bf9, 0x9bd8, 0xabbb, 0xbb9a,
    0x4a75, 0x5a54, 0x6a37, 0x7a16, 0x0af1, 0x1ad0, 0x2ab3, 0x3a92, 0xfd2e, 0xed0f, 0xdd6c, 0xcd4d,
    0xbdaa, 0xad8b, 0x9de8, 0x8dc9, 0x7c26, 0x6c07, 0x5c64, 0x4c45, 0x3ca2, 0x2c83, 0x1ce0, 0x0cc1,
    0xef1f, 0xff3e, 0xcf5d, 0xdf7c, 0xaf9b, 0xbfba, 0x8fd9, 0x9ff8, 0x6e17, 0x7e36, 0x4e55, 0x5e74,
    0x2e93, 0x3eb2, 0x0ed1, 0x1ef0
  };

  /**
   * @param buf byte[]
   * @return String
   */
  public static String toHexString(byte[] buf) {
    String hex = "";
    if (buf == null) return hex;
    for (int i = 0; i < buf.length; i++) {
      hex += toHexString(buf[i]);
    }
    return hex;
  }

  /**
   * Byte转Hex
   *
   * @param b byte
   * @return String
   */
  public static String toHexString(byte b) {
    String s = Integer.toHexString(b);
    if (s.length() == 1) {
      s = "0" + s;
    } else if (s.length() > 2) {
      s = s.substring(s.length() - 2);
    }
    return s;
  }

  /**
   * 十六进制数组转换字符
   *
   * @param src byte[]
   * @return String
   */
  public static String bytesToHexString(byte[] src) {
    StringBuilder stringBuilder = new StringBuilder("");
    if (src == null || src.length == 0) {
      return null;
    }
    for (int i = 0; i < src.length; i++) {
      int v = src[i] & 0xFF;
      String hv = Integer.toHexString(v);
      if (hv.length() < 2) {
        stringBuilder.append(0);
      }
      stringBuilder.append(hv);
    }
    return stringBuilder.toString();
  }

  /**
   * 转换为对应的16进制BCD编码
   * @param timeStr
   * @return
   */
  public static String convertToBcdHex(String timeStr) {
    StringBuilder bcdHex = new StringBuilder();

    for (int i = 0; i < timeStr.length(); i++) {
      // 将每个字符转换为BCD，即每个十进制数字转换为四位二进制数
      int digit = Character.getNumericValue(timeStr.charAt(i));
      // 将四位二进制转换为十六进制数
      bcdHex.append(Integer.toHexString(digit));
    }

    return bcdHex.toString();
  }

  /** 十进制转十六进制 */
  public static String decimalToHexadecimal(int number) {

    int i = 0;
    char[] S = new char[100];
    String result = "";
    if (number == 0) {
      result = "0";
    } else {
      while (number != 0) {
        int t = number % 16;
        if (t >= 0 && t < 10) {
          S[i] = (char) (t + '0');
          i++;
        } else {
          S[i] = (char) (t + 'A' - 10);
          i++;
        }
        number = number / 16;
      }

      for (int j = i - 1; j >= 0; j--) {
        result = result + S[j];
      }
    }
    return result;
  }

  /**
   * CRC16 校验 低位在左 高位在右
   *
   * @param crc16Data
   * @return
   */
  public static String crc(String crc16Data) {
    byte[] hexByte = hexStringToByteArray(crc16Data);
    int crc16_ccitt = crc16_ccitt(hexByte, hexByte.length);

    int big = (crc16_ccitt & 0xFF00) >> 8;
    int little = crc16_ccitt & 0xFF;

    String _big = String.format("%02x", big);
    String _little = String.format("%02x", little);

    return _little + _big;
  }

  /**
   * @param
   * @return
   */
  public static String reverseHex(final String hex) {
    final char[] charArray = hex.toCharArray();
    final int length = charArray.length;
    final int times = length / 2;
    for (int c1i = 0; c1i < times; c1i += 2) {
      final int c2i = c1i + 1;
      final char c1 = charArray[c1i];
      final char c2 = charArray[c2i];
      final int c3i = length - c1i - 2;
      final int c4i = length - c1i - 1;
      charArray[c1i] = charArray[c3i];
      charArray[c2i] = charArray[c4i];
      charArray[c3i] = c1;
      charArray[c4i] = c2;
    }
    return new String(charArray);
  }

  public static int crc16_ccitt(byte buf[], int len) {
    int counter;
    int crc = 0;
    for (counter = 0; counter < len; counter++) {
      crc = (crc << 8) ^ TABLE1021[((crc >> 8) ^ buf[counter]) & 0x00FF];
    }
    return crc & 0xFFFF;
  }

  public static byte[] hexStringToByteArray(String hexString) {
    if (StringUtils.isBlank(hexString)) {
      return null;
    }
    hexString = hexString.toUpperCase();
    hexString = hexString.replace(" ", "");
    int length = hexString.length() / 2;
    char[] hexChars = hexString.toCharArray();
    byte[] d = new byte[length];
    for (int i = 0; i < length; i++) {
      int pos = i * 2;
      d[i] = (byte) (charToByte(hexChars[pos]) << 4 | charToByte(hexChars[pos + 1]));
    }
    return d;
  }

  private static byte charToByte(char c) {
    return (byte) "0123456789ABCDEF".indexOf(c);
  }

  public static byte[] str2Bcd(String asc) {
    int len = asc.length();
    int mod = len % 2;

    if (mod != 0) {
      asc = "0" + asc;
      len = asc.length();
    }

    byte abt[] = new byte[len];
    if (len >= 2) {
      len = len / 2;
    }

    byte bbt[] = new byte[len];
    abt = asc.getBytes();
    int j, k;

    for (int p = 0; p < asc.length() / 2; p++) {
      if ((abt[2 * p] >= '0') && (abt[2 * p] <= '9')) {
        j = abt[2 * p] - '0';
      } else if ((abt[2 * p] >= 'a') && (abt[2 * p] <= 'z')) {
        j = abt[2 * p] - 'a' + 0x0a;
      } else {
        j = abt[2 * p] - 'A' + 0x0a;
      }

      if ((abt[2 * p + 1] >= '0') && (abt[2 * p + 1] <= '9')) {
        k = abt[2 * p + 1] - '0';
      } else if ((abt[2 * p + 1] >= 'a') && (abt[2 * p + 1] <= 'z')) {
        k = abt[2 * p + 1] - 'a' + 0x0a;
      } else {
        k = abt[2 * p + 1] - 'A' + 0x0a;
      }

      int a = (j << 4) + k;
      byte b = (byte) a;
      bbt[p] = b;
    }
    return bbt;
  }

  // 指令遇上5e5d和5e7e转义
  public static String escapeBefore(byte[] bytes) {
    String escapeCmd = "";
    for (byte b : bytes) {
      String _bHexValue = ByteUtil.toHexString(b);
      if (_bHexValue.toLowerCase().trim().equals("5e")) {
        _bHexValue = "5e5d";
      } else if (_bHexValue.toLowerCase().trim().equals("7e")) {
        _bHexValue = "5e7d";
      }
      escapeCmd += _bHexValue;
    }
    return escapeCmd;
  }

  public static String escapeAfter(byte[] data) {
    String dataString = "";
    for (int i = 0; i < data.length; i++) {
      if (ByteUtil.toHexString(data[i]).toLowerCase().equals("5e")) {
        if (i < data.length - 1) {
          if (ByteUtil.toHexString(data[i + 1]).toLowerCase().equals("5d")) {
            dataString += "5e";
            i++;
          } else if (ByteUtil.toHexString(data[i + 1]).toLowerCase().equals("7d")) {
            dataString += "7e";
            i++;
          }
          continue;
        }
      } else {
        dataString += ByteUtil.toHexString(data[i]);
      }
    }
    return dataString.toLowerCase();
  }

  /**
   * 16进制值转有符号的十进制数
   * 返回结果的单位是：dBm
   * @param hexString
   * @return
   */
  public static int hexToSignedDecimal(String hexString) {
      // 检查输入字符串长度是否为4（2个字节）
      if (hexString.length() != 4) {
       throw new IllegalArgumentException("十六进制字符串必须正好4个字符长。");
      }

      // 移除可能的"0x"前缀
      if (hexString.startsWith("0x") || hexString.startsWith("0X")) {
        hexString = hexString.substring(2);
      }

      // 将十六进制字符串转换为无符号的十进制整数
      int unsignedDecimal = Integer.parseInt(hexString, 16);

      // 检查最高位（符号位）
      if ((unsignedDecimal & 0x8000) != 0) {
        // 如果符号位为1，计算补码得到有符号的十进制负数
        return unsignedDecimal - 0x10000; // 从无符号整数中减去2^16来获得有符号的负数
      } else {
        // 如果符号位为0，直接返回无符号的十进制整数
        return unsignedDecimal;
      }
  }

  public static void main(String[] args) {
      String hexString = "fe70";
      int signedDecimal = hexToSignedDecimal(hexString);
      System.out.println("有符号的十进制数: " + signedDecimal);

    //    String antCode = "E20045EE1336CD4A5EC2E77E";
    //    antCode = antCode.replaceAll(" ", antCode);
    //
    //    String fullCmd =
    //        MqParamConfig.PARAM_CONFIG_ORDER
    //            + MqParamConfig.DOWN
    //            + MqParamConfig.SET_DEVICE_PROBE
    //            + MqParamConfig.ORDER_RESULT_FILL
    //            + "0d00"
    //            + "01"
    //            + antCode;
    //
    //    String deviceId = ByteUtil.reverseHex("88883333");
    //    fullCmd = deviceId + fullCmd;
    //    fullCmd = fullCmd.replace(" ", "");
    //
    //    String crc16 = ByteUtil.crc(fullCmd);
    //    String _cmd = fullCmd + crc16;
    //    byte[] bytes = ByteUtil.hexStringToByteArray(_cmd);
    //    String escapedStr = ByteUtil.escapeBefore(bytes);
    //    log.info(
    //        "监控主机:{},CRC：{},完整报文信息:{},转义后的的报文:{}",
    //        deviceId,
    //        crc16,
    //        fullCmd.toUpperCase(),
    //        escapedStr.toUpperCase());
    //    fullCmd = MqParamConfig.PREFIX_SUFFIX + escapedStr + MqParamConfig.PREFIX_SUFFIX;
    //    log.info("设备主机:{},完整的报文指令:{},CRC校验值:{}", deviceId, _cmd, escapedStr);
    //
    //    String ss = "33 33 88 88 04 01 01 01 0d 00 01 00 00 00 00 7e 5e aa bb cc dd 5e 7d  6ced";
    String ss = "33 33 88 88 02 01 12010e00 01 00 00 00 00 7e 5e aa bb cc dd 5e 7d 00 e9e7";
    //    String ss = "04000822020113010E0002000000007e5eaabbcc5e7d78543D";
    ss = ss.replace(" ", "");
    String s = ByteUtil.escapeBefore(ByteUtil.hexStringToByteArray(ss));
    System.out.println(s.toUpperCase());
  }
}
