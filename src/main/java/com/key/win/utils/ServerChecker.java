package com.key.win.utils;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;


public class ServerChecker {

    public static boolean isServerRunning(String ip, int port) {
        Socket socket = new Socket();
        try {
            // 设置连接超时时间为2秒
            socket.connect(new InetSocketAddress(ip, port), 2000);
            return true; // 连接成功
        } catch (IOException e) {
            return false; // 连接失败
        } finally {
            try {
                socket.close(); // 关闭socket
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}