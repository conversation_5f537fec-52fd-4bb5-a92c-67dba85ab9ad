package com.key.win.utils;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

/**
 * Json export or import.
 *
 * <AUTHOR>
 * @version 1.0
 */
public class JsonUtil {

  /**
   * readJson.
   *
   * @param multipartFile multipartFile
   * @return obj
   */
  public static Map<String, String> readJson(MultipartFile multipartFile) throws IOException {
    Map<String, String> result = new HashMap<>();
    String fileName = multipartFile.getOriginalFilename();
    File jsonFile = multipartFileToFile(multipartFile);
    String suffixName = fileName.substring(fileName.lastIndexOf("."));

    if (StringUtils.isBlank(suffixName) || !(suffixName.toLowerCase().trim().endsWith("json"))) {
      return null;
    }

    String jsonString = FileUtils.readFileToString(jsonFile, "UTF-8");
    Map map = JSONObject.parseObject(jsonString, Map.class);
    return map;
  }

  /**
   * exportJson.
   *
   * @param response response
   * @param obj obj
   * @param fileName fileName
   */
  public static void exportJson(HttpServletResponse response, Object obj, String fileName) {
    try {
      String jsonString =
          JSON.toJSONString(
              obj,
              SerializerFeature.PrettyFormat,
              SerializerFeature.WriteMapNullValue,
              SerializerFeature.WriteDateUseDateFormat);

      String rootPath = System.getProperty("user.dir");
      String fullPath = rootPath + "/sf-topo/";

      if (!FileUtil.exist(fullPath)) {
        FileUtil.mkdir(new File(fullPath));
      }
      String fileNameWithFullPath = fullPath + fileName;
      File file = new File(fileNameWithFullPath);

      Writer write = new OutputStreamWriter(new FileOutputStream(file), StandardCharsets.UTF_8);
      write.write(jsonString);
      write.flush();
      write.close();

      FileInputStream fis = new FileInputStream(file);
      response.setContentType("application/json");
      response.setHeader(
          "Content-Disposition",
          "attachment;filename=".concat(String.valueOf(URLEncoder.encode(fileName, "UTF-8"))));
      response.setCharacterEncoding("utf-8");

      OutputStream os = response.getOutputStream();
      byte[] buf = new byte[fis.available()];
      int len = 0;
      while ((len = fis.read(buf)) != -1) {
        os.write(buf, 0, len);
      }
      fis.close();
      os.close();
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  private static File multipartFileToFile(MultipartFile multipartFile) {
    File file = null;
    InputStream inputStream = null;
    OutputStream outputStream = null;
    try {
      inputStream = multipartFile.getInputStream();
      file = new File(multipartFile.getOriginalFilename());
      outputStream = new FileOutputStream(file);
      write(inputStream, outputStream);
    } catch (IOException e) {
      e.printStackTrace();
    } finally {
      if (inputStream != null) {
        try {
          inputStream.close();
        } catch (IOException e) {
          e.printStackTrace();
        }
      }
      if (outputStream != null) {
        try {
          outputStream.close();
        } catch (IOException e) {
          e.printStackTrace();
        }
      }
    }
    return file;
  }

  public static void write(InputStream inputStream, OutputStream outputStream) {
    byte[] buffer = new byte[4096];
    try {
      int count = inputStream.read(buffer, 0, buffer.length);
      while (count != -1) {
        outputStream.write(buffer, 0, count);
        count = inputStream.read(buffer, 0, buffer.length);
      }
    } catch (RuntimeException e) {
      throw e;
    } catch (Exception e) {
      throw new RuntimeException(e.getMessage(), e);
    }
  }
}
