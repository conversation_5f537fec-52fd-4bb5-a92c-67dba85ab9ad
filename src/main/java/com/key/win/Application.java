package com.key.win;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/25 10:26
 * @description 启动类
 */
@Slf4j
@EnableScheduling
@MapperScan("com.key.win.**.dao")
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class Application {

  public static void main(String[] args) {

    ConfigurableApplicationContext context = SpringApplication.run(Application.class, args);
    /** 程序品牌名称* */
    String brand = context.getEnvironment().getProperty("sf.brand");
    /** 产品名称* */
    String productName = context.getEnvironment().getProperty("sf.productName");
    /** 当前程序版本* */
    String serverVersion = context.getEnvironment().getProperty("sf.version");
    /** 当前程序版本描述* */
    String versionDesc = context.getEnvironment().getProperty("sf.versionDesc");
    /** 当前程序匹配的设备版本描述* */
    String deviceVersionDesc = context.getEnvironment().getProperty("sf.deviceVersionDesc");
    /** 当前程序启用的配置文件* */
    String active = context.getEnvironment().getProperty("spring.profiles.active");
    /** 后端程序启动端口* */
    String port = context.getEnvironment().getProperty("server.port");
    /** 配置集中告警分配的systemCode* */
    String systemCode = context.getEnvironment().getProperty("sf.tel.systemCode");
    /** 设备天线告警上报多少次后,网管才收集上报信息* */
    String reportCount = context.getEnvironment().getProperty("sf.alarm.reportCount");
    /** 是否开启实时上报到集中告警* */
    String whetherToReportAlarmsInRealTime =
        context.getEnvironment().getProperty("sf.alarm.report");
    /** 上报集中告警延迟时间（当whetherToReportAlarmsInRealTime配置开启时才生效）* */
    String delayTime = context.getEnvironment().getProperty("sf.alarm.delay");
    /** 是否启用集中告警服务* */
    String telEnable = context.getEnvironment().getProperty("sf.tel.enable");
    String reportType = "";
    if (Boolean.parseBoolean(whetherToReportAlarmsInRealTime)) {
      reportType = "实时上报";
    } else {
      reportType = "延迟上报";
    }

    log.info("=========================================================");
    log.info("=========================================================");
    log.info("||品牌:【{}】", brand);
    log.info("||产品名称:【{}】", productName);
    log.info("||程序版本:【{}】【{}】", serverVersion, versionDesc);
    log.info("||设备版本:【{}】", deviceVersionDesc);
    log.info("||设备上报路损次数:【{}】", reportCount);
    log.info("||");
    log.info("||加载的配置文件:【{}】,当前端口:【{}】", active, port);
    log.info("||集中告警是否启用:【{}】", telEnable);
    if (Boolean.parseBoolean(telEnable)) {
      log.info("||集中告警配置的SystemCode:【{}】", systemCode);
      log.info("||集中告警上报告警模式:【{}】", reportType);
      if (!Boolean.parseBoolean(whetherToReportAlarmsInRealTime)) {
        log.info("||集中告警上报延时:【{}s】", delayTime);
      }
    }
    log.info("=========================================================");
    log.info("=========================================================");
  }
}
