package com.key.win.snmpserver.trap;

import com.key.win.biz.alarm.service.RealTimeWarnSevice;
import com.key.win.snmpserver.model.RealTimeAlarm;
import com.key.win.snmpserver.oid.SFOid;
import com.key.win.snmpserver.service.RealTimeAlarmService;
import java.io.IOException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.snmp4j.CommunityTarget;
import org.snmp4j.PDU;
import org.snmp4j.Snmp;
import org.snmp4j.mp.SnmpConstants;
import org.snmp4j.smi.*;
import org.snmp4j.smi.Gauge32;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/** 实时告警Trap服务 */
@Slf4j
@Component
public class RealTimeAlarmTrapService {

  @Autowired private RealTimeAlarmService realTimeAlarmService;

  @Autowired private RealTimeWarnSevice realTimeWarnSevice;

  /** 发送单个实时告警Trap */
  public void sendAlarmTrap(
      Snmp snmp, String trapIp, int trapPort, RealTimeAlarm alarm, String notifyOIDValue) {
    try {
      Address targetAddress = UdpAddress.parse(trapIp + "/" + trapPort);
      CommunityTarget target = new CommunityTarget();
      target.setCommunity(new OctetString("public"));
      target.setVersion(SnmpConstants.version2c);
      target.setAddress(targetAddress);
      target.setRetries(2);
      target.setTimeout(15000);

      PDU trap = new PDU();
      trap.setType(PDU.TRAP);
      trap.add(
          new VariableBinding(new OID(SFOid.trapNotifyTrapOid), new OctetString(notifyOIDValue)));

      trap.add(new VariableBinding(new OID(SFOid.alarmAID), new Integer32(alarm.getAlarmAID())));
      trap.add(new VariableBinding(new OID(SFOid.alarmDAID), new Integer32(0))); // 默认值
      trap.add(
          new VariableBinding(
              new OID(SFOid.alarmProperty), new Integer32(alarm.getAlarmProperty())));
      trap.add(
          new VariableBinding(
              new OID(SFOid.alarmSeries), new Integer32(alarm.getAlarmSeries()))); // 默认值
      trap.add(
          new VariableBinding(
              new OID(SFOid.alarmStyle), new Integer32(alarm.getAlarmStyle()))); // 默认值
      trap.add(
          new VariableBinding(new OID(SFOid.alarmEquipID), new Integer32(alarm.getAlarmEquipID())));
      trap.add(
          new VariableBinding(
              new OID(SFOid.alarmModule1), new OctetString(alarm.getAlarmModule1())));
      trap.add(
          new VariableBinding(
              new OID(SFOid.alarmCard2), new OctetString(alarm.getAlarmCard2()))); // 默认值
      trap.add(
          new VariableBinding(
              new OID(SFOid.alarmPort3), new OctetString(alarm.getAlarmPort3()))); // 默认值
      trap.add(
          new VariableBinding(
              new OID(SFOid.alarmReserve4), new OctetString(alarm.getAlarmReserve4()))); // 默认值
      trap.add(
          new VariableBinding(
              new OID(SFOid.alarmSeverity), new Integer32(alarm.getAlarmSeverity())));
      trap.add(
          new VariableBinding(new OID(SFOid.alarmStatus), new Integer32(alarm.getAlarmStatus())));
      trap.add(
          new VariableBinding(
              new OID(SFOid.alarmDateTime), new OctetString(alarm.getAlarmDateTime())));
      trap.add(
          new VariableBinding(
              new OID(SFOid.alarmAlmNum), new Gauge32(alarm.getAlarmAlmNum()))); // 默认值
      trap.add(
          new VariableBinding(
              new OID(SFOid.alarmAlmCategory), new OctetString(alarm.getAlarmAlmCategory())));
      trap.add(
          new VariableBinding(
              new OID(SFOid.alarmAlmText), new OctetString(alarm.getAlarmAlmText())));
      trap.add(
          new VariableBinding(
              new OID(SFOid.alarmFromShelf), new OctetString(alarm.getAlarmFromShelf())));
      trap.add(
          new VariableBinding(
              new OID(SFOid.alarmShelfLoc), new OctetString(alarm.getAlarmShelfLoc())));
      trap.add(
          new VariableBinding(
              new OID(SFOid.alarmONAlmNum), new Gauge32(alarm.getAlarmONAlmNum()))); // 默认值
      trap.add(
          new VariableBinding(
              new OID(SFOid.alarmONAlmCategory), new OctetString(alarm.getAlarmONAlmCategory())));

      snmp.send(trap, target);
      log.info("实时告警Trap已发送: {} - {}", alarm.getAlarmAID(), alarm.getAlarmAlmText());

    } catch (IOException e) {
      log.error("发送实时告警Trap失败: {}", e.getMessage(), e);
    }
  }

  public void sendTrap(Snmp snmp, String trapIp, int trapPort, String notifyOIDValue) {
    Address targetAddress = UdpAddress.parse(trapIp + "/" + trapPort);
    CommunityTarget target = new CommunityTarget();
    target.setCommunity(new OctetString("public"));
    target.setVersion(SnmpConstants.version2c);
    target.setAddress(targetAddress);
    target.setRetries(2);
    target.setTimeout(15000);

    PDU trap = new PDU();
    trap.setType(PDU.TRAP);
    trap.add(
        new VariableBinding(new OID(SFOid.trapNotifyTrapOid), new OctetString(notifyOIDValue)));
    try {
      snmp.send(trap, target);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
    log.info("Trap已发送: {} - {}", SFOid.trapNotifyTrapOid, notifyOIDValue);
  }

  public void sendAlarmTableTraps(Snmp snmp, String trapIp, int trapPort) {
    List<RealTimeAlarm> realTimeAlarms = realTimeAlarmService.loadRealTimeAlarms();
    log.info("开始发送批量实时告警Trap，共{}条告警", realTimeAlarms.size());
    for (RealTimeAlarm alarm : realTimeAlarms) {
      sendAlarmTrap(snmp, trapIp, trapPort, alarm, SFOid.trapAlarmItemOid);
      try {
        Thread.sleep(200); // 每条告警间隔200ms
      } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
        log.error("发送告警Trap被中断", e);
        break;
      }
    }
    log.info("批量实时告警Trap发送完成");
  }
}
