package com.key.win.snmpserver.trap;

import com.key.win.snmpserver.model.RealTimeAlarm;
import com.key.win.snmpserver.oid.SFOid;
import com.key.win.snmpserver.service.RealTimeAlarmService;
import java.io.IOException;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.snmp4j.CommunityTarget;
import org.snmp4j.Snmp;
import org.snmp4j.mp.SnmpConstants;
import org.snmp4j.smi.*;
import org.snmp4j.transport.DefaultUdpTransportMapping;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AlarmTrapService {

  @Resource private RealTimeAlarmService realTimeAlarmService;
  @Resource private RealTimeAlarmTrapService realTimeAlarmTrapService;

  public Snmp trapClientStart() {
    try {
      Snmp snmp = new Snmp(new DefaultUdpTransportMapping());
      snmp.listen();
      return snmp;
    } catch (Exception e) {
      e.printStackTrace();
    }
    return null;
  }

  /**
   * 告警同步指令接收到之后的 上报的trap
   *
   * @param snmp
   * @param trapIp
   * @param trapPort
   * @throws IOException
   * @throws InterruptedException
   */
  public void alarmAsyncTraps(Snmp snmp, String trapIp, int trapPort)
      throws IOException, InterruptedException {
    if (SFOid.syncState.get() == 0) {
      log.info("trapData ==== 取消同步>>>>>>>>");
      return;
    }

    realTimeAlarmTrapService.sendTrap(snmp, trapIp, trapPort, SFOid.trapSyncStartOid);
    log.info("告警同步开始===>{} || {}", SFOid.trapNotifyTrapOid, SFOid.trapSyncStartOid);

    List<RealTimeAlarm> mockAlarms = realTimeAlarmService.loadRealTimeAlarms();
    CommunityTarget target = getCommunityTarget(trapIp, trapPort);

    for (int i = 0; i < mockAlarms.size() - 3; i++) {
      log.info("当前同步状态===>{}", SFOid.syncState.get());
      if (SFOid.syncState.get() == 0) {
        log.info("同步状态已被中断，取消 Trap 发送流程");
        return;
      }
      RealTimeAlarm alarm = mockAlarms.get(i);
      realTimeAlarmTrapService.sendAlarmTrap(snmp, trapIp, trapPort, alarm, SFOid.trapAlarmItemOid);
      log.info("单条告警 Trap 已发送：{}", alarm.getAlarmAlmText());
      Thread.sleep(200);
    }

    realTimeAlarmTrapService.sendTrap(snmp, trapIp, trapPort, SFOid.trapSyncEndOid);
    log.info("告警同步结束===>{} || {}", SFOid.trapNotifyTrapOid, SFOid.trapSyncEndOid);

    SFOid.syncState.set(0); // 回到非同步状态
  }

  private static CommunityTarget getCommunityTarget(String trapIp, int trapPort) {
    Address targetAddress = UdpAddress.parse(trapIp + "/" + trapPort);
    CommunityTarget target = new CommunityTarget();
    target.setCommunity(new OctetString("public"));
    target.setVersion(SnmpConstants.version2c);
    target.setAddress(targetAddress);
    target.setRetries(2);
    target.setTimeout(15000);
    return target;
  }
}
