package com.key.win.snmpserver.config;

import com.key.win.snmpserver.service.SnmpConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * SNMP配置初始化器
 * 在应用启动时初始化SNMP配置
 */
@Slf4j
@Component
public class SnmpConfigInitializer implements ApplicationRunner {

    @Autowired
    private SnmpConfigService snmpConfigService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        try {
            log.info("开始初始化SNMP配置...");
            
            // 初始化默认配置
            snmpConfigService.initDefaultConfigs();
            
            // 刷新SFOid中的配置
            snmpConfigService.refreshSFOidConfig();
            
            log.info("SNMP配置初始化完成");
        } catch (Exception e) {
            log.error("SNMP配置初始化失败", e);
        }
    }
}
