package com.key.win.snmpserver;

import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.snmp4j.*;
import org.snmp4j.mp.MPv3;
import org.snmp4j.mp.StatusInformation;
import org.snmp4j.smi.*;
import org.snmp4j.transport.DefaultUdpTransportMapping;
import org.springframework.stereotype.Service;

@Service
public class SnmpAgentService implements CommandResponder {
  private static final Logger log = LoggerFactory.getLogger(SnmpAgentService.class);

  @Resource private OidDataStore oidDataStore;

  public Snmp startAgent(String ip, int port) throws Exception {
    TransportMapping<UdpAddress> transport =
        new DefaultUdpTransportMapping(new UdpAddress(ip + "/" + port));
    Snmp snmp = new Snmp(transport);
    snmp.getMessageDispatcher().addMessageProcessingModel(new MPv3());
    snmp.listen();
    snmp.addCommandResponder(this);
    log.info("SNMP Agent is running on {}/{}", ip, port);
    return snmp;
  }

  @Override
  public void processPdu(CommandResponderEvent event) {
    PDU request = event.getPDU();
    if (request == null) return;

    log.info("Received SNMP request: {}", request);

    PDU response = new PDU();
    response.setType(PDU.RESPONSE);
    response.setRequestID(request.getRequestID());

    for (VariableBinding vb : request.getVariableBindings()) {
      OID oid = vb.getOid();
      Variable result;

      if (request.getType() == PDU.GET) {
        result = oidDataStore.getValue(oid);
        if (result != null) {
          response.add(new VariableBinding(oid, result));
        } else {
          response.setErrorStatus(PDU.noSuchName);
          response.setErrorIndex(1);
          break;
        }

      } else if (request.getType() == PDU.SET) {
        if (oidDataStore.isWritable(oid)) {
          result = oidDataStore.setValue(oid, vb.getVariable());
          if (result != null) {
            response.add(new VariableBinding(oid, result));
          } else {
            response.setErrorStatus(PDU.wrongValue);
            response.setErrorIndex(1);
            break;
          }
        } else {
          response.setErrorStatus(PDU.notWritable);
          response.setErrorIndex(1);
          break;
        }
      } else {
        response.setErrorStatus(PDU.genErr);
        response.setErrorIndex(1);
        break;
      }
    }
    try {
      event
          .getMessageDispatcher()
          .returnResponsePdu(
              event.getMessageProcessingModel(),
              event.getSecurityModel(),
              event.getSecurityName(),
              event.getSecurityLevel(),
              response,
              event.getMaxSizeResponsePDU(),
              event.getStateReference(),
              new StatusInformation());

      log.info("Sent SNMP response: {}", response);

    } catch (Exception e) {
      log.error("Failed to send SNMP response", e);
    }
  }
}
