package com.key.win.snmpserver.service;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.alarm.model.RealTimeWarn;
import com.key.win.biz.alarm.service.RealTimeWarnSevice;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.snmpserver.model.RealTimeAlarm;
import com.key.win.snmpserver.model.ZHWGDevice;
import com.key.win.snmpserver.oid.SFOid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/** 实时告警服务 */
@Slf4j
@Service
public class RealTimeAlarmService {

  @Autowired private RealTimeWarnSevice realTimeWarnSevice;

  private final ConcurrentHashMap<Integer, RealTimeAlarm> alarmStore = new ConcurrentHashMap<>();
  private final AtomicInteger alarmCounter = new AtomicInteger(1);

  /** 生成模拟实时告警 */
  public List<RealTimeAlarm> loadRealTimeAlarms() {
    List<RealTimeAlarm> mockAlarms = new ArrayList<>();
    try {

      LambdaQueryWrapper<RealTimeWarn> lqw = new LambdaQueryWrapper<>();
      lqw.eq(RealTimeWarn::getEnableFlag, true);
      lqw.eq(RealTimeWarn::getAlarmStatus, GConfig.alarmStatus_realTime);
      List<RealTimeWarn> list = realTimeWarnSevice.list(lqw);

      if (list.size() > 0) {
        for (int i = 0; i < list.size(); i++) {
          RealTimeWarn warn = list.get(i);
          String hostNumber = warn.getHostNumber();
          String networkName = warn.getNetworkName();
          String equipmentType = warn.getEquipmentType();
          String alarmTitle = warn.getAlarmName();
          String stationId = warn.getBelongStationId();
          String alarmTime = warn.getAlarmTime();
          Integer severity = 6;
          String alarmType = SFOid.zhwgAlarmTypeMap.get(equipmentType);
          if (alarmType.equals("SF_DEVICE")) {
            severity = 2;
          } else if (alarmType.equals("SF_DEVICE")) {
            severity = 3;
          }
          String mapSearchKey = stationId + "#" + hostNumber + "#" + networkName;
          ZHWGDevice zhwgDevice = MapUtil.get(SFOid.zhwgDeviceMap, mapSearchKey, ZHWGDevice.class);
          if (zhwgDevice == null) {
            log.info(
                "当前信息{}#{}#{}没有匹配到综合网管的设备点表,请检查数据库中zhwg_device表中的配置",
                stationId,
                hostNumber,
                networkName);
            continue;
          }
          Integer equipId = zhwgDevice.getEquipId();
          Integer series = zhwgDevice.getSeries();
          Integer style = zhwgDevice.getStyle();

          RealTimeAlarm alarm = new RealTimeAlarm();
          alarm.setAlarmAID((i + 1));
          alarm.setAlarmDAID((i + 1));
          alarm.setAlarmProperty(0); // 0为设备告警 1环境告警 2性能告警

          alarm.setAlarmSeries(series);
          alarm.setAlarmStyle(style);
          alarm.setAlarmEquipID(equipId); // 给集中告警的设备ID

          alarm.setAlarmModule1("");
          alarm.setAlarmCard2("");
          alarm.setAlarmPort3("");
          alarm.setAlarmReserve4("");

          alarm.setAlarmSeverity(severity); // 告警级别 1~6  1最严重
          alarm.setAlarmStatus(2); // 2故障告警 1恢复 0事件告警
          alarm.setAlarmDateTime(alarmTime);
          alarm.setAlarmAlmNum(1L);

          alarm.setAlarmAlmCategory(alarmType);
          alarm.setAlarmAlmText(alarmTitle);

          alarm.setAlarmFromShelf("");
          alarm.setAlarmShelfLoc("");

          alarm.setAlarmONAlmNum(0L); // 如果本条告警是恢复告警，与其匹配的故障告警号；
          alarm.setAlarmONAlmCategory(""); // 如果本条告警是恢复告警，与其匹配的故障告警类；如果本条告警是故障告警或事件告警，置空即可

          mockAlarms.add(alarm);
        }
      }

    } catch (Exception e) {
      log.error("构建实时告警列表失败...>", e);
    }

    return mockAlarms;
  }

  private String now() {
    return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
  }
}
