package com.key.win.snmpserver.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.key.win.common.web.MybatiesPageServiceTemplate;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import com.key.win.snmpserver.dao.SnmpConfigDao;
import com.key.win.snmpserver.model.SnmpConfig;
import com.key.win.snmpserver.oid.SFOid;
import com.key.win.snmpserver.service.SnmpConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;

/**
 * SNMP配置服务实现类
 */
@Slf4j
@Service
public class SnmpConfigServiceImpl extends ServiceImpl<SnmpConfigDao, SnmpConfig> implements SnmpConfigService {

    @PostConstruct
    public void init() {
        initDefaultConfigs();
        refreshSFOidConfig();
    }

    @Override
    public PageResult<SnmpConfig> getPageSnmpConfig(PageRequest<SnmpConfig> pageRequest) {
        MybatiesPageServiceTemplate<SnmpConfig, SnmpConfig> page =
                new MybatiesPageServiceTemplate<SnmpConfig, SnmpConfig>(this.baseMapper) {
                    @Override
                    protected com.baomidou.mybatisplus.core.conditions.Wrapper<SnmpConfig> constructWrapper(SnmpConfig snmpConfig) {
                        LambdaQueryWrapper<SnmpConfig> lqw = new LambdaQueryWrapper<>();
                        lqw.orderByAsc(SnmpConfig::getSortOrder);
                        lqw.orderByAsc(SnmpConfig::getConfigKey);
                        
                        if (snmpConfig != null) {
                            if (StringUtils.isNotBlank(snmpConfig.getConfigKey())) {
                                lqw.like(SnmpConfig::getConfigKey, snmpConfig.getConfigKey());
                            }
                            if (StringUtils.isNotBlank(snmpConfig.getDescription())) {
                                lqw.like(SnmpConfig::getDescription, snmpConfig.getDescription());
                            }
                            if (snmpConfig.getEnabled() != null) {
                                lqw.eq(SnmpConfig::getEnabled, snmpConfig.getEnabled());
                            }
                        }
                        return lqw;
                    }
                };
        return page.doPagingQuery(pageRequest);
    }

    @Override
    public Result saveOrUpdateSnmpConfig(SnmpConfig snmpConfig) {
        try {
            if (StringUtils.isBlank(snmpConfig.getConfigKey())) {
                return Result.failed("配置键不能为空");
            }
            
            // 检查是否已存在相同的配置键
            if (StringUtils.isBlank(snmpConfig.getId())) {
                LambdaQueryWrapper<SnmpConfig> lqw = new LambdaQueryWrapper<>();
                lqw.eq(SnmpConfig::getConfigKey, snmpConfig.getConfigKey());
                SnmpConfig existConfig = getOne(lqw);
                if (existConfig != null) {
                    return Result.failed("配置键已存在");
                }
            }
            
            boolean result = saveOrUpdate(snmpConfig);
            if (result) {
                refreshSFOidConfig();
                return Result.succeed(snmpConfig, "保存成功");
            } else {
                return Result.failed("保存失败");
            }
        } catch (Exception e) {
            log.error("保存SNMP配置失败", e);
            return Result.failed("保存失败: " + e.getMessage());
        }
    }

    @Override
    public String getConfigValue(String configKey) {
        return getConfigValue(configKey, null);
    }

    @Override
    public String getConfigValue(String configKey, String defaultValue) {
        LambdaQueryWrapper<SnmpConfig> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SnmpConfig::getConfigKey, configKey);
        lqw.eq(SnmpConfig::getEnabled, true);
        SnmpConfig config = getOne(lqw);
        return config != null ? config.getConfigValue() : defaultValue;
    }

    @Override
    public Result updateConfigValue(String configKey, String configValue) {
        try {
            LambdaUpdateWrapper<SnmpConfig> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(SnmpConfig::getConfigKey, configKey);
            updateWrapper.set(SnmpConfig::getConfigValue, configValue);
            
            boolean result = update(updateWrapper);
            if (result) {
                refreshSFOidConfig();
                return Result.succeed("更新成功");
            } else {
                return Result.failed("更新失败");
            }
        } catch (Exception e) {
            log.error("更新SNMP配置失败", e);
            return Result.failed("更新失败: " + e.getMessage());
        }
    }

    @Override
    public List<SnmpConfig> getAllEnabledConfigs() {
        LambdaQueryWrapper<SnmpConfig> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SnmpConfig::getEnabled, true);
        lqw.orderByAsc(SnmpConfig::getSortOrder);
        return list(lqw);
    }

    @Override
    public void initDefaultConfigs() {
        // 检查是否已经初始化过
        long count = count();
        if (count > 0) {
            return;
        }

        List<SnmpConfig> defaultConfigs = Arrays.asList(
                SnmpConfig.builder()
                        .configKey("snmp.client.ip")
                        .configValue("*************")
                        .description("SNMP客户端IP地址")
                        .configType("IP")
                        .enabled(true)
                        .sortOrder(1)
                        .build(),
                SnmpConfig.builder()
                        .configKey("snmp.client.port")
                        .configValue("162")
                        .description("SNMP客户端端口号")
                        .configType("PORT")
                        .enabled(true)
                        .sortOrder(2)
                        .build()
        );

        saveBatch(defaultConfigs);
        log.info("SNMP默认配置初始化完成");
    }

    @Override
    public void refreshSFOidConfig() {
        try {
            String clientIp = getConfigValue("snmp.client.ip", "*************");
            String clientPort = getConfigValue("snmp.client.port", "162");
            
            SFOid.snmpClientIp = clientIp;
            SFOid.snmpClientPort = Integer.parseInt(clientPort);
            
            log.info("SFOid配置已刷新: IP={}, Port={}", clientIp, clientPort);
        } catch (Exception e) {
            log.error("刷新SFOid配置失败", e);
        }
    }
}
