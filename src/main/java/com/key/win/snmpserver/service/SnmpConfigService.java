package com.key.win.snmpserver.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import com.key.win.snmpserver.model.SnmpConfig;

import java.util.List;

/**
 * SNMP配置服务接口
 */
public interface SnmpConfigService extends IService<SnmpConfig> {

    /**
     * 分页查询SNMP配置
     */
    PageResult<SnmpConfig> getPageSnmpConfig(PageRequest<SnmpConfig> pageRequest);

    /**
     * 保存或更新SNMP配置
     */
    Result saveOrUpdateSnmpConfig(SnmpConfig snmpConfig);

    /**
     * 根据配置键获取配置值
     */
    String getConfigValue(String configKey);

    /**
     * 根据配置键获取配置值，如果不存在则返回默认值
     */
    String getConfigValue(String configKey, String defaultValue);

    /**
     * 更新配置值
     */
    Result updateConfigValue(String configKey, String configValue);

    /**
     * 获取所有启用的配置
     */
    List<SnmpConfig> getAllEnabledConfigs();

    /**
     * 初始化默认配置
     */
    void initDefaultConfigs();

    /**
     * 刷新SFOid中的配置
     */
    void refreshSFOidConfig();
}
