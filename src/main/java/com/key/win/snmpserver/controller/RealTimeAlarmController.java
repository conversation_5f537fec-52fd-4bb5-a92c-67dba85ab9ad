package com.key.win.snmpserver.controller;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.alarm.model.RealTimeWarn;
import com.key.win.biz.alarm.service.RealTimeWarnSevice;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.snmpserver.model.RealTimeAlarm;
import com.key.win.snmpserver.model.ZHWGDevice;
import com.key.win.snmpserver.oid.SFOid;
import com.key.win.snmpserver.service.RealTimeAlarmService;
import com.key.win.snmpserver.trap.RealTimeAlarmTrapService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/** 实时告警REST API控制器 */
@Slf4j
@RestController
@RequestMapping("/api/realtime-alarm")
public class RealTimeAlarmController {

  @Autowired private RealTimeAlarmService realTimeAlarmService;

  @Autowired private RealTimeAlarmTrapService realTimeAlarmTrapService;

  @Autowired private RealTimeWarnSevice realTimeWarnSevice;

  /** 获取所有实时告警 */
  @GetMapping("/all")
  public ResponseEntity<List<RealTimeWarn>> getAllAlarms() {
    LambdaQueryWrapper<RealTimeWarn> lqw = new LambdaQueryWrapper<>();
    lqw.eq(RealTimeWarn::getEnableFlag, true);
    lqw.eq(RealTimeWarn::getAlarmStatus, GConfig.alarmStatus_realTime);
    List<RealTimeWarn> sfAlarms = realTimeWarnSevice.list(lqw);
    return ResponseEntity.ok(sfAlarms);
  }

  /** 主动上报实时告警trap到Manager */
  @PostMapping("/send-mock-traps")
  public ResponseEntity<Map<String, Object>> sendMockTraps() {
    try {
      String trapIp = SFOid.snmpClientIp;
      int trapPort = SFOid.snmpClientPort;

      List<RealTimeAlarm> realTimeAlarms = realTimeAlarmService.loadRealTimeAlarms();

      realTimeAlarms.forEach(
          alarm -> {
            realTimeAlarmTrapService.sendAlarmTrap(
                SFOid.snmp162, trapIp, trapPort, alarm, SFOid.trapRealTimeReport);
          });

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "批量实时告警Trap发送成功");
      return ResponseEntity.ok(response);
    } catch (Exception e) {
      log.error("批量发送实时告警Trap失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "发送失败: " + e.getMessage());
      return ResponseEntity.ok(response);
    }
  }

  /** 主动上报告警trap 单条操作 */
  @PostMapping("/send-mock-traps2")
  public ResponseEntity<Map<String, Object>> sendMockTraps2(@RequestParam String id) {
    if (StringUtils.isBlank(id)) {
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "传入的告警ID为空");
      return ResponseEntity.ok(response);
    }
    try {
      String trapIp = SFOid.snmpClientIp;
      int trapPort = SFOid.snmpClientPort;

      LambdaQueryWrapper<RealTimeWarn> lqw = new LambdaQueryWrapper<>();
      lqw.eq(RealTimeWarn::getId, id);
      lqw.eq(RealTimeWarn::getEnableFlag, true);
      lqw.eq(RealTimeWarn::getAlarmStatus, GConfig.alarmStatus_realTime);
      List<RealTimeWarn> list = realTimeWarnSevice.list(lqw);
      if (list.size() > 0) {
        RealTimeWarn warn = list.get(0);

        String hostNumber = warn.getHostNumber();
        String networkName = warn.getNetworkName();
        String equipmentType = warn.getEquipmentType();
        String alarmTitle = warn.getAlarmName();
        String stationId = warn.getBelongStationId();
        String alarmTime = warn.getAlarmTime();

        Integer severity = 6;
        String alarmType = SFOid.zhwgAlarmTypeMap.get(equipmentType);
        if (alarmType.equals("SF_DEVICE")) {
          severity = 2;
        } else if (alarmType.equals("SF_DEVICE")) {
          severity = 3;
        }

        String mapSearchKey = stationId + "#" + hostNumber + "#" + networkName;
        ZHWGDevice zhwgDevice = MapUtil.get(SFOid.zhwgDeviceMap, mapSearchKey, ZHWGDevice.class);
        if (zhwgDevice == null) {
          log.info(
              "当前信息{}#{}#{}没有匹配到综合网管的设备点表,请检查数据库中zhwg_device表中的配置",
              stationId,
              hostNumber,
              networkName);
          Map<String, Object> response = new HashMap<>();
          response.put("success", false);
          response.put("message", "当前告警信息没有匹配到综合网管点表设备数据.");
          return ResponseEntity.ok(response);
        }

        Integer equipId = zhwgDevice.getEquipId();
        Integer series = zhwgDevice.getSeries();
        Integer style = zhwgDevice.getStyle();

        RealTimeAlarm alarm = new RealTimeAlarm();
        alarm.setAlarmAID(1);
        alarm.setAlarmDAID(1);
        alarm.setAlarmProperty(0); // 0为设备告警 1环境告警 2性能告警

        alarm.setAlarmSeries(series);
        alarm.setAlarmStyle(style);
        alarm.setAlarmEquipID(equipId); // 给集中告警的设备ID

        alarm.setAlarmModule1("");
        alarm.setAlarmCard2("");
        alarm.setAlarmPort3("");
        alarm.setAlarmReserve4("");

        alarm.setAlarmSeverity(severity); // 告警级别 1~6  1最严重
        alarm.setAlarmStatus(2); // 2故障告警 1恢复 0事件告警
        alarm.setAlarmDateTime(alarmTime);
        alarm.setAlarmAlmNum(1L);

        alarm.setAlarmAlmCategory(alarmType);
        alarm.setAlarmAlmText(alarmTitle);

        alarm.setAlarmFromShelf("");
        alarm.setAlarmShelfLoc("");

        alarm.setAlarmONAlmNum(0L); // 如果本条告警是恢复告警，与其匹配的故障告警号；
        alarm.setAlarmONAlmCategory(""); // 如果本条告警是恢复告警，与其匹配的故障告警类；如果本条告警是故障告警或事件告警，置空即可

        realTimeAlarmTrapService.sendAlarmTrap(
            SFOid.snmp162, trapIp, trapPort, alarm, SFOid.trapRealTimeReport);

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "模拟实时告警Trap发送成功");
        return ResponseEntity.ok(response);
      } else {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "告警数据不存在..");
        return ResponseEntity.ok(response);
      }
    } catch (Exception e) {
      log.error("发送实时告警Trap失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "发送失败: " + e.getMessage());
      return ResponseEntity.ok(response);
    }
  }

  /** 向Manager 发送 告警同步的申请trap 当agent离线后，agent需要向manager发送告警同步的申请trap manager届时会发起告警同步trap到agent */
  @PostMapping("/send-async-traps")
  public ResponseEntity<Map<String, Object>> sendAsyncTraps() {
    try {
      String trapIp = SFOid.snmpClientIp;
      int trapPort = SFOid.snmpClientPort;

      realTimeAlarmTrapService.sendTrap(SFOid.snmp162, trapIp, trapPort, SFOid.发起告警同步申请OID);

      Map<String, Object> response = new HashMap<>();
      response.put("success", true);
      response.put("message", "主动告警同步申请,发送成功 " + SFOid.发起告警同步申请OID);
      return ResponseEntity.ok(response);
    } catch (Exception e) {
      log.error("主动告警同步申请 发送Trap失败", e);
      Map<String, Object> response = new HashMap<>();
      response.put("success", false);
      response.put("message", "发送失败: " + e.getMessage());
      return ResponseEntity.ok(response);
    }
  }
}
