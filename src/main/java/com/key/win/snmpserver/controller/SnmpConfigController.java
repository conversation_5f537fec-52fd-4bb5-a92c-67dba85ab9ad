package com.key.win.snmpserver.controller;

import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import com.key.win.snmpserver.model.SnmpConfig;
import com.key.win.snmpserver.oid.SFOid;
import com.key.win.snmpserver.service.SnmpConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SNMP配置控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/snmp-config")
public class SnmpConfigController {

    @Autowired
    private SnmpConfigService snmpConfigService;

    /**
     * 分页查询SNMP配置
     */
    @PostMapping("/page")
    public PageResult<SnmpConfig> getPageSnmpConfig(@RequestBody PageRequest<SnmpConfig> pageRequest) {
        return snmpConfigService.getPageSnmpConfig(pageRequest);
    }

    /**
     * 获取所有启用的配置
     */
    @GetMapping("/all")
    public Result<List<SnmpConfig>> getAllEnabledConfigs() {
        List<SnmpConfig> configs = snmpConfigService.getAllEnabledConfigs();
        return Result.succeed(configs, "查询成功");
    }

    /**
     * 根据ID查询配置
     */
    @GetMapping("/{id}")
    public Result<SnmpConfig> getById(@PathVariable String id) {
        SnmpConfig config = snmpConfigService.getById(id);
        if (config != null) {
            return Result.succeed(config, "查询成功");
        } else {
            return Result.failed("配置不存在");
        }
    }

    /**
     * 保存或更新SNMP配置
     */
    @PostMapping("/save")
    public Result saveOrUpdateSnmpConfig(@RequestBody SnmpConfig snmpConfig) {
        return snmpConfigService.saveOrUpdateSnmpConfig(snmpConfig);
    }

    /**
     * 删除配置
     */
    @DeleteMapping("/{id}")
    public Result deleteById(@PathVariable String id) {
        try {
            boolean result = snmpConfigService.removeById(id);
            if (result) {
                snmpConfigService.refreshSFOidConfig();
                return Result.succeed("删除成功");
            } else {
                return Result.failed("删除失败");
            }
        } catch (Exception e) {
            log.error("删除SNMP配置失败", e);
            return Result.failed("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新配置
     */
    @PostMapping("/batch-update")
    public Result batchUpdateConfigs(@RequestBody Map<String, String> configMap) {
        try {
            for (Map.Entry<String, String> entry : configMap.entrySet()) {
                snmpConfigService.updateConfigValue(entry.getKey(), entry.getValue());
            }
            return Result.succeed("批量更新成功");
        } catch (Exception e) {
            log.error("批量更新SNMP配置失败", e);
            return Result.failed("批量更新失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前生效的配置信息
     */
    @GetMapping("/current")
    public Result<Map<String, Object>> getCurrentConfig() {
        Map<String, Object> currentConfig = new HashMap<>();
        currentConfig.put("snmpClientIp", SFOid.snmpClientIp);
        currentConfig.put("snmpClientPort", SFOid.snmpClientPort);
        return Result.succeed(currentConfig, "获取当前配置成功");
    }

    /**
     * 刷新配置
     */
    @PostMapping("/refresh")
    public Result refreshConfig() {
        try {
            snmpConfigService.refreshSFOidConfig();
            return Result.succeed("配置刷新成功");
        } catch (Exception e) {
            log.error("刷新配置失败", e);
            return Result.failed("刷新配置失败: " + e.getMessage());
        }
    }

    /**
     * 测试连接
     */
    @PostMapping("/test-connection")
    public Result testConnection(@RequestBody Map<String, String> testConfig) {
        try {
            String ip = testConfig.get("ip");
            String port = testConfig.get("port");
            
            // 这里可以添加实际的连接测试逻辑
            // 暂时返回成功，实际项目中可以尝试发送测试SNMP包
            
            return Result.succeed("连接测试成功");
        } catch (Exception e) {
            log.error("连接测试失败", e);
            return Result.failed("连接测试失败: " + e.getMessage());
        }
    }
}
