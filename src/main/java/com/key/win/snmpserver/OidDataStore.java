package com.key.win.snmpserver;

import com.key.win.snmpserver.oid.SFOid;
import com.key.win.snmpserver.setting.HeartbeatOIDSetting;
import com.key.win.snmpserver.trap.AlarmTrapService;
import com.key.win.snmpserver.trap.RealTimeAlarmTrapService;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.snmp4j.smi.*;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class OidDataStore {
  private final Map<String, Function<Variable, Variable>> oidHandlersMap = new HashMap<>();

  @Resource private HeartbeatOIDSetting heartbeatStore;
  @Resource private AlarmTrapService alarmTrapService;
  @Resource private RealTimeAlarmTrapService realTimeAlarmTrapService;

  public OidDataStore() {
    bindOid();
  }

  private void bindOid() {
    // 心跳周期设置 OID : *******.4.1.37405.1.1.2.1
    oidHandlersMap.put(
        SFOid.heartBeat_interval,
        (input) -> {
          if (input instanceof Null) {
            return new Integer32(heartbeatStore.get());
          } else {
            if (input instanceof Integer32) {
              int val = ((Integer32) input).getValue();
              if (heartbeatStore.set(val)) {
                return new Integer32(val);
              }
            }
            return null; // 设置失败
          }
        });

    // 心跳响应 OID : *******.4.1.37405.1.1.2.2
    oidHandlersMap.put(
        SFOid.heartBeat,
        (input) -> {
          if (input instanceof Null) { // GET 请求，返回当前时间
            String now =
                java.time.LocalDateTime.now()
                    .format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return new OctetString(now);
          } else {
            return null;
          }
        });

    // 来自manager的告警同步 OID : *******.4.1.37405.1.1.2.3
    oidHandlersMap.put(
        SFOid.alarmAsyncOid,
        (input) -> {
          if (input instanceof Null) {
            return new Integer32(SFOid.syncState.get());
          } else if (input instanceof Integer32) {
            int requestedValue = ((Integer32) input).getValue();

            if (requestedValue != 0 && requestedValue != 1) {
              return null; // 非法值，BadValue
            }
            int current = SFOid.syncState.get();
            if (requestedValue == 1 && current == 1) {
              log.info("告警正在同步中....>>>>>");
              return null;
            }
            SFOid.syncState.set(requestedValue); // 更改为同步状态
            if (requestedValue == 0) {
              log.info("告警同步取消>>>>>>");
              log.info("告警同步被 Manager 主动中止"); // requestedValue == 0 且当前是 1，视为 Manager 强制中止同步
              return new Integer32(requestedValue);
            }
            log.info(
                "当前的同步标识:current:{} , requestedValue:{}", SFOid.syncState.get(), requestedValue);
            String trapIp = SFOid.snmpClientIp;
            int trapPort = SFOid.snmpClientPort;

            new Thread(
                    () -> {
                      try {
                        alarmTrapService.alarmAsyncTraps(SFOid.snmp162, trapIp, trapPort);
                      } catch (IOException e) {
                        throw new RuntimeException(e);
                      } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                      }
                    })
                .start();
            return new Integer32(requestedValue); // 正常响应
          }
          return null; // 非法类型，BadValue
        });

    // 当前告警表Table OID : *******.4.1.37405.*******
    oidHandlersMap.put(
        SFOid.alarmTableTrapOID,
        (input) -> {
          if (input instanceof Null) {
            return new Integer32(0); // GET请求返回当前状态
          } else if (input instanceof Integer32) {
            int requestedValue = ((Integer32) input).getValue();

            if (requestedValue != 0 && requestedValue != 1) {
              return null; // 非法值，BadValue
            }

            if (requestedValue == 1) {
              log.info("触发实时告警Trap发送");
              String trapIp = SFOid.snmpClientIp;
              int trapPort = SFOid.snmpClientPort;

              new Thread(
                      () -> {
                        try {
                          realTimeAlarmTrapService.sendAlarmTableTraps(
                              SFOid.snmp162, trapIp, trapPort);
                        } catch (Exception e) {
                          log.error("发送实时告警Trap失败", e);
                        }
                      })
                  .start();
            }

            return new Integer32(requestedValue);
          }
          return null; // 非法类型，BadValue
        });
  }

  public Variable getValue(OID oid) {
    Function<Variable, Variable> handler = oidHandlersMap.get(oid.toString());
    return handler != null ? handler.apply(new Null()) : null;
  }

  public Variable setValue(OID oid, Variable newValue) {
    Function<Variable, Variable> handler = oidHandlersMap.get(oid.toString());
    return handler != null ? handler.apply(newValue) : null;
  }

  public boolean isWritable(OID oid) {
    return oidHandlersMap.containsKey(oid.toString());
  }

  public Map<String, Function<Variable, Variable>> getOidHandlersMap() {
    return oidHandlersMap;
  }
}
