package com.key.win.snmpserver.oid;

import com.key.win.snmpserver.model.ZHWGDevice;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import org.snmp4j.Snmp;

public class SFOid {

  /***********SYSTEM OID********************************************************************************************************************************/
  public static final String trapNotifyTrapOid = "*******.6.3.1.1.4.1";

  /****心跳OID*********************************************************************   ******************************************************************/
  public static final String heartBeat_interval = "*******.4.1.37405.1.1.2.1"; // 心跳周期OID

  public static final String heartBeat = "*******.4.1.37405.1.1.2.2"; // 心跳获取OID

  /***告警同步OID    ************************************************************************************************************************************/
  public static final String alarmAsyncOid = "*******.4.1.37405.1.1.2.3"; // 告警同步触发OID

  public static final String trapSyncStartOid = "*******.4.1.37405.1.1.3.0.1"; // 告警开始OID
  public static final String trapAlarmItemOid = "*******.4.1.37405.1.1.3.0.2"; // 告警同步过程OID
  public static final String trapSyncEndOid = "*******.4.1.37405.1.1.3.0.3"; // 告警结束OID

  /** SNMP Agent新产生的告警，通过该Trap上报SNMP Manager */
  public static final String trapRealTimeReport = "*******.4.1.37405.1.1.3.0.4"; // 实时告警TrapOID

  /** 告警同步申请trap 离线后发送给Manager,让Manager发送告警同步 */
  public static final String 发起告警同步申请OID = "*******.4.1.37405.1.1.3.0.5";

  /***当前告警表Table OID ********************************************************************************************************************************/
  public static final String alarmTableTrapOID = "*******.4.1.37405.1.1.4.1";

  /***实时告警OID  告警字段OID ****************************************************************************************************************************/
  public static final String alarmAID = "*******.4.1.37405.*******"; // alarm-AID

  public static final String alarmDAID = "*******.4.1.37405.*******"; // alarm-DAID
  public static final String alarmProperty = "*******.4.1.37405.*******"; // alarm-Property
  public static final String alarmSeries = "*******.4.1.37405.*******"; // alarm-Series
  public static final String alarmStyle = "*******.4.1.37405.*******"; // alarm-Style
  public static final String alarmEquipID = "*******.4.1.37405.*******"; // alarm-EquipID
  public static final String alarmModule1 = "*******.4.1.37405.*******"; // alarm-Module1
  public static final String alarmCard2 = "*******.4.1.37405.*******"; // alarm-Card2
  public static final String alarmPort3 = "*******.4.1.37405.*******"; // alarm-Port3
  public static final String alarmReserve4 = "*******.4.1.37405.*******0"; // alarm-Reserve4
  public static final String alarmSeverity = "*******.4.1.37405.*******1"; // alarm-Severity
  public static final String alarmStatus = "*******.4.1.37405.********"; // alarm-Status
  public static final String alarmDateTime = "*******.4.1.37405.*******3"; // alarm-DateTime
  public static final String alarmAlmNum = "*******.4.1.37405.*******4"; // alarm-AlmNum
  public static final String alarmAlmCategory = "*******.4.1.37405.*******5"; // alarm-AlmCategory
  public static final String alarmAlmText = "*******.4.1.37405.*******6"; // alarm-AlmText
  public static final String alarmFromShelf = "*******.4.1.37405.********"; // alarm-FromShelf
  public static final String alarmShelfLoc = "*******.4.1.37405.*******8"; // alarm-ShelfLoc
  public static final String alarmONAlmNum = "*******.4.1.37405.*******9"; // alarm-ONAlmNum
  public static final String alarmONAlmCategory = "*******.4.1.37405.*******0";

  /***********OID****************************************************************************************************************************************/
  public static AtomicInteger syncState = new AtomicInteger(0); // 0: 非同步，1: 同步

  public static Snmp snmp161 = null;
  public static Snmp snmp162 = null;

  // SNMP客户端配置 - 现在通过数据库配置管理，这里作为默认值
  public static String snmpClientIp = "*************";
  public static int snmpClientPort = 162;

  public static Map<String, ZHWGDevice> zhwgDeviceMap = new HashMap<>();
  public static Map<String, String> zhwgAlarmTypeMap = new HashMap<>();


}
