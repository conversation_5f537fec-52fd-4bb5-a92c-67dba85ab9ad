package com.key.win.snmpserver.setting;

import java.io.*;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class HeartbeatOIDSetting {
  private static final String FILE_PATH = "heartbeat.conf";
  private final AtomicInteger value = new AtomicInteger(60);

  public HeartbeatOIDSetting() {
    load();
  }

  public int get() {
    return value.get();
  }

  public synchronized boolean set(int newVal) {
    if (newVal >= 3 && newVal <= 300) {
      value.set(newVal);
      save();
      return true;
    }
    return false;
  }

  private void load() {
    File file = new File(FILE_PATH);
    if (!file.exists()) return;

    try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
      int v = Integer.parseInt(reader.readLine().trim());
      if (v >= 3 && v <= 300) {
        value.set(v);
      }
    } catch (Exception e) {
      log.error("Failed to load heartbeat value: " + e.getMessage());
    }
  }

  private void save() {
    try (BufferedWriter writer = new BufferedWriter(new FileWriter(FILE_PATH))) {
      writer.write(String.valueOf(value.get()));
    } catch (IOException e) {
      log.error("Failed to save heartbeat value: " + e.getMessage());
    }
  }
}
