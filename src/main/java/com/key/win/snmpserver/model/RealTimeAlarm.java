package com.key.win.snmpserver.model;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RealTimeAlarm {
  private Integer alarmAID; // *******.4.1.37405.*******
  private Integer alarmDAID; // *******.4.1.37405.*******
  private Integer alarmProperty; // *******.4.1.37405.*******
  private Integer alarmSeries; // *******.4.1.37405.*******
  private Integer alarmStyle; // *******.4.1.37405.*******
  private Integer alarmEquipID; // *******.4.1.37405.*******
  private String alarmModule1; // *******.4.1.37405.*******
  private String alarmCard2; // *******.4.1.37405.*******
  private String alarmPort3; // *******.4.1.37405.*******
  private String alarmReserve4; // *******.4.1.37405.*******0
  private Integer alarmSeverity; // *******.4.1.37405.*******1
  private Integer alarmStatus; // *******.4.1.37405.*******2
  private String alarmDateTime; // *******.4.1.37405.*******3
  private Long alarmAlmNum; // *******.4.1.37405.*******4
  private String alarmAlmCategory; // *******.4.1.37405.*******5
  private String alarmAlmText; // *******.4.1.37405.*******6
  private String alarmFromShelf; // *******.4.1.37405.*******7
  private String alarmShelfLoc; // *******.4.1.37405.*******8
  private Long alarmONAlmNum; // *******.4.1.37405.*******9
  private String alarmONAlmCategory; // *******.4.1.37405.********
}
