package com.key.win.snmpserver.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

@Data
@TableName("zhwg_device")
public class ZHWGDevice implements Serializable {

  static final long serialVersionUID = 1L;

  @TableId private Integer id;

  private Integer equipId;
  private Integer style;
  private String hostNum;

  private String antName;
  private String stationId;
  private Integer series;
}
