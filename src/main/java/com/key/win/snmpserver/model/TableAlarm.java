package com.key.win.snmpserver.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TableAlarm {
  private Integer currentAlarmAID;              // *******.4.1.3740 *******.1.1.1
  private Integer currentAlarmDAID;             // *******.4.1.3740 *******.1.1.2
  private Integer currentAlarmProperty;         // *******.4.1.3740 *******.1.1.3
  private Integer currentAlarmSeries;           // *******.4.1.3740 *******.1.1.4
  private Integer currentAlarmStyle;            // *******.4.1.3740 *******.1.1.5
  private Integer currentAlarmEquipID;          // *******.4.1.3740 *******.1.1.6
  private String  currentAlarmModule1;          // *******.4.1.3740 *******.1.1.7
  private String  currentAlarmCard2;            // *******.4.1.3740 *******.1.1.8
  private String  currentAlarmPort3;            // *******.4.1.3740 *******.1.1.9
  private String  currentAlarmReserve4;         // *******.4.1.3740 *******.1.1.10
  private Integer currentAlarmSeverity;         // *******.4.1.3740 *******.1.1.11
  private Integer currentAlarmStatus;           // *******.4.1.3740 *******.1.1.12
  private String  currentAlarmDateTime;         // *******.4.1.3740 *******.1.1.13
  private Long    currentAlarmAlmNum;           // *******.4.1.3740 *******.1.1.14
  private String  currentAlarmAlmCategory;      // *******.4.1.3740 *******.1.1.15
  private String  currentAlarmAlmText;          // *******.4.1.3740 *******.1.1.16
  private String  currentAlarmFromShelf;        // *******.4.1.3740 *******.1.1.17
  private String  currentAlarmShelfLoc;         // *******.4.1.3740 *******.1.1.18
  private Long    currentAlarmONAlmNum;         // *******.4.1.3740 *******.1.1.19
  private String  currentAlarmONAlmCategory;    // *******.4.1.3740 *******.1.1.20
}
