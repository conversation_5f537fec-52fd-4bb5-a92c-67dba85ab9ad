package com.key.win.snmpserver.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.key.win.common.web.MybatisID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;

/**
 * SNMP配置实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(callSuper = true)
@TableName("snmp_config")
@Entity(name = "snmp_config")
public class SnmpConfig extends MybatisID {

    /**
     * 配置项名称
     */
    private String configKey;

    /**
     * 配置项值
     */
    private String configValue;

    /**
     * 配置项描述
     */
    private String description;

    /**
     * 配置项类型 (IP, PORT, STRING, NUMBER)
     */
    private String configType;

    /**
     * 是否启用
     */
    @TableField("`enabled`")
    private Boolean enabled;

    /**
     * 排序
     */
    private Integer sortOrder;
}
