package com.key.win.mqtt.Scheduling.config.runnable;

import com.key.win.mqtt.Scheduling.config.runnable.service.ProbeReadingService;
import com.key.win.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 天线路损监测任务
 *
 * <AUTHOR>
 */
@Slf4j
public class ProbePathlossReadingRunnable implements Runnable {

  private String hostNum;

  public String getHostNum() {
    return hostNum;
  }

  public void setHostNum(String hostNum) {
    this.hostNum = hostNum;
  }

  @Override
  public void run() {
    ProbeReadingService bean = SpringUtils.getBean(ProbeReadingService.class);
    bean.AutoCheck(getHostNum());
  }
}
