package com.key.win.mqtt.Scheduling.config.runnable.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.biz.baseInfo.service.ProbeService;
import com.key.win.biz.topo.service.TopoService;
import com.key.win.config.mqtt.MqttPushClient;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.mqtt.mq.cmd.EmqRequestService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/** 天线链路监测 */
@Slf4j
@Service
public class ProbeLinkCheckService {

  @Autowired private EmqRequestService emqRequestService;

  @Autowired private BelongUnitService belongUnitService;

  @Autowired private TopoService topoService;

  @Autowired private ProbeService probeService;

  public void AutoCheck(String deviceId) {
    LambdaQueryWrapper<BelongUnit> lqw = new LambdaQueryWrapper<>();
    lqw.eq(BelongUnit::getEnableFlag, true);

    // 如果自检的设备ID存在不为空,则只对当前主机进行自检
    if (StringUtils.isNotBlank(deviceId)) {
      log.info("[Ctl]:[单采][链路诊断]==设备ID[{}]", deviceId);
      lqw.eq(BelongUnit::getHostNum, deviceId);
    } else {
      log.info("[Task]:[混检][链路诊断]>>>>>>>>>>>>>>>>");
    }

    List<BelongUnit> list = belongUnitService.list(lqw);
    int deviceCount = list.size();
    int onlineCount = 0;
    int offlineCount = 0;

    for (BelongUnit u : list) {

      deviceId = u.getHostNum();
      boolean isBan = belongUnitService.isBanByLineOrStation(deviceId);
      if (isBan) {
        log.warn(
            "[{}][ProbeLinkCheckService]当前主机的线路或者车站被禁用,当前自动跳过主机检测,或请检查数据库线路、车站的启用状态!", deviceId);
        continue;
      }

      boolean isOnline = emqRequestService.clientIsOnline("reader_" + deviceId);

      if (!isOnline) {
        log.warn("[链路诊断][{}][offline],[skip]", deviceId);
        offlineCount++;
        continue;
      } else {
        onlineCount++;
        int antOfflineCount = topoService.getAntNodeLinks(deviceId);
        Map<String, String> msgMap = new HashMap<>();
        msgMap.put("deviceId", deviceId);
        String msgJsonString = JSON.toJSONString(msgMap);

        if (antOfflineCount > 0) {
          MqttPushClient.getInstance()
              .publishDeviceProbeLinkCheck(
                  GConfig.MQ_TOPIC_DEVICE_LINK_ERR + deviceId, msgJsonString);
        } else {
          MqttPushClient.getInstance()
              .publishDeviceProbeLinkCheck(
                  GConfig.MQ_TOPIC_DEVICE_LINK_ONLINE + deviceId, msgJsonString);
        }
      }
      try {
        Thread.sleep(1000);
      } catch (InterruptedException e) {
        e.printStackTrace();
      }
    }
    if (deviceCount > 0) {
      log.info("[Task][链路诊断]结束,Waiting,在线[{}]台,离线[{}]台!", onlineCount, offlineCount);
    }
  }
}
