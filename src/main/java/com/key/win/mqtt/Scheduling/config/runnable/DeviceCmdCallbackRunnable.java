package com.key.win.mqtt.Scheduling.config.runnable;

import com.key.win.mqtt.Scheduling.config.runnable.service.DeviceArgumentSaveService;
import com.key.win.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DeviceCmdCallbackRunnable implements Runnable {

  private String deviceId;
  private String wsId;
  private long waitTimeSecond;

  public String getWsId() {
    return wsId;
  }

  public void setWsId(String wsId) {
    this.wsId = wsId;
  }

  public String getDeviceId() {
    return deviceId;
  }

  public void setDeviceId(String deviceId) {
    this.deviceId = deviceId;
  }

  public long getWaitTimeSecond() {
    return waitTimeSecond;
  }

  public void setWaitTimeSecond(long waitTimeSecond) {
    this.waitTimeSecond = waitTimeSecond;
  }

  @Override
  public void run() {
    try {
      Thread.sleep(getWaitTimeSecond() * 1000);
    } catch (InterruptedException e) {
      e.printStackTrace();
    }
    DeviceArgumentSaveService bean = SpringUtils.getBean(DeviceArgumentSaveService.class);
    bean.setWsId(getWsId());
    bean.AutoCheck(getDeviceId());
  }
}
