package com.key.win.mqtt.Scheduling.config.runnable.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.config.RedisUtil;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.mqtt.mq.cmd.EmqRequestService;
import com.key.win.mqtt.mq.cmd.QueryCommandService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ProbeReadingService {
  @Autowired private EmqRequestService emqRequestService;

  @Autowired private BelongUnitService belongUnitService;

  @Autowired private QueryCommandService queryCommandService;

  @Autowired private RedisUtil redisUtil;

  public void AutoCheck(String deviceId) {

    LambdaQueryWrapper<BelongUnit> lqw = new LambdaQueryWrapper<>();
    lqw.eq(BelongUnit::getEnableFlag, true);

    // 如果自检的设备ID存在不为空,则只对当前主机进行自检
    if (StringUtils.isNotBlank(deviceId)) {
      log.info("[Ctl]:[单采][路损查询]==设备ID[{}]", deviceId);
      lqw.eq(BelongUnit::getHostNum, deviceId);
    } else {
      log.info("[Task]:[混检][路损查询]>>>>>>>>>>>>>>>>");
    }

    List<BelongUnit> list = belongUnitService.list(lqw);
    int deviceCount = list.size();
    int onlineCount = 0;
    int offlineCount = 0;

    for (BelongUnit u : list) {

      deviceId = u.getHostNum();
      boolean isBan = belongUnitService.isBanByLineOrStation(deviceId);
      if (isBan) {
        log.warn("[{}][ProbeReadingService]当前主机的线路或者车站被禁用,当前自动跳过主机检测,或请检查数据库线路、车站的启用状态!", deviceId);
        continue;
      }
      boolean isOnline = emqRequestService.clientIsOnline("reader_" + deviceId);

      if (!isOnline) {
        log.warn("[路损查询][{}][Offline],[skip]", deviceId);
        offlineCount++;
        continue;
      } else {
        onlineCount++;
        boolean hasKey = redisUtil.hasKey(GConfig.SERVICE_BASE_KEY + deviceId);
        if (hasKey) {
          log.info("[路损查询][{}][Online]指令重复Ing...,[skip]", deviceId);
          continue;
        }

        String startPowerStr = StringUtils.defaultIfBlank(u.getStartPower(), "0");
        String endPowerStr = StringUtils.defaultIfBlank(u.getEndPower(), "0");
        int startPower = Integer.parseInt(startPowerStr);
        int endPower = Integer.parseInt(endPowerStr);
        long waitTime = (endPower - startPower + 1) * GConfig.getDeviceReadingWaitTimeSecond();
        redisUtil.set(GConfig.SERVICE_BASE_KEY + deviceId, "路损查询", waitTime);

        // 精确读取
        queryCommandService.accurateReadingByDeviceId(deviceId);
        log.info("[路损查询][{}]指令下发完毕.", deviceId);
      }
      try {
        Thread.sleep(1000);
      } catch (InterruptedException e) {
        e.printStackTrace();
      }
    }
    if (deviceCount > 0) {
      log.info("[Task][路损查询]结束,Waiting,在线[{}]台,离线[{}]台!", onlineCount, offlineCount);
    }
  }
}
