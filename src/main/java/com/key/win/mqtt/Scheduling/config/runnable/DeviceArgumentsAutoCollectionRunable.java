package com.key.win.mqtt.Scheduling.config.runnable;

import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.config.RedisUtil;
import com.key.win.mqtt.Scheduling.config.runnable.service.DeviceArgumentSaveService;
import com.key.win.mqtt.Scheduling.config.runnable.service.DeviceArgumentsCollectService;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.utils.SpringUtils;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * 设备参数信息自动收集任务 如果需要检测指定的设备,请通过传入参数hostId来指定设备 如果留空或者null则全设备检测
 *
 * <AUTHOR>
 */
@Slf4j
public class DeviceArgumentsAutoCollectionRunable implements Runnable {

  private String hostId;

  public String getHostId() {
    return hostId;
  }

  public void setHostId(String hostId) {
    this.hostId = hostId;
  }

  @Override
  public void run() {
    BelongUnitService belongUnitService = SpringUtils.getBean(BelongUnitService.class);
    long waitTimeSecond = GConfig.getDeviceInfoAsyncWaitTimeSecond();
    RedisUtil redisUtil = SpringUtils.getBean(RedisUtil.class);
    if (belongUnitService != null) {
      List<BelongUnit> list = belongUnitService.list();
      int deviceCount = list.size();
      if (deviceCount > 0) {
        waitTimeSecond = waitTimeSecond * list.size();
        log.info("共有{}台监控主机,任务约耗时:{}秒", list.size(), waitTimeSecond);
        log.info("开始执行任务收集调度程序==========>");
        if (redisUtil.hasKey(GConfig.TASK_SELF_CHECK)) {
          long expire = redisUtil.getExpire(GConfig.TASK_SELF_CHECK);
          log.info("定时调度任务正在执行,请{}秒后重试!", expire);
          return;
        }
        log.info("任务开始执行,期间相关指令禁用时间:{} s", waitTimeSecond);
        redisUtil.set(GConfig.TASK_SELF_CHECK, "1", waitTimeSecond);

        // 监控主机参数信息同步
        DeviceArgumentsCollectService service =
            SpringUtils.getBean(DeviceArgumentsCollectService.class);
        service.setSource("Task");
        service.AutoCheck(getHostId());
        try {
          Thread.sleep(waitTimeSecond * 1000);
        } catch (InterruptedException e) {
          log.info("设备参数同步总需耗时约:{}s", waitTimeSecond);
          throw new RuntimeException(e);
        }
        log.info("设备同步指令已完成,即将开始执行存入DB操作...");
        DeviceArgumentSaveService saveDB = SpringUtils.getBean(DeviceArgumentSaveService.class);
        saveDB.AutoCheck(hostId);
        log.info("设备参数信息同步完毕,已存入数据库!");

        redisUtil.del(GConfig.TASK_SELF_CHECK);
        log.info("设备信息收集任务调度执行完毕<<<<<<<<<");
      } else {
        log.info("没有录入的监控主机信息,当前任务执行完毕!");
      }
    }
  }
}
