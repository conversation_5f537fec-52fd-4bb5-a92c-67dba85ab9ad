package com.key.win.mqtt.Scheduling.config.runnable.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.config.RedisUtil;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.mqtt.mq.cmd.EmqRequestService;
import com.key.win.mqtt.mq.cmd.QueryCommandService;
import com.key.win.mqtt.mq.cmd.SettingCommandService;
import com.key.win.utils.HVersionEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 设备参数信息收集类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeviceArgumentsCollectService {

  @Autowired private EmqRequestService emqRequestService;

  @Autowired private BelongUnitService belongUnitService;

  @Autowired private QueryCommandService queryCommandService;

  @Autowired
  private SettingCommandService settingCommandService;

  @Autowired private RedisUtil redisUtil;

  private String source = "";

  /** 是否开启频率步进开关 */
  @Value("${sf.cmd.param.frequencyStep}")
  private boolean frequencyStepFlag = false;

  /** 是否开启开始频率 */
  @Value("${sf.cmd.param.frequencyStart}")
  private boolean frequencyStartFlag = false;

  /** 结束频率 */
  @Value("${sf.cmd.param.frequencyEnd}")
  private boolean frequencyEndFlag = false;

  public String getSource() {
    return source;
  }

  public void setSource(String source) {
    this.source = source;
  }

  public void AutoCheck(String deviceId) {
    LambdaQueryWrapper<BelongUnit> lqw = new LambdaQueryWrapper<>();
    lqw.eq(BelongUnit::getEnableFlag, true);

    // 如果自检的设备ID存在不为空,则只对当前主机进行自检
    if (StringUtils.isNotBlank(deviceId)) {
      log.info("[设备参数][Ctl]:[单检]==设备ID[{}]", deviceId);
      lqw.eq(BelongUnit::getHostNum, deviceId);
    } else {
      log.info("[设备参数][Task]:[混检]>>>>>>>>>>>>>>>>");
    }

    List<BelongUnit> list = belongUnitService.list(lqw);
    int deviceCount = list.size();
    int onlineCount = 0;
    int offlineCount = 0;

    for (BelongUnit u : list) {
      // 主机编码
      deviceId = u.getHostNum();

      boolean isBan = belongUnitService.isBanByLineOrStation(deviceId);
      if (isBan) {
        log.info(
            "[{}][DeviceArgumentsCollectService]当前主机的线路或者车站被禁用,当前自动跳过主机检测,或请检查数据库线路、车站的启用状态!",
            deviceId);
        continue;
      }

      boolean isOnline = emqRequestService.clientIsOnline("reader_" + deviceId);

      if (!isOnline) {
        log.warn("[设备参数][{}][Offline],[skip]", deviceId);
        offlineCount++;
        continue;
      } else {
        onlineCount++;
        boolean hasKey = redisUtil.hasKey(GConfig.SERVICE_BASE_KEY + deviceId);

        if (hasKey) {
          log.info("[设备参数][{}][Online]执行指令重复,[skip]", deviceId);
          continue;
        }
        redisUtil.set(
            GConfig.SERVICE_BASE_KEY + deviceId,
            "设备参数同步",
            GConfig.getDeviceInfoAsyncWaitTimeSecond());

        // 开始功率 结束功率 发射功率
        log.info("[{}]开始下发指令↓↓↓↓↓↓↓↓", deviceId);
        log.info("[Cmd]开始功率-结束功率-发射功率[start]");

        queryCommandService.startPower(deviceId);
        queryCommandService.endPower(deviceId);
        queryCommandService.launchPower(deviceId);

        // 版本号 生产系列号 设备型号
        log.info("[Cmd]版本号-生产系列号-设备型号");
        queryCommandService.softVersion(deviceId);
        queryCommandService.productSerVersion(deviceId);
        queryCommandService.deviceVersion(deviceId);

        // 服务器IP 端口号
        log.info("[Cmd]服务器IP-端口号");
        queryCommandService.lookIp(deviceId);
        queryCommandService.serverAno(deviceId);

        // 故障诊断时间
        log.info("[Cmd]故障诊断周期时间:============");
        queryCommandService.faultDiagnosisTime(deviceId);
        //send3Service.faultDiagnosisTime(deviceId);
        // 故障诊断周期
        //				log.info("[Cmd]故障诊断周期:============");
        //				send3Service.faultDiagnosisCycle(deviceId);
        // 故障诊断次数
        log.info("[Cmd]故障诊断次数:============");
        //send3Service.faultDiagnosisTimes(deviceId);
        queryCommandService.faultDiagnosisTimes(deviceId);

        // 告警判断次数
        log.info("[Cmd]告警判断次数:============");
        queryCommandService.alarmJudgeCount(deviceId);

        // 开始频率
        if (frequencyStepFlag) {
          log.info("[Cmd]开始频率");
          queryCommandService.frequencyStart(deviceId);
        }
        // 结束频率
        if (frequencyStartFlag) {
          log.info("[Cmd]结束频率");
          queryCommandService.frequencyEnd(deviceId);
        }
        // 频率步进
        if (frequencyEndFlag) {
          log.info("频率步进");
          queryCommandService.lookFrequencyStep(deviceId);
        }
        // 故障诊断开关
        //				log.info("[Cmd]故障诊断开关");
        //				send3Service.lookFaultSwitch(deviceId);
        // 精确读取开关
        //				log.info("[Cmd]精确读取开关");
        //				send3Service.lookAccurateReadingSwitch(deviceId);

        // 关闭精确读取开关
        log.info("[Cmd]关闭精确读取开关");
        queryCommandService.setAccurateReadingSwitch(deviceId);
        // 关闭故障诊断开关
        log.info("[Cmd]关闭故障诊断开关");
        queryCommandService.setFaultSwitch(deviceId);

        // 精确读取修正值
        log.info("[Cmd]精确读取修正值[End]");
        queryCommandService.lookAccuratelyReadTheCorrectionValue(deviceId);

        //查询功率步进
        log.info("[Cmd]查询功率步进[End]");
        queryCommandService.powerStep(deviceId);

        //设置设备时间（取网管系统时间）
        log.info("[Cmd]设置设备时间[End]");
        settingCommandService.setEquipmenTime(deviceId);

        //如果主机的设备代数是空或二代机时，则查询光交换机光口-状态-功率情况。
        if(StringUtils.isBlank(u.getHardwareVersion()) || HVersionEnum.V_TWO.getName().equals(u.getHardwareVersion())){
          log.info("[Cmd]光交换机光端口状态查询.");
          queryCommandService.queryOpticalPort(deviceId);

          log.info("[Cmd]光交换机光收功率查询.");
          queryCommandService.queryOpticalReceivePower_1(deviceId);
          queryCommandService.queryOpticalReceivePower_2(deviceId);

          log.info("[Cmd]光交换机光发功能查询.");
          queryCommandService.queryOpticalOutputPower_1(deviceId);
          queryCommandService.queryOpticalOutputPower_2(deviceId);
        }
        log.warn("[设备参数][{}]指令下发完毕.", deviceId);
      }
      try {
        Thread.sleep(1000);
      } catch (InterruptedException e) {
        e.printStackTrace();
      }
    }
    if (deviceCount > 0) {
      log.info("[Task][设备参数]结束,Waiting,在线[{}]台,离线[{}]台!", onlineCount, offlineCount);
    }
  }
}
