package com.key.win.mqtt.Scheduling.config.support;

import com.key.win.utils.SpringUtils;

public class GConfig {

  private static String nodeType = SpringUtils.getProperty("sf.tel.type");

  public static final String aliveKey = "ALIVED:";
  public static final String alarmType_deviceOffline = "设备离线";
  public static final String alarmType_antErr = "天线异常";
  public static final String alarmType_probeLinkErr = "链路异常";
  public static final String alarmType_probeSignalStrength = "信源偏弱";
  // 实时告警
  public static final String alarmStatus_realTime = "0";
  // 告警确认
  public static final String alarmStatus_confirm = "1";
  // 清除告警（历史告警）
  public static final String alarmStatus_His_clear = "2";
  public static final String equipmentType_machine = "固定主机";
  public static final String equipmentType_Other = "其他";
  public static final String equipmentType_Ant = "天线";
  public static final String hostKey = "SF:CACHEKEY:HOST:";
  public static final String probeKey = "SF:CACHEKEY:PROBE:";
  public static final String spticalKey = "SF:CACHEKEY:SPTICAL:";
  public static final String probeKeyPathLossCheck = "SF:CACHEKEY:PATHLOSS:PROBE:CHECK:";
  public static final String probeKeyPathLoss = "SF:CACHEKEY:PATHLOSS:PROBE:";
  public static final String device_status_online = "在线";
  public static final String device_status_offline = "离线";
  public static final String ANT_PATH_LOSS = "20";
  public static final String TASK_SELF_CHECK = "TASK_SELF_CHECK";
  public static final String SERVICE_BASE_KEY = "SERVICE_BASE_KEY:";
  public static final String CMD_TYPE_DEVICE_REBOOT = "reboot";
  public static final String CMD_TYPE_DEVICE_ARGUMENTINFO = "deviceInfo";
  public static final String CMD_TYPE_FAULT = "fault";
  public static final String CMD_TYPE_PATHLOSS = "pathloss";
  public static final String MQ_TOPIC_BASE = "sf/device/";
  public static final String MQ_TOPIC_DEVICE_ONLINE = MQ_TOPIC_BASE + "online/";
  public static final String MQ_TOPIC_DEVICE_OFFLINE = MQ_TOPIC_BASE + "offline/";
  public static final String MQ_TOPIC_DEVICE_HIS_ANT_LOSS = MQ_TOPIC_BASE + "his_ant_loss/";
  public static final String MQ_TOPIC_DEVICE_OPTICAL = MQ_TOPIC_BASE + "optical/";
  public static final String MQ_TOPIC_DEVICE_PROBE_SIGNAL_STRENGTH =
      MQ_TOPIC_BASE + "signalStrength/";
  public static final String MQ_TOPIC_DEVICE_LINK_ERR = MQ_TOPIC_BASE + "link/err";
  public static final String MQ_TOPIC_DEVICE_LINK_ONLINE = MQ_TOPIC_BASE + "link/online";
  public static final String MQ_TOPIC_DEVICE_ERR = MQ_TOPIC_BASE + "error/";
  public static final String MQ_TOPIC_PROBE_ERR = MQ_TOPIC_BASE + "prbe/error/";
  public static final String MQ_TOPIC_PROBE_ONLINE = MQ_TOPIC_BASE + "prbe/online/";
  public static final String MQ_TOPIC_PROBE_PATHLOSS = MQ_TOPIC_BASE + "prbe/pathloss/";

  public static final String MQ_TOPIC_CSHARP_CLIENT = "sf/alert/";
  public static final String MQ_TOPIC_CSHARP_CLIENT_ALERT_ASYNC = "sf/alert/async/" + nodeType;
  public static final String MQ_TOPIC_CSHARP_CLIENT_ALERT_INFO = "sf/alert/info/" + nodeType;

  public static final String MQ_TOPIC_CSHARP_CLIENT_START = "sf/alert/start/" + nodeType;
  public static final String MQ_TOPIC_CSHARP_CLIENT_END = "sf/alert/end/" + nodeType;

  /** 上报标识符 1：不用上报 0：需要上报 */
  public static final String ALARM_REPORT_FLAG_1 = "1";

  public static final String ALARM_REPORT_FLAG_0 = "0";
  public static final String PROBE_CHECK_KEY = "probeCheck:";
  public static final long PROBE_CHECK_KEY_EXPIRED_TIME = 1 * 24 * 60 * 60;
  private static long offlineMin = 2;
  private static long deviceInfoAsyncWaitTimeSecond = 15;
  private static long deviceFaultWaitTimeSecond = 20;
  private static long deviceReadingWaitTimeSecond = 10;
  private static long deviceRebootWaitTimeSecond = 60;
  private static long antDeviationValue = 120;

  /** 离线时间（分钟） 只要大于离线约定时间，及判定为监控主机离线状态 默认是2分钟 */
  public static long getOfflineMin() {
    return offlineMin;
  }

  public static void setOfflineMin(long offlineMin) {
    GConfig.offlineMin = offlineMin;
  }

  /** 设备信息同步时,每一台主机需要的耗时(s) */
  public static long getDeviceInfoAsyncWaitTimeSecond() {
    return deviceInfoAsyncWaitTimeSecond;
  }

  public static void setDeviceInfoAsyncWaitTimeSecond(long deviceInfoAsyncWaitTimeSecond) {
    GConfig.deviceInfoAsyncWaitTimeSecond = deviceInfoAsyncWaitTimeSecond;
  }

  /** 设备故障诊断时,每一台主机需要的耗时(s) */
  public static long getDeviceFaultWaitTimeSecond() {
    return deviceFaultWaitTimeSecond;
  }

  public static void setDeviceFaultWaitTimeSecond(long deviceFaultWaitTimeSecond) {
    GConfig.deviceFaultWaitTimeSecond = deviceFaultWaitTimeSecond;
  }

  /** 设备路损诊断时,每一台主机1db需要的耗时(s) */
  public static long getDeviceReadingWaitTimeSecond() {
    return deviceReadingWaitTimeSecond;
  }

  public static void setDeviceReadingWaitTimeSecond(long deviceReadingWaitTimeSecond) {
    GConfig.deviceReadingWaitTimeSecond = deviceReadingWaitTimeSecond;
  }

  public static long getDeviceRebootWaitTimeSecond() {
    return deviceRebootWaitTimeSecond;
  }

  public static void setDeviceRebootWaitTimeSecond(long deviceRebootWaitTimeSecond) {
    GConfig.deviceRebootWaitTimeSecond = deviceRebootWaitTimeSecond;
  }

  public static long getAntDeviationValue() {
    return antDeviationValue;
  }

  public static void setAntDeviationValue(long antDeviationValue) {
    GConfig.antDeviationValue = antDeviationValue;
  }
}
