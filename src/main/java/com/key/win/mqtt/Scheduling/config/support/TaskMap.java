package com.key.win.mqtt.Scheduling.config.support;

import com.key.win.mqtt.Scheduling.config.runnable.DeviceArgumentsAutoCollectionRunable;
import com.key.win.mqtt.Scheduling.config.runnable.DeviceStatusMonitoringRunable;
import com.key.win.mqtt.Scheduling.config.runnable.ProbeLinkCheckRunnable;
import com.key.win.mqtt.Scheduling.config.runnable.ProbePathlossReadingRunnable;
import com.key.win.mqtt.Scheduling.config.runnable.ProbeSignalStrengthCheckRunnable;
import com.key.win.mqtt.Scheduling.config.runnable.ProbeStatusCheckRunnable;
import java.util.HashMap;
import java.util.Map;

public class TaskMap {

  /** 心跳、设备离线任务 */
  private static final String DEVICE_HEART_BEAT_CHECK_TASK = "10";

  /** 设备参数信息同步 */
  private static final String DEVICE_SELFCHECK_TASK = "20";

  /** 故障诊断任务 */
  private static final String DEVICE_PROBE_STATAUS_CHECK_TASK = "30";

  /** 路损查询 */
  private static final String DEVICE_PROBE_PATHLOSS_READING_TASK = "40";

  /** 链路监测 */
  private static final String DEVICE_PROBE_LINK_CHECK_TASK = "50";

  /*信源强度监测*/
  private static final String DEVICE_PROBE_SIGNAL_STRING_TASK = "60";

  private static final Map<String, Runnable> taskMap = new HashMap<String, Runnable>();

  static {
    taskMap.put(DEVICE_HEART_BEAT_CHECK_TASK, new DeviceStatusMonitoringRunable());
    taskMap.put(DEVICE_SELFCHECK_TASK, new DeviceArgumentsAutoCollectionRunable());
    taskMap.put(DEVICE_PROBE_STATAUS_CHECK_TASK, new ProbeStatusCheckRunnable());
    taskMap.put(DEVICE_PROBE_PATHLOSS_READING_TASK, new ProbePathlossReadingRunnable());
    taskMap.put(DEVICE_PROBE_LINK_CHECK_TASK, new ProbeLinkCheckRunnable());
    taskMap.put(DEVICE_PROBE_SIGNAL_STRING_TASK, new ProbeSignalStrengthCheckRunnable());
  }

  public static String getDeviceHeartBeatCheckTask() {
    return DEVICE_HEART_BEAT_CHECK_TASK;
  }

  public static String getDeviceSelfcheckTask() {
    return DEVICE_SELFCHECK_TASK;
  }

  public static Map<String, Runnable> getTaskmap() {
    return taskMap;
  }

  public static Runnable getRunnable(String taskId) {
    Runnable runnable = getTaskmap().get(taskId);
    return runnable;
  }
}
