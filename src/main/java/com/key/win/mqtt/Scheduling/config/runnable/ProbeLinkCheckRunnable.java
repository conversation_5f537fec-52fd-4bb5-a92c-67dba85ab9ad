package com.key.win.mqtt.Scheduling.config.runnable;

import com.key.win.mqtt.Scheduling.config.runnable.service.ProbeLinkCheckService;
import com.key.win.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 天线链路监测
 *
 * <AUTHOR>
 */
@Slf4j
public class ProbeLinkCheckRunnable implements Runnable {

  private String hostNum;

  public String getHostNum() {
    return hostNum;
  }

  public void setHostNum(String hostNum) {
    this.hostNum = hostNum;
  }

  @Override
  public void run() {
    ProbeLinkCheckService bean = SpringUtils.getBean(ProbeLinkCheckService.class);
    bean.AutoCheck(getHostNum());
  }
}
