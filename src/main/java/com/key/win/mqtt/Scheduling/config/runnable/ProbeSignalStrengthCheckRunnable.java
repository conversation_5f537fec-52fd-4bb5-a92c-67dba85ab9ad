package com.key.win.mqtt.Scheduling.config.runnable;

import com.key.win.mqtt.Scheduling.config.runnable.service.ProbeSignalStrengthCheckService;
import com.key.win.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 天线链路监测
 *
 * <AUTHOR>
 */
@Slf4j
public class ProbeSignalStrengthCheckRunnable implements Runnable {

  private String hostNum;

  public String getHostNum() {
    return hostNum;
  }

  public void setHostNum(String hostNum) {
    this.hostNum = hostNum;
  }

  @Override
  public void run() {
    ProbeSignalStrengthCheckService bean =
        SpringUtils.getBean(ProbeSignalStrengthCheckService.class);
    bean.AutoCheck(getHostNum());
  }
}
