package com.key.win.mqtt.Scheduling.config.runnable.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.config.RedisUtil;
import com.key.win.config.mqtt.MqttPushClient;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.mqtt.mq.cmd.EmqRequestService;
import com.key.win.system.config.sf.model.SfGlobalConfig;
import com.key.win.system.config.sf.service.ISfGlobalConfigService;
import com.key.win.system.config.sf.support.SfGlobalConfigString;
import com.key.win.system.config.sf.support.SfGlobalConfigType;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 设备状态监测,对设备状态的变化进行离线告警后者告警清除
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeviceStatusMonitoringService {

  private String alivedCacheKey = GConfig.aliveKey;

  @Autowired private BelongUnitService belongUnitService;

  @Autowired private ISfGlobalConfigService sfGlobalConfigService;

  @Autowired private RedisUtil redisUtil;

  @Autowired private EmqRequestService emqRequestService;

  public void monitoring() {
    List<BelongUnit> devicesList = belongUnitService.list();
    LambdaQueryWrapper<SfGlobalConfig> lqw = new LambdaQueryWrapper<SfGlobalConfig>();
    lqw.eq(
        SfGlobalConfig::getType, SfGlobalConfigType.CONFIG_DEVICE_OFFLINE_TIME_SETTING.toString());
    SfGlobalConfig cfgBean = sfGlobalConfigService.getOne(lqw);
    if (cfgBean != null) {
      JSONObject object = JSON.parseObject(cfgBean.getConfig());
      long dbOfflineTime =
          object.getObject(SfGlobalConfigString.ConfigMapKey_deviceOffLineTime, Long.class);
      GConfig.setOfflineMin(dbOfflineTime);
      log.info("[配置覆盖]设备离线时长告警配置为:{}分钟", GConfig.getOfflineMin());
    } else {
      log.info("[默认配置]设备离线时长告警配置为:{}分钟", GConfig.getOfflineMin());
    }

    // 当前时间,用来收集设备离线时长
    Date currentDate = new Date();
    for (BelongUnit device : devicesList) {

      boolean isBan = belongUnitService.isBanByLineOrStation(device.getHostNum());
      if (isBan) {
        log.warn(
            "[{}][DeviceStatusMonitoringService]当前主机的线路或者车站被禁用,当前自动跳过主机检测,或请检查数据库线路、车站的启用状态!",
            device.getHostNum());
        continue;
      }

      // 主机编号
      String deviceId = device.getHostNum();
      Map<String, String> msgMap = new HashMap<>();
      msgMap.put("deviceId", deviceId);
      String alivedKey = alivedCacheKey + deviceId;
      if (redisUtil.hasKey(alivedKey)) {
        // 设备最后一次的发送心跳时间
        Date deviceLastHeartBeatTime = (Date) redisUtil.get(alivedKey);
        // 获取当前时间与设备主机上次在线时间的时间差
        long offlineTime = currentDate.getTime() - deviceLastHeartBeatTime.getTime();
        msgMap.put("offlineTime", String.valueOf(offlineTime));
        String msgJsonString = JSON.toJSONString(msgMap);

        boolean isOnline = emqRequestService.clientIsOnline("reader_" + deviceId);
        if (isOnline) {
          MqttPushClient.getInstance()
              .publishDeviceOnlineOrOffline(
                  GConfig.MQ_TOPIC_DEVICE_ONLINE + deviceId, msgJsonString);
        } else {
          if (offlineTime > GConfig.getOfflineMin() * 60 * 1000) {
            MqttPushClient.getInstance()
                .publishDeviceOnlineOrOffline(
                    GConfig.MQ_TOPIC_DEVICE_OFFLINE + deviceId, msgJsonString);
          }
        }

      } else {
        String msgJsonString = JSON.toJSONString(msgMap);
        MqttPushClient.getInstance()
            .publishDeviceOnlineOrOffline(GConfig.MQ_TOPIC_DEVICE_ERR + deviceId, msgJsonString);
      }
    }
  }
}
