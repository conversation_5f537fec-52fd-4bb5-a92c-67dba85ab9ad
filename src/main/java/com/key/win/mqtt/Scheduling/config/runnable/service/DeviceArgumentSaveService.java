package com.key.win.mqtt.Scheduling.config.runnable.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.config.RedisUtil;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.mqtt.websocket.server.WebSocketServer;
import com.key.win.utils.HVersionEnum;
import com.key.win.utils.SpringBeanUtilsExt;
import java.util.Map;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * 333333333333333 将主机参数信息从redis中持久化到数据库中
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeviceArgumentSaveService {
  /** 二代机软件版本号的前缀固定值（仅用于判断二代机设备的标记） **/
  public static final String CODE_PRE_SOFT_VERSION_II_DEVICE ="RFIDIIT113";

  private String wsId;
  @Autowired private RedisTemplate<String, Object> redisTemplate;
  @Autowired private BelongUnitService belongUnitService;

  /** 是否开启不存在设备时,将探针信息存储数据库开关 */
  @Value("${sf.collection.probe.addWithoutDbData}")
  private boolean addWithoutDbData = false;

  @Autowired private RedisUtil redisUtil;

  public String getWsId() {
    if (StringUtils.isBlank(wsId)) {
      return "Anonymous";
    }
    return wsId;
  }

  public void setWsId(String wsId) {
    this.wsId = wsId;
  }

  public void AutoCheck(String hostNum) {
    String hostKey = GConfig.hostKey;

    Set<String> hostKeys = null;
    if (StringUtils.isNoneBlank(hostNum)) {
      hostKeys = redisTemplate.keys(hostKey + hostNum);
    } else {
      hostKeys = redisTemplate.keys(hostKey + "*");
    }

    /** 系统对所有指令自检完成之后,封装到redis中的数据在此处进行DB持久化存储 */
    // 主机信息存储
    for (String key : hostKeys) {
      String[] _arr = key.split(":");
      // 监控主机编号
      hostNum = _arr[_arr.length - 1];

      boolean isBan = belongUnitService.isBanByLineOrStation(hostNum);
      if (isBan) {
        log.warn(
            "[{}][DeviceArgumentsAutoCollectionRunnable]当前主机的线路或者车站被禁用,当前自动跳过主机检测,或请检查数据库线路、车站的启用状态!",
            hostNum);
        continue;
      }

      Map<String, Object> hmget = redisUtil.hmget2(key);
      // 监控主机对象
      String hmgetJsonStr = JSON.toJSONString(hmget);
      log.info("当前设备ID:{},设备参数信息:{}", hostNum, hmgetJsonStr);
      BelongUnit hostUnit = JSON.parseObject(hmgetJsonStr, BelongUnit.class);
      hostUnit.setHostNum(hostNum);

      LambdaQueryWrapper<BelongUnit> lqw = new LambdaQueryWrapper<BelongUnit>();
      lqw.eq(BelongUnit::getHostNum, hostNum);
      BelongUnit one = belongUnitService.getOne(lqw);
      if (one != null) {
        String softwareVersion = one.getSoftwareVersion();
        SpringBeanUtilsExt.copyProperties(hostUnit, one);
        if(StringUtils.isNotBlank(softwareVersion) && softwareVersion.startsWith(CODE_PRE_SOFT_VERSION_II_DEVICE)){
            one.setHardwareVersion(HVersionEnum.V_TWO.getName());
        }else{
            one.setHardwareVersion(HVersionEnum.V_ONE.getName());
        }
        belongUnitService.updateById(one);
        log.info("[设备参数][DB][{}]更新完毕.", one.getHostNum());
      }
    }
    WebSocketServer.send("主机参数信息已同步到最新状态.", getWsId());
  }
}
