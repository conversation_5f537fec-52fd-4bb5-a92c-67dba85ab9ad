package com.key.win.mqtt.Scheduling.config.runnable;

import com.key.win.mqtt.Scheduling.config.runnable.service.DeviceStatusMonitoringService;
import com.key.win.utils.SpringUtils;

/**
 * 设备状态自动监测告警任务
 *
 * <AUTHOR>
 */
public class DeviceStatusMonitoringRunable implements Runnable {

  @Override
  public void run() {
    DeviceStatusMonitoringService bean = SpringUtils.getBean(DeviceStatusMonitoringService.class);
    bean.monitoring();
  }
}
