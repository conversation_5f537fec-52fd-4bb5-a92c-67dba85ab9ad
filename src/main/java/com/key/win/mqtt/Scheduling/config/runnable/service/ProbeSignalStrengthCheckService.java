package com.key.win.mqtt.Scheduling.config.runnable.service;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.model.Probe;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.biz.baseInfo.service.ISignalStrengthService;
import com.key.win.config.RedisUtil;
import com.key.win.config.mqtt.MqttPushClient;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.mqtt.mq.cmd.EmqRequestService;
import com.key.win.system.config.sf.model.SfGlobalConfig;
import com.key.win.system.config.sf.service.ISfGlobalConfigService;
import com.key.win.system.config.sf.support.SfGlobalConfigString;
import com.key.win.system.config.sf.support.SfGlobalConfigType;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/** 天线信源强度监测 */
@Slf4j
@Service
public class ProbeSignalStrengthCheckService {

  @Autowired private EmqRequestService emqRequestService;
  @Autowired private BelongUnitService belongUnitService;
  @Autowired private RedisUtil redisUtil;
  @Autowired private ISfGlobalConfigService sfGlobalConfigService;
  @Autowired private ISignalStrengthService signalStrengthService;

  public void AutoCheck(String deviceId) {

    SfGlobalConfig configBean =
        sfGlobalConfigService.getConfigByType(
            SfGlobalConfigType.CONFIG_ANT_DEVIATION_VALUE_SETTING.toString());
    Map configMap = JSON.parseObject(configBean.getConfig(), Map.class);
    Long deviationValue =
        MapUtil.getLong(
            configMap,
            SfGlobalConfigString.ConfigMapKey_ANT_deviationValue,
            GConfig.getAntDeviationValue());
    Boolean enabled = MapUtil.getBool(configMap, SfGlobalConfigString.ConfigMapKey_enabled, false);

    if (!enabled) {
      log.info("[Task]:[信源强度诊断]==当前配置项暂未启用,忽略本次调度任务!!!!!");
      return;
    }

    LambdaQueryWrapper<BelongUnit> lqw = new LambdaQueryWrapper<>();
    lqw.eq(BelongUnit::getEnableFlag, true);

    // 如果自检的设备ID存在不为空,则只对当前主机进行自检
    if (StringUtils.isNotBlank(deviceId)) {
      log.info("[Ctl]:[单采][信源强度诊断]==设备ID[{}]", deviceId);
      lqw.eq(BelongUnit::getHostNum, deviceId);
    } else {
      log.info("[Task]:[混检][信源强度诊断]>>>>>>>>>>>>>>>>");
    }

    List<BelongUnit> list = belongUnitService.list(lqw);
    int deviceCount = list.size();
    int onlineCount = 0;
    int offlineCount = 0;

    for (BelongUnit u : list) {
      deviceId = u.getHostNum();
      boolean isBan = belongUnitService.isBanByLineOrStation(deviceId);
      if (isBan) {
        log.warn(
            "[{}][ProbeSignalStrengthCheckService]当前主机的线路或者车站被禁用,当前自动跳过主机检测,或请检查数据库线路、车站的启用状态!",
            deviceId);
        continue;
      }

      boolean isOnline = emqRequestService.clientIsOnline("reader_" + deviceId);

      if (!isOnline) {
        log.warn("[信源强度诊断][{}][offline],[skip]", deviceId);
        offlineCount++;
        continue;
      } else {
        onlineCount++;

        List<Map> maps =
            signalStrengthService.antennaListByHostNumber(deviceId); // 获取当前监控主机下的所有信源强度基准值

        for (Map map : maps) {
          String sid = MapUtil.getStr(map, "sid");
          String antName = MapUtil.getStr(map, "antName");
          Double signalStrength = MapUtil.getDouble(map, "signalStrength"); // 信源监测后,每个探针的信源强度基准值
          // 如果当前探针不存在基准值,则无需进行预警,跳过本次进入下一个
          if (signalStrength == null) {
            continue;
          }

          // 根据天线的编写的序号,查找对应序号的探针
          Map<String, Object> probeInfoMap =
              redisUtil.hmget2(GConfig.probeKey + deviceId + ":" + sid);
          String probeInfoString = JSON.toJSONString(probeInfoMap);
          Probe probe = JSON.parseObject(probeInfoString, Probe.class);
          // 如果没有获取到探针,只有一种可能,天线TOPO中有该序号的天线,但是设备中是没有探针.则放弃当前所有操作
          // 所有的操作均是以topo图里面的天线为基准,允许设备中存在冗余的探针序号
          if (probe == null) {
            continue;
          }

          int lost = probe.getLost();
          // deviationValue 偏离值（参数配置中配置的信息）; signalStrength信源强度基准值（信源强度诊断采集的最小值）
          double limitLost = deviationValue + signalStrength; // 限制值,当路损超出该范围，则开始计入告警
          if (lost > 0 && lost < 120) { // 表示还没有收集到路损或者设备离线（0未收集到;120 探针异常）
            Map<String, Object> msgMap = new HashMap<>();
            msgMap.put("deviceId", deviceId);
            msgMap.put("sid", sid);
            msgMap.put("lost", lost);
            msgMap.put("antName", antName);
            msgMap.put("limitLost", limitLost);
            msgMap.put("lineId", u.getLineId());
            msgMap.put("lineName", u.getLineName());
            msgMap.put("stationId", u.getStationId());
            msgMap.put("stationName", u.getStationName());
            String msgJsonString = JSON.toJSONString(msgMap);
            MqttPushClient.getInstance()
                .publishDeviceProbeSignalStrengthCheck(
                    GConfig.MQ_TOPIC_DEVICE_PROBE_SIGNAL_STRENGTH + deviceId, msgJsonString);
            log.info(
                "[信源强度诊断][{}][{}]配置偏离值:{},基准值:{},阈值:{},当前路损值为:{},推送给MQ进行处理.",
                deviceId,
                sid,
                deviationValue,
                signalStrength,
                limitLost,
                lost);
          } else {
            log.info(
                "[信源强度诊断][{}][{}]配置偏离值:{},基准值:{},阈值:{},当前路损值为:{},非正常区间[0~119(正常区间)],区间异常,忽略本次！",
                deviceId,
                sid,
                deviationValue,
                signalStrength,
                limitLost,
                lost);
          }
        }
      }
      try {
        Thread.sleep(1000);
      } catch (InterruptedException e) {
        e.printStackTrace();
      }
    }
    if (deviceCount > 0) {
      log.info("[Task][信源强度诊断]结束,Waiting,在线[{}]台,离线[{}]台!", onlineCount, offlineCount);
    }
  }
}
