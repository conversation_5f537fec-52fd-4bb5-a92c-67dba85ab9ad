package com.key.win.mqtt.Scheduling.config.runnable;

import com.key.win.mqtt.Scheduling.config.runnable.service.ProbeFaultDiagnosisService;
import com.key.win.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 天线状态监测任务
 *
 * <AUTHOR>
 */
@Slf4j
public class ProbeStatusCheckRunnable implements Runnable {

  private String hostNum;

  public String getHostNum() {
    return hostNum;
  }

  public void setHostNum(String hostNum) {
    this.hostNum = hostNum;
  }

  @Override
  public void run() {
    ProbeFaultDiagnosisService bean = SpringUtils.getBean(ProbeFaultDiagnosisService.class);
    bean.AutoCheck(getHostNum());
  }
}
