package com.key.win.mqtt.Scheduling.config.runnable.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.config.RedisUtil;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.mqtt.mq.cmd.EmqRequestService;
import com.key.win.mqtt.mq.cmd.QueryCommandService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ProbeFaultDiagnosisService {

  @Autowired private RedisUtil redisUtil;

  @Autowired private EmqRequestService emqRequestService;

  @Autowired private BelongUnitService belongUnitService;

  @Autowired private QueryCommandService queryCommandService;

  public void AutoCheck(String deviceId) {
    LambdaQueryWrapper<BelongUnit> lqw = new LambdaQueryWrapper<>();
    lqw.eq(BelongUnit::getEnableFlag, true);

    // 如果自检的设备ID存在不为空,则只对当前主机进行自检
    if (StringUtils.isNotBlank(deviceId)) {
      log.info("[Ctl]:[单采][故障诊断]==设备ID[{}]", deviceId);
      lqw.eq(BelongUnit::getHostNum, deviceId);
    } else {
      log.info("[Task]:[混检][故障诊断]>>>>>>>>>>>>>>>>");
    }

    List<BelongUnit> list = belongUnitService.list(lqw);
    int deviceCount = list.size();
    int onlineCount = 0;
    int offlineCount = 0;

    for (BelongUnit u : list) {
      // 主机编码
      deviceId = u.getHostNum();

      boolean isBan = belongUnitService.isBanByLineOrStation(deviceId);
      if (isBan) {
        log.warn(
            "[{}][ProbeFaultDiagnosisService]当前主机的线路或者车站被禁用,当前自动跳过主机检测,或请检查数据库线路、车站的启用状态!",
            deviceId);
        continue;
      }

      boolean isOnline = emqRequestService.clientIsOnline("reader_" + deviceId);

      if (!isOnline) {
        log.warn("[故障诊断][{}][offline],[skip]", deviceId);
        offlineCount++;
        continue;
      } else {
        boolean hasKey = redisUtil.hasKey(GConfig.SERVICE_BASE_KEY + deviceId);
        if (hasKey) {
          log.info("[故障诊断][{}][Online]指令重复,[skip]", deviceId);
          continue;
        }
        redisUtil.set(
            GConfig.SERVICE_BASE_KEY + deviceId, "故障诊断", GConfig.getDeviceFaultWaitTimeSecond());

        onlineCount++;
        // 故障诊断
        queryCommandService.faultDiagnosisByDeviceId(deviceId);
        log.info("[故障诊断][{}]下发给主机...", deviceId);
      }
      try {
        Thread.sleep(1000);
      } catch (InterruptedException e) {
        e.printStackTrace();
      }
    }
    if (deviceCount > 0) {
      log.info("[Task][故障诊断]结束,Waiting,在线[{}]台,离线[{}]台!", onlineCount, offlineCount);
    }
  }
}
