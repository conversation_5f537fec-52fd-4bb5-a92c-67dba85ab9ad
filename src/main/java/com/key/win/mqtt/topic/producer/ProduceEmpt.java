package com.key.win.mqtt.topic.producer;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/10/22 20:05
 */
@Slf4j
public class ProduceEmpt implements Runnable {

  private int wait;

  @SneakyThrows
  @Override
  public void run() {
    synchronized (this) {
      wait(wait);
    }

    log.debug("等待{}秒", wait / 1000);
  }

  public ProduceEmpt(int wait) {
    this.wait = wait;
  }
}
