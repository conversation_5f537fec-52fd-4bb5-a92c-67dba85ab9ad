package com.key.win.mqtt.topic.producer;

import com.key.win.config.mqtt.MqttPushClient;

import lombok.SneakyThrows;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/10/7 23:09
 */
public class ProduceService implements Runnable {

  private MqttPushClient mqttPushClient;

  private String topic;
  private String fullCmds;
  private int wait;

  @SneakyThrows
  @Override
  public void run() {
    mqttPushClient.publishWaitTime(0, false, "reader_" + topic, fullCmds, wait);
    //        mqttPushClient.publish(0, false, "reader_" + topic, fullCmds);
  }

  public ProduceService(MqttPushClient mqttPushClient, String topic, String fullCmds, int wait) {
    this.mqttPushClient = mqttPushClient;
    this.topic = topic;
    this.fullCmds = fullCmds;
    this.wait = wait;
  }
}
