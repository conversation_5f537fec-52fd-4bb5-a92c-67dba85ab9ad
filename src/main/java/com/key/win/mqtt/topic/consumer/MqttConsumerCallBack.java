package com.key.win.mqtt.topic.consumer;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.common.web.Result;
import com.key.win.config.mqtt.MqttSubClient;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.mqtt.mq.parser.*;
import com.key.win.mqtt.mq.parser.csharp.CsharpClientParser;
import com.key.win.mqtt.mq.parser.probe.*;
import com.key.win.mqtt.websocket.server.WebSocketServer;
import com.key.win.utils.ByteUtil;
import com.key.win.utils.SpringSecurityUtils;
import com.key.win.utils.SpringUtils;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallbackExtended;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.core.env.Environment;

/**
 * @Author: cc @Date: 2021/7/30 17:06 @Description:
 */
@Slf4j
public class MqttConsumerCallBack implements MqttCallbackExtended {

    /// ////////////////////////////////
    private static Integer core_pool_size = 6;
    private static Integer max_pool_size = 12;
    private static Integer queue_capacity = 500;

    private static final ExecutorService messageExecutor =
            new ThreadPoolExecutor(
                    core_pool_size,
                    max_pool_size,
                    60L,
                    TimeUnit.SECONDS,
                    new LinkedBlockingQueue<>(queue_capacity),
                    new ThreadPoolExecutor.CallerRunsPolicy());

    /// ////////////////////////////////

    private static BelongUnitService belongUnitService = SpringUtils.getBean(BelongUnitService.class);

    private static Environment environment = SpringUtils.getBean(Environment.class);

    /**
     * 客户端断开连接的回调
     *
     * @param throwable
     * @return void
     * <AUTHOR>
     * @date 2021/7/30 17:14
     */
    @Override
    public void connectionLost(Throwable throwable) {
        log.error("MQTT连接断开: {}", throwable.getMessage(), throwable);
        // 记录断开连接的时间
        log.info("连接断开时间: {}", new Date());
        // 通知相关组件连接已断开
        notifyConnectionLost();
    }

    /** 通知其他组件连接已断开 */
    private void notifyConnectionLost() {
        // 通知WebSocket客户端
        try {
            Result<Object> result = Result.succeed("[系统通知]MQTT连接断开，请检查网络连接");
            if (SpringSecurityUtils.getUserName() != null) {
                WebSocketServer.sendInfo(JSON.toJSONString(result), SpringSecurityUtils.getUserName());
            }
        } catch (Exception e) {
            log.warn("通知WebSocket客户端连接断开失败: {}", e.getMessage());
        }
    }

    /**
     * 消息到达的回调
     *
     * @param topic
     * @param message
     * @return void
     * <AUTHOR>
     * @date 2021/7/30 17:14
     */
    @Override
    @SuppressWarnings("rawtypes")
    public void messageArrived(String topic, MqttMessage message) throws Exception {
        // 提交到线程池异步处理，避免阻塞MQTT客户端
        messageExecutor.submit(
                () -> {
                    try {
                        processMessage(topic, message);
                    } catch (Exception e) {
                        log.error("处理MQTT消息时发生异常: topic={}", topic, e);
                    }
                });
    }

    private void processMessage(String topic, MqttMessage message) throws IOException {
        String _vl = environment.getProperty("sf.tel.enable");
        boolean vl = Boolean.parseBoolean(_vl);
        //		String 设备离线TOPIC = "$SYS/brokers/+/clients/+/disconnected";
        //		String 设备上线TOPIC = "$SYS/brokers/+/clients/+/connected";
        // 离线
        if (topic.startsWith("$SYS/brokers/") && topic.endsWith("/disconnected")) {
            String payload = new String(message.getPayload());
            Map payloadMap = JSON.parseObject(payload, Map.class);
            String clientId = MapUtils.getString(payloadMap, "clientid");
            if (clientId.startsWith("reader_")) {
                log.info("EmqX:主机[{}]离线×", clientId);
                belongUnitService.offline(clientId.replace("reader_", ""));
                Result<Object> succeed =
                        Result.succeed("[WS播报]主机" + clientId.replace("reader_", "") + "离线!");
                if (SpringSecurityUtils.getUserName() != null) {
                    WebSocketServer.sendInfo(JSON.toJSONString(succeed), SpringSecurityUtils.getUserName());
                }
            } else {
                log.info("EmqX:客户端{}离线×", clientId);
            }
        } else if (topic.startsWith("$SYS/brokers/") && topic.endsWith("/connected")) {
            String payload = new String(message.getPayload());
            Map payloadMap = JSON.parseObject(payload, Map.class);
            String clientId = MapUtils.getString(payloadMap, "clientid");
            if (clientId.startsWith("reader_")) {
                log.info("EmqX:主机[{}]已连线√", clientId);
                belongUnitService.online(clientId.replace("reader_", ""));
                Result<Object> succeed =
                        Result.succeed("[WS播报]主机" + clientId.replace("reader_", "") + "连接成功!");
                if (SpringSecurityUtils.getUserName() != null) {
                    WebSocketServer.sendInfo(JSON.toJSONString(succeed), SpringSecurityUtils.getUserName());
                }
            } else {
                log.info("EmqX:客户端{}已连线√", clientId);
            }
        } else if (topic.startsWith("reader_")) { // 其他设备的通用消息
            topic = topic.replace("reader_", "");
            String msg = ByteUtil.toHexString(message.getPayload());
            ChenChaoCmdMessageParser.ResolveMqMessage(msg);
            new ZhangPanLinCmdMessageParser().dealWithMessage(msg, topic);
            // 天线状态解析类
            new ProbeMessageParser().resolve(msg);
        } else if (topic.startsWith(GConfig.MQ_TOPIC_DEVICE_ONLINE)) { // 设备恢复
            String payload = new String(message.getPayload());
            Map payloadMap = JSON.parseObject(payload, Map.class);
            DeviceStatusMessageParser.deviceOnline(payloadMap);
        } else if (topic.startsWith(GConfig.MQ_TOPIC_DEVICE_OFFLINE)) { // 设备离线
            String payload = new String(message.getPayload());
            Map payloadMap = JSON.parseObject(payload, Map.class);
            DeviceStatusMessageParser.deviceOffline(payloadMap);
        } else if (topic.startsWith(GConfig.MQ_TOPIC_DEVICE_ERR)) { // 设备从未上线,网路可能故障
            String payload = new String(message.getPayload());
            Map payloadMap = JSON.parseObject(payload, Map.class);
            DeviceStatusMessageParser.deviceNetError(payloadMap);
        } else if (topic.startsWith(GConfig.MQ_TOPIC_PROBE_ONLINE)) { // 天线正常
            String payload = new String(message.getPayload());
            Map payloadMap = JSON.parseObject(payload, Map.class);
            ProbeOnlineParser.resolve(payloadMap);
        } else if (topic.startsWith(GConfig.MQ_TOPIC_PROBE_ERR)) { // 天线故障
            String payload = new String(message.getPayload());
            Map payloadMap = JSON.parseObject(payload, Map.class);
            ProbeErrorParser.resolve(payloadMap);
        } else if (topic.startsWith(GConfig.MQ_TOPIC_DEVICE_LINK_ERR)) { // 链路异常
            String payload = new String(message.getPayload());
            Map payloadMap = JSON.parseObject(payload, Map.class);
            ProbeLinkErrorParser.resolve(payloadMap);
        } else if (topic.startsWith(GConfig.MQ_TOPIC_DEVICE_LINK_ONLINE)) { // 链路正常
            String payload = new String(message.getPayload());
            Map payloadMap = JSON.parseObject(payload, Map.class);
            ProbeLinkOnlineParser.resolve(payloadMap);
        } else if (topic.startsWith(GConfig.MQ_TOPIC_DEVICE_PROBE_SIGNAL_STRENGTH)) { // 信源强度
            String payload = new String(message.getPayload());
            Map payloadMap = JSON.parseObject(payload, Map.class);
            ProbeSignalStrengthParser.resolve(payloadMap);
        } else if (topic.startsWith(GConfig.MQ_TOPIC_CSHARP_CLIENT_ALERT_ASYNC)) { // 来自C#客户端的告警信息同步请求
            if (vl) {
                log.info("[C#]收到集中告警信息同步请求指令!!!!!");
                String payload = new String(message.getPayload());
                CsharpClientParser.resolve(payload);
            } else {
                log.warn("[TEL]!!!!!!!!!!!!!!!!!!!!!!在配置项中,TEL服务未启用.!!!!!!!!!!!!!!!!!!!!!");
            }
        } else if (topic.startsWith(GConfig.MQ_TOPIC_DEVICE_HIS_ANT_LOSS)) {
            // log.info("消费-记录历史天线路损值～");
            String payload = new String(message.getPayload());
            Map payloadMap = JSON.parseObject(payload, Map.class);
            ProbeHisAntLossParser.resolve(payloadMap);
        } else if (topic.startsWith(GConfig.MQ_TOPIC_DEVICE_OPTICAL)) {
            // log.info("消费-光交换机光端口，功率～");
            String payload = new String(message.getPayload());
            // Map payloadMap = JSON.parseObject(payload, Map.class);
            OpticalSwitchMessageParser.resolve(payload);
        } else {
            log.debug("Unknown topic :{}", topic);
        }
    }

    /**
     * 消息发布成功回调
     *
     * @param iMqttDeliveryToken
     * @return void
     * <AUTHOR>
     * @date 2021/7/30 17:14
     */
    @Override
    public void deliveryComplete(IMqttDeliveryToken iMqttDeliveryToken) {

        log.info("==================>deliveryComplete====================>");
        log.info(JSON.toJSONString(iMqttDeliveryToken));
        log.info("<==================>deliveryComplete====================");
    }

    @Override
    public void connectComplete(boolean reconnect, String serverURI) {
        log.info("MQTT连接完成，重新订阅主题. reconnect={}, serverURI={}", reconnect, serverURI);

        // 订阅系统主题
        subscribeWithRetry("$SYS/brokers/+/clients/+/disconnected", 0, "[Sys][离线主题]");
        subscribeWithRetry("$SYS/brokers/+/clients/+/connected", 0, "[Sys][上线主题]");

        // 订阅业务主题
        subscribeWithRetry(GConfig.MQ_TOPIC_BASE + "#", 0, "[Biz][基础主题]");
        subscribeWithRetry(GConfig.MQ_TOPIC_CSHARP_CLIENT + "#", 0, "[Biz][CSharp主题]");

        // 异步处理设备主题订阅
        messageExecutor.submit(
                () -> {
                    try {
                        subscribeDeviceTopics();
                    } catch (Exception e) {
                        log.error("订阅设备主题时发生异常", e);
                    }
                });
    }

    /** 带重试机制的订阅方法 */
    private void subscribeWithRetry(String topic, int qos, String topicDesc) {
        int maxRetries = 3;
        for (int i = 0; i < maxRetries; i++) {
            try {
                MqttSubClient.getInstance().subscribe(topic, qos);
                log.info("[订阅][{}]订阅成功: {}", topicDesc, topic);
                return;
            } catch (Exception e) {
                log.warn("[订阅][{}]第{}次订阅失败: {}", topicDesc, i + 1, e.getMessage());
                if (i == maxRetries - 1) {
                    log.error("[订阅][{}]订阅失败，已达到最大重试次数: {}", topicDesc, topic, e);
                } else {
                    try {
                        Thread.sleep(1000); // 等待1秒后重试
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        return;
                    }
                }
            }
        }
    }

    /** 订阅设备相关主题 */
    private void subscribeDeviceTopics() {
        try {
            LambdaQueryWrapper<BelongUnit> lqw = new LambdaQueryWrapper<BelongUnit>();
            lqw.eq(BelongUnit::getEnableFlag, true);

            BelongUnitService service = SpringUtils.getBean(BelongUnitService.class);
            List<BelongUnit> list = service.list(lqw);

            log.info("开始订阅{}个设备主题", list.size());
            for (BelongUnit nt : list) {
                String hostNum = nt.getHostNum();
                String topic = "reader_" + hostNum;
                try {
                    MqttSubClient.getInstance().subscribe(topic, 0);
                    log.info("[订阅][设备主题][{}]订阅成功", topic);
                } catch (Exception e) {
                    log.error("[订阅][设备主题][{}]订阅失败", topic, e);
                }
            }
        } catch (Exception e) {
            log.error("查询设备列表或订阅设备主题时发生异常", e);
        }
    }
}
