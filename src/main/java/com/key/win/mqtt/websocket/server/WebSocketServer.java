package com.key.win.mqtt.websocket.server;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.key.win.biz.baseInfo.model.SiteInfo;
import com.key.win.common.web.Result;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@ServerEndpoint(value = "/ws/sf/{uuid}")
public class WebSocketServer {

  // 静态变量，用来记录当前在线连接数。应该把它设计成线程安全的。
  private static AtomicInteger onlineNum = new AtomicInteger();

  // 用来存放所有的用户session
  public static ConcurrentHashMap<String, WebSocketServer> sessionPool = new ConcurrentHashMap<>();

  // 与某个客户端的连接会话，需要通过它来给客户端发送数据
  private Session session;

  // 接收sid
  private String uuid = "";

  // 建立连接成功调用
  @OnOpen
  public void onOpen(Session session, @PathParam("uuid") String uuid) throws IOException {
    this.session = session;
    this.uuid = uuid;
    // 将session 存放到 hashMap 集合
    sessionPool.put(uuid, this);
    // 在线人数+1
    addOnlineCount();
    this.sendAllUserOnlineStatus();
    log.info("【{}】==WS连接成功,当前总连接数:{}", this.uuid, onlineNum.intValue());
    Result<Object> succeed = Result.succeed("WS:【{" + uuid + "}】用户与服务器连接成功");
    WebSocketServer.sendInfo(JSON.toJSONString(succeed), uuid);
  }

  // 关闭连接时调用
  @OnClose
  public void onClose() {
    // 删除集合中退出的session,
    sessionPool.remove(this.uuid);
    // 在线人数--
    subOnlineCount();
    this.sendAllUserOnlineStatus();
    log.info("【{}】-WS连接中断,当前总连接数:{}", this.uuid, onlineNum.intValue());
  }

  // 收到客户端信息
  @OnMessage
  public void onMessage(String message) {
    if (StringUtils.isNotBlank(message)) {}
  }

  // 发送给所有在线用户
  private void sendAllUserOnlineStatus() {
    String msg = MessageBeanToJson(1);
    try {
      sendAllUser(msg);
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  /** 实现服务器主动推送 */
  public synchronized void sendMessage(String message) throws IOException {
    this.session.getBasicRemote().sendText(message);
  }

  /** 群发自定义消息 */
  public static void sendAllUser(String message) throws IOException {

    for (String key : sessionPool.keySet()) {
      try {
        WebSocketServer socketServer = sessionPool.get(key);
        socketServer.sendMessage(message);
      } catch (IOException e) {
        continue;
      }
    }
  }

  public static void sendInfo(String message, @PathParam("sid") String sid) throws IOException {
    log.info("推送消息到窗口:{},推送内容:{}", sid, message);
    for (WebSocketServer item : sessionPool.values()) {
      try {
        if (StringUtils.isBlank(sid)) {
          log.warn("SID传入为空");
        } else if (item.uuid.equals(sid)) {
          item.sendMessage(message);
        }
      } catch (IOException e) {
        throw e;
      }
    }
  }

  public static void send(String message, @PathParam("sid") String sid) {
    log.info("推送消息到窗口:{},推送内容:{}", sid, message);
    for (WebSocketServer item : sessionPool.values()) {
      try {
        if (StringUtils.isBlank(sid)) {
          log.warn("SID传入为空");
        } else if (item.uuid.equals(sid)) {
          Result<Object> succeed = Result.succeed(message);
          item.sendMessage(JSON.toJSONString(succeed));
        }
      } catch (IOException e) {
        e.printStackTrace();
      }
    }
  }

  String MessageBeanToJson(int type, SiteInfo data) {
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("type", type);
    jsonObject.put("data", data);

    String message = jsonObject.toJSONString();

    return message;
  }

  String MessageBeanToJson(int type) {
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("type", type);
    String message = jsonObject.toJSONString();
    return message;
  }

  // 错误时调用
  @OnError
  public void onError(Session session, Throwable throwable) {
    throwable.printStackTrace();
  }

  public static void addOnlineCount() {
    onlineNum.incrementAndGet();
  }

  public static void subOnlineCount() {
    onlineNum.decrementAndGet();
  }
}
