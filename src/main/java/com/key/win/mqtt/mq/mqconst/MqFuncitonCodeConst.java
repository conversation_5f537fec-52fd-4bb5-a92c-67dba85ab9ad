package com.key.win.mqtt.mq.mqconst;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.key.win.mqtt.mq.statilFile.OpticalStaticList;

import java.util.stream.Collectors;

public final class MqFuncitonCodeConst {

  private static final Table<String, String, Integer> paramObject = HashBasedTable.create();
  private static final Table<String, String, Integer> SysObject = HashBasedTable.create();

  private static final Table<String, String, Integer> opticalObject = HashBasedTable.create();

  static {

    // ======================================================================
    paramObject.put("17A000", "软件版本", 20); // 软件版本
    paramObject.put("17A100", "设备型号", 20); // 设备型号
    paramObject.put("17A200", "设备生产系列号", 20); // 设备生产系列号
    paramObject.put("0AA300", "设备时间", 7); // 设备时间

    paramObject.put("17A800", "服务器IP", 20); // 服务器IP地址
    paramObject.put("05A900", "服务器端口", 2); // 服务器端口号

    paramObject.put("07B001", "开始频率", 4); // 开始频率
    paramObject.put("07B101", "结束频率", 4); // 结束频率
    paramObject.put("05B201", "频率步进", 2); // 频率步进

   // paramObject.put("05B501", "故障诊断时间", 2); // 故障诊断时间
   // paramObject.put("05B601", "故障诊断周期", 2); // 故障诊断周期
    //paramObject.put("04B701", "故障诊断次数", 1); // 故障诊断次数 (指令值不正确)
    paramObject.put("05B601", "故障诊断周期时间", 2); // 故障诊断周期时间
    paramObject.put("04DF01", "故障诊断次数", 1); // 故障诊断次数

    paramObject.put("04B401", "故障诊断开关", 1); // 故障诊断开关
    paramObject.put("04B801", "精确读取开关", 1); // 精确读取开关
    paramObject.put("04B901", "精确读取修正值", 1); // 精确读取修正值

    paramObject.put("04B301", "发射功率", 1); // 发射功率
    paramObject.put("04BA01", "起始功率", 1); // 起始功率
    paramObject.put("04BB01", "结束功率", 1); // 结束功率
    paramObject.put("04C601", "功率步进", 1); // 功率步进
    paramObject.put("04E001", "告警判断次数", 1); // 告警判断次数

    opticalObject.put(OpticalConst.CODE_OPTICAL_PORT_1, "光交换机光端口状态1", 1); // 光交换机光端口1状态
    opticalObject.put(OpticalConst.CODE_OPTICAL_PORT_2, "光交换机光端口状态2", 1); // 光交换机光端口2状态
    opticalObject.put(OpticalConst.CODE_OPTICAL_PORT_3, "光交换机光端口状态3", 1); // 光交换机光端口3状态
    opticalObject.put(OpticalConst.CODE_OPTICAL_PORT_4, "光交换机光端口状态4", 1); // 光交换机光端口4状态
    opticalObject.put(OpticalConst.CODE_OPTICAL_PORT_5, "光交换机光端口状态5", 1); // 光交换机光端口5状态
    opticalObject.put(OpticalConst.CODE_OPTICAL_PORT_6, "光交换机光端口状态6", 1); // 光交换机光端口6状态
    opticalObject.put(OpticalConst.CODE_OPTICAL_PORT_7, "光交换机光端口状态7", 1); // 光交换机光端口7状态
    opticalObject.put(OpticalConst.CODE_OPTICAL_PORT_8, "光交换机光端口状态8", 1); // 光交换机光端口8状态


    opticalObject.put(OpticalConst.CODE_OPTICAL_RECEIVE_POWER_1, "光交换机光收功率端口1", 2); // 光交换机光收功率1
    opticalObject.put(OpticalConst.CODE_OPTICAL_RECEIVE_POWER_2, "光交换机光收功率端口2", 2); // 光交换机光收功率2
    opticalObject.put(OpticalConst.CODE_OPTICAL_RECEIVE_POWER_3, "光交换机光收功率端口3", 2); // 光交换机光收功率3
    opticalObject.put(OpticalConst.CODE_OPTICAL_RECEIVE_POWER_4, "光交换机光收功率端口4", 2); // 光交换机光收功率4
    opticalObject.put(OpticalConst.CODE_OPTICAL_RECEIVE_POWER_5, "光交换机光收功率端口5", 2); // 光交换机光收功率5
    opticalObject.put(OpticalConst.CODE_OPTICAL_RECEIVE_POWER_6, "光交换机光收功率端口6", 2); // 光交换机光收功率6
    opticalObject.put(OpticalConst.CODE_OPTICAL_RECEIVE_POWER_7, "光交换机光收功率端口7", 2); // 光交换机光收功率7
    opticalObject.put(OpticalConst.CODE_OPTICAL_RECEIVE_POWER_8, "光交换机光收功率端口8", 2); // 光交换机光收功率8

    opticalObject.put(OpticalConst.CODE_OPTICAL_OUTPUT_POWER_1, "光交换机光发功率端口1", 2); // 光交换机光发功率1
    opticalObject.put(OpticalConst.CODE_OPTICAL_OUTPUT_POWER_2, "光交换机光发功率端口2", 2); // 光交换机光发功率2
    opticalObject.put(OpticalConst.CODE_OPTICAL_OUTPUT_POWER_3, "光交换机光发功率端口3", 2); // 光交换机光发功率3
    opticalObject.put(OpticalConst.CODE_OPTICAL_OUTPUT_POWER_4, "光交换机光发功率端口4", 2); // 光交换机光发功率4
    opticalObject.put(OpticalConst.CODE_OPTICAL_OUTPUT_POWER_5, "光交换机光发功率端口5", 2); // 光交换机光发功率5
    opticalObject.put(OpticalConst.CODE_OPTICAL_OUTPUT_POWER_6, "光交换机光发功率端口6", 2); // 光交换机光发功率6
    opticalObject.put(OpticalConst.CODE_OPTICAL_OUTPUT_POWER_7, "光交换机光发功率端口7", 2); // 光交换机光发功率7
    opticalObject.put(OpticalConst.CODE_OPTICAL_OUTPUT_POWER_8, "光交换机光发功率端口8", 2); // 光交换机光发功率8


    // ======================================================================
    // 设备到网管的上行指令
    SysObject.put("010101FF0400", "心跳指令", 4);
    SysObject.put("010102010000", "系统重置", 0);
    SysObject.put("020110010400", "故障诊断指令", 4);
    SysObject.put("020111010400", "精确读取", 14);
    SysObject.put("020112010E00", "状态上报", 14);
    SysObject.put("020113010E00", "路损上报", 14);
    SysObject.put("040101010D00", "探针查询", 13);
    SysObject.put("040102010D00", "探针设置", 13);
    SysObject.put("040103010100", "探针批量删除", 4);
    SysObject.put("040106010D00", "删除探针", 13);
    SysObject.put("040108010200", "告警信息读取", 2);
  }

  public static Table<String, String, Integer> getParamObject() {
    return paramObject;
  }

  public static Table<String, String, Integer> getSysObject() {
    return SysObject;
  }

  public static Table<String, String, Integer> getOpticalObject() {
    return opticalObject;
  }
}
