package com.key.win.mqtt.mq.mqconst;

public final class MqConst {

  /** 正则前段匹配 第一段：7E 开始码 第二段：8位机器号码 */
  public static final String EXP_PREFIX = "^7(e|E)[\\da-zA-z]{8}";

  /** 适用于[参数查询之外]的MQ报文内容 数据响应data部分:不定长度 尾段:7E结束码 */
  public static final String EXP_SUFIX = "(.*?)7(e|E)$";

  /** 适用于参数查询响应的MQ报文匹配 第一段:4个字符串长度的 双字节报文长度 例如：0A00 第二段：不定长度的报文data 第三段：结束码7E */
  public static final String EXP_ARG_QUERY_SUFIX = "(.*?)7(e|E)$";

  public static final String EXP_DATA_LENGTH = "[\\da-zA-z]{4}";

  /** 心跳指令 01：监测主机指令 01：上行 01：功能码 FF:上行填充 0400：数据长度 */
  public static final String code_heartBeat = "010101FF0400"; // 心跳指令

  /** 系统重置 01：监测主机指令 01：上行 02：功能码 01:状态正常码 0000：数据长度 */
  public static final String code_reboot = "010102010000"; // 系统重置

  /** 故障诊断指令 02：系统指令 01：上行 10：功能码 01:状态正常码 0400：数据长度 [ˌdaɪəɡˈnəʊsɪs] 诊断 */
  public static final String code_diagnosis = "020110010400"; // 故障诊断指令

  /** 故障诊断指令 02：系统指令 01：上行 11：功能码 01:状态正常码 0400：数据长度 [ˈækjərət] 精确的 */
  public static final String code_accurate_reading = "020111010400"; // 精确读取

  /** 状态上报 02：系统指令 01：上行 12：功能码 01:状态正常码 0E00：数据长度 */
  public static final String code_status_reported = "020112010E00";

  /** 路损上报 02：系统指令 01：上行 13：功能码 01:状态正常码 0E00：数据长度 line loss 路损 */
  public static final String code_line_loss_reported = "020113010E00";

  /** 探针查询 04：RFID 01：上行 01：功能码 01:状态正常码 0D00：数据长度 [prəʊb] n. 探针查询 */
  public static final String code_probe_query = "040101010D00"; // 探针查询

  /** 探针设置 04：RFID 01：上行 02：功能码 01:状态正常码 0D00：数据长度 [prəʊb] n. 探针查询 */
  public static final String code_probe_setting = "040102010D00";

  /** 探针批量删除 04：RFID 01：上行 03：功能码 01:状态正常码 0100：数据长度 */
  public static final String code_probe_deletebatch = "040103010100"; // 批量删除探针

  /** 批量删除 04：RFID 01：上行 06：功能码 01:状态正常码 0D00：数据长度 */
  public static final String code_probe_delete = "040106010D00"; // 删除探针

  /** 告警信息读取 04：RFID 01：上行 08：功能码 01:状态正常码 0200：数据长度 */
  public static final String code_alarminfo_reading = "040108010200"; // 告警信息读取

  /** 参数设置 04：RFID 01：上行 0A：功能码 01:状态正常码 0100：数据长度 */
  public static final String code_arg_setting = "04010A010100"; // 参数设置

  /** 参数查询(设备到网关)：没有数据长度,请使用EXP_ARG_QUERY_SUFIX 作为后缀进行匹配操作 04：RFID 01：上行 09：功能码 01:状态正常码 */
  public static final String code_arg_query_DEVICE2OMC = "04010901"; // 参数查询()

  /** 参数查询(网关到设备)：没有数据长度,请使用EXP_ARG_QUERY_SUFIX 作为后缀进行匹配操作 04：RFID 01：上行 09：功能码 01:状态正常码 */
  public static final String code_arg_query_OMC2DEVICE = "040009FF";

  /** 参数查询（光交换机光端口状态查询）  \\d{2} 表示匹配两个数字字符 */
  public static final String OPTICAL_PORT_REGEX = "04c701\\d{2}04c801\\d{2}04c901\\d{2}04ca01\\d{2}04cb01\\d{2}04cc01\\d{2}04cd01\\d{2}04ce01\\d{2}";

  /** 参数查询（光交换机光收功率查询）4个端口组合1  .{4} 表示匹配任意4个字符 */
  public static final String OPTICAL_RECEIVE_POWER_REGEX_1 ="05cf01.{4}05d001.{4}05d101.{4}05d201.{4}";

  /** 参数查询（光交换机光收功率查询）4个端口组合2  .{4} 表示匹配任意4个字符 */
  public static final String OPTICAL_RECEIVE_POWER_REGEX_2 ="05d301.{4}05d401.{4}05d501.{4}05d601.{4}";

  /** 参数查询（光交换机光发功率查询）4个端口组合1  .{4} 表示匹配任意4个字符 */
  public static final String OPTICAL_OUTPUT_POWER_REGEX_1 ="05d701.{4}05d801.{4}05d901.{4}05da01.{4}";

  /** 参数查询（光交换机光发功率查询）4个端口组合2  .{4} 表示匹配任意4个字符 */
  public static final String OPTICAL_OUTPUT_POWER_REGEX_2 ="05db01.{4}05dc01.{4}05dd01.{4}05de01.{4}";

}
