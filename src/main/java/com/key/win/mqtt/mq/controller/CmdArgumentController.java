package com.key.win.mqtt.mq.controller;

import com.key.win.biz.baseInfo.model.Probe;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.service.ProbeService;
import com.key.win.mqtt.mq.cmd.TestPageCmdService;
import com.key.win.mqtt.mq.vo.ResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/cmd/argument/*")
public class CmdArgumentController {

  @Autowired private TestPageCmdService testPageCmdService;

  @Autowired private ProbeService probeService;

  @PostMapping("/query/probe/{deviceId}")
  public ResultVo lookDeviceProbe(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.lookDeviceProbe(deviceId);
  }

  @PostMapping("/query/alarmInfoRead/{deviceId}")
  public ResultVo alarmInfoRead(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.alarmInfoRead(deviceId);
  }

  @PostMapping("/query/softVersion/{deviceId}")
  public ResultVo softVersion(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.softVersion(deviceId);
  }

  /**
   * 设备型号
   *
   * @param deviceId
   * @return
   */
  @PostMapping("/query/softModelNumber/{deviceId}")
  public ResultVo softModelNumber(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.deviceVersion(deviceId);
  }

  /**
   * 设备生产系列号
   *
   * @param deviceId
   * @return
   */
  @PostMapping("/query/deviceSerialNumber/{deviceId}")
  public ResultVo deviceSerialNumber(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.productSerVersion(deviceId);
  }

  @PostMapping("/query/equipmentTime/{deviceId}")
  public ResultVo equipmentTime(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.equipmenTime(deviceId);
  }

  @PostMapping("/query/ip/{deviceId}")
  public ResultVo lookIp(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.lookIp(deviceId);
  }

  @PostMapping("/query/port/{deviceId}")
  public ResultVo serverAno(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.serverAno(deviceId);
  }

  @PostMapping("/query/startFrequency/{deviceId}")
  public ResultVo startFrequency(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.frequencyStart(deviceId);
  }

  /**
   * 结束频率
   *
   * @param deviceId
   * @return
   */
  @PostMapping("/query/endFrequency/{deviceId}")
  public ResultVo endFrequency(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.frequencyEnd(deviceId);
  }

  @PostMapping("/query/frequencyStep/{deviceId}")
  public ResultVo lookFrequencyStep(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.lookFrequencyStep(deviceId);
  }

  @PostMapping("/query/launchPower/{deviceId}")
  public ResultVo launchPower(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.launchPower(deviceId);
  }

  @PostMapping("/query/faultSwitch/{deviceId}")
  public ResultVo lookFaultSwitch(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.lookFaultSwitch(deviceId);
  }

  @PostMapping("/query/faultDiagnosisCycle/{deviceId}")
  public ResultVo faultDiagnosisCycle(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.faultDiagnosisCycle(deviceId);
  }

  @PostMapping("/query/faultDiagnosisTime/{deviceId}")
  public ResultVo faultDiagnosisTime(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.faultDiagnosisTime(deviceId);
  }

  @PostMapping("/query/faultDiagnosisTimes/{deviceId}")
  public ResultVo faultDiagnosisTimes(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.faultDiagnosisTimes(deviceId);
  }

  @PostMapping("/query/accurateReadingSwitch/{deviceId}")
  public ResultVo lookAccurateReadingSwitch(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.lookAccurateReadingSwitch(deviceId);
  }

  @PostMapping("/query/accuratelyReadTheCorrectionValue/{deviceId}")
  public ResultVo lookAccuratelyReadTheCorrectionValue(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.lookAccuratelyReadTheCorrectionValue(deviceId);
  }

  @PostMapping("/query/startEndPower/{deviceId}")
  public ResultVo startEndPower(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.startEndPower(deviceId);
  }

  @PostMapping("/query/startPower/{deviceId}")
  public ResultVo startPower(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.queryStartPower(deviceId);
  }

  @PostMapping("/query/endPower/{deviceId}")
  public ResultVo endPower(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.queryEndPower(deviceId);
  }

  /**
   * 查询功率步进
   * @param deviceId
   * @return
   */
  @PostMapping("/query/powerStep/{deviceId}")
  public ResultVo powerStep(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.powerStep(deviceId);
  }

  /**
   * 查询-光交换机光功率（端口状态，端口收/发功率）
   * @param deviceId
   * @return
   */
  @PostMapping("/query/opticalPower/{deviceId}")
  public ResultVo opticalPower(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.queryOpticalSwitchMsg(deviceId);
  }


  // ========================================  下面是设置 ======================================
  @PostMapping("/set/probe")
  public ResultVo setUpProbe(@RequestBody Probe probe) {
    return testPageCmdService.setUpProbe(probe);
  }

  @PostMapping("/set/removeProbeBatch/{deviceId}")
  public ResultVo deleteProbeBatch(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.deleteProbeBatch(deviceId);
  }

  @PostMapping("/set/removeProbe")
  public ResultVo deleteProbe(@RequestBody Probe probe) {
    return testPageCmdService.deleteProbe(probe);
  }

  /**
   * 设置设备的时间（网管系统当前时间）
   * @param belongUnit
   * @return
   */
  @PostMapping("/set/serverCurrentTime")
  public ResultVo serverTime(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setEquipmenTime(belongUnit);
  }

  @PostMapping("/set/ip")
  public ResultVo setUpTheServer(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setUpTheServer(belongUnit);
  }

  @PostMapping("/set/port")
  public ResultVo setUpThePort(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setUpThePort(belongUnit);
  }

  @PostMapping("/set/startFrequency")
  public ResultVo setUpStartFrequency(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setUpStartFrequency(belongUnit);
  }

  @PostMapping("/set/endFrequency")
  public ResultVo setUpEndFrequency(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setUpEndFrequency(belongUnit);
  }

  @PostMapping("/set/frequencyStep")
  public ResultVo frequencyStep(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setFrequencyStep(belongUnit);
  }

  @PostMapping("/set/transmitPower")
  public ResultVo setTransmitPower(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setTransmitPower(belongUnit);
  }

  @PostMapping("/set/faultSwitch")
  public ResultVo setFaultSwitch(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setFaultSwitch(belongUnit);
  }

  @PostMapping("/set/faultDiagnosisTime")
  public ResultVo setFaultDiagnosisTime(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setFaultDiagnosisTime(belongUnit);
  }

  /**
   * 故障诊断周期时间
   * @param belongUnit
   * @return
   */
  @PostMapping("/set/faultDiagnosisCycle")
  public ResultVo setTheFaultDiagnosisCycle(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setTheFaultDiagnosisCycle(belongUnit);
  }

  /**
   * 设置故障诊断次数
   * @param belongUnit
   * @return
   */
  @PostMapping("/set/faultDiagnosisTimes")
  public ResultVo setFaultDiagnosisNumber(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setFaultDiagnosisNumber(belongUnit);
  }

  @PostMapping("/set/accurateReadingSwitch")
  public ResultVo setAccurateReadingSwitch(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setAccurateReadingSwitch(belongUnit);
  }

  @PostMapping("/set/accuratelyReadTheCorrectionValue")
  public ResultVo accuratelyReadTheCorrectionValue(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setAccuratelyReadTheCorrectionValue(belongUnit);
  }

  @PostMapping("/set/startPower")
  public ResultVo SetStartPower(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setStartPower(belongUnit);
  }

  @PostMapping("/set/endPower")
  public ResultVo SetEndPower(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setEndPower(belongUnit);
  }

  /**
   * 设置功率步进
   * @param belongUnit
   * @return
   */
  @PostMapping("/set/powerStep")
  public ResultVo setPowerStep(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setPowerStep(belongUnit);
  }

  /**
   * 设置告警判断次数
   * @param belongUnit
   * @return
   */
  @PostMapping("/set/alarmJudgeCount")
  public ResultVo setAlarmJudgeCount(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setAlarmJudgeCount(belongUnit);
  }



}
