package com.key.win.mqtt.mq.parser.probe;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.TEL.bootstrap.TELService;
import com.key.win.biz.alarm.model.RealTimeWarn;
import com.key.win.biz.alarm.service.RealTimeWarnSevice;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.config.mqtt.MqttPushClient;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.utils.DateUtils;
import com.key.win.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.Map;

@SuppressWarnings("rawtypes")
@Slf4j
public class ProbeLinkOnlineParser {

  private static RealTimeWarnSevice realTimeWarnSevice =
      SpringUtils.getBean(RealTimeWarnSevice.class);

  private static BelongUnitService belongUnitService = SpringUtils.getBean(BelongUnitService.class);

  private static String alarmType = GConfig.alarmType_probeLinkErr;

  public static void resolve(Map payload) {

    String deviceId = MapUtil.getStr(payload, "deviceId");
    Date currentTime = new Date();
    RealTimeWarn probeAlarmBean = getProbeAlarmBean(deviceId);
    if (probeAlarmBean != null) {
      probeAlarmBean.setAlarmStatus(GConfig.alarmStatus_His_clear); // 实时告警
      probeAlarmBean.setRemark("链路恢复: [" + deviceId + "]链路恢复正常,自动删除故障信息");
      probeAlarmBean.setOperationPeople("SYS_CHECK");
      probeAlarmBean.setAttr1(GConfig.ALARM_REPORT_FLAG_0);
      probeAlarmBean.setOperationTime(DateUtils.dateTimeToStr(currentTime));
      realTimeWarnSevice.updateById(probeAlarmBean);

      String reportMsg =
          TELService.packageMsgProactiveReporting(
              belongUnitService, probeAlarmBean, 1, TELService.reportMsg00);
      MqttPushClient.getInstance()
          .publish2CsharpClient(GConfig.MQ_TOPIC_CSHARP_CLIENT_ALERT_INFO, reportMsg);
      log.info("设备：{}，链路恢复，告警清除报文[{}]",deviceId,reportMsg);
    }
  }

  private static RealTimeWarn getProbeAlarmBean(String deviceId) {
    LambdaQueryWrapper<RealTimeWarn> lqwRwrning = new LambdaQueryWrapper<RealTimeWarn>();
    lqwRwrning.eq(RealTimeWarn::getAlarmType, alarmType);
    lqwRwrning.eq(RealTimeWarn::getHostNumber, deviceId);
    lqwRwrning.eq(RealTimeWarn::getAlarmStatus, GConfig.alarmStatus_realTime); // 实时告警
    RealTimeWarn probeAlarmBean = realTimeWarnSevice.getOne(lqwRwrning);
    return probeAlarmBean;
  }
}
