package com.key.win.mqtt.mq.parser;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.TEL.bootstrap.TELService;
import com.key.win.biz.alarm.model.RealTimeWarn;
import com.key.win.biz.alarm.service.RealTimeWarnSevice;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.config.mqtt.MqttPushClient;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.utils.DateUtils;
import com.key.win.utils.SpringUtils;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 诊断设备心跳监测后的离线/恢复在线业务存储功能
 *
 * <AUTHOR>
 */
@Slf4j
@SuppressWarnings("rawtypes")
public final class DeviceStatusMessageParser {

  private static BelongUnitService belongUnitService = SpringUtils.getBean(BelongUnitService.class);
  private static RealTimeWarnSevice realTimeWarnSevice =
      SpringUtils.getBean(RealTimeWarnSevice.class);
  private static String alarmType = GConfig.alarmType_deviceOffline;

  /**
   * 设备恢复
   *
   * @param payload
   */
  public static void deviceOnline(Map payload) {
    String deviceId = MapUtil.getStr(payload, "deviceId", "");
    if (StringUtils.isBlank(deviceId)) {
      return;
    }
    Date currentTime = new Date();
    RealTimeWarn dbAlarmBean = getWarnByDeviceId(deviceId);
    if (dbAlarmBean != null) {
      // 如果当前主机在告警信息中,则将告警信息的状态置成历史告警（2）
      dbAlarmBean.setAlarmStatus(GConfig.alarmStatus_His_clear);
      dbAlarmBean.setRemark("系统自检执行");
      dbAlarmBean.setOperationPeople("SYS_CHECK");
      dbAlarmBean.setAttr1(GConfig.ALARM_REPORT_FLAG_1);
      dbAlarmBean.setOperationTime(DateUtils.dateTimeToStr(currentTime));
      realTimeWarnSevice.updateById(dbAlarmBean);
      String reportMsg =
          TELService.packageMsgProactiveReporting(
              belongUnitService, dbAlarmBean, 1, TELService.reportMsg00);
      log.info("设备：{}在线，发送告警清除报文[{}]",deviceId,reportMsg);
      MqttPushClient.getInstance()
          .publish2CsharpClient(GConfig.MQ_TOPIC_CSHARP_CLIENT_ALERT_INFO, reportMsg);
    }
  }

  /**
   * 设备离线
   *
   * @param payload Map类型,存储设备编号和离线时间
   */
  public static void deviceOffline(Map payload) {
    String deviceId = MapUtil.getStr(payload, "deviceId", "");
    if (StringUtils.isBlank(deviceId)) {
      return;
    }
    long _offlineTime = MapUtil.getLong(payload, "offlineTime");
    RealTimeWarn warn = getWarnByDeviceId(deviceId);
    if (warn != null) {
      warn.setAttr1(GConfig.ALARM_REPORT_FLAG_0);
      warn.setAlarmTimes(warn.getAlarmTimes() + 1);
      warn.setRemark("离线: 设备已经离线时长:" + _offlineTime / 1000 / 60 + "分钟");
      realTimeWarnSevice.updateById(warn);
    } else {
      // 不存在实时告警信息,可以创建告警信息
      BelongUnit unit = getUnitByDeviceId(deviceId);
      String remark = "离线: 设备已经离线时长:" + _offlineTime / 1000 / 60 + "分钟";
      warn = buildAlarmBean(remark, deviceId, unit);
      warn.setRemark(remark);
      realTimeWarnSevice.save(warn);
      String reportMsg =
          TELService.packageMsgProactiveReporting(
              belongUnitService, warn, 1, TELService.reportMsgFF);
      MqttPushClient.getInstance()
          .publish2CsharpClient(GConfig.MQ_TOPIC_CSHARP_CLIENT_ALERT_INFO, reportMsg);
      log.info("设备：{}离线，上报告警[{}]",deviceId,reportMsg);
    }
  }

  /**
   * 设备网路故障
   *
   * @param payload
   */
  public static void deviceNetError(Map payload) {
    String deviceId = MapUtil.getStr(payload, "deviceId", "");
    if (StringUtils.isBlank(deviceId)) {
      return;
    }
    log.warn("警告: {} 监控主机从未上线,[计入告警信息]请检查监控主机配置以及网络环境.", deviceId);
    RealTimeWarn warn = getWarnByDeviceId(deviceId);
    if (warn != null) {
      warn.setAlarmTimes(warn.getAlarmTimes() + 1);
      realTimeWarnSevice.updateById(warn);
    } else {
      BelongUnit unit = getUnitByDeviceId(deviceId);
      String remark = "设备异常: 设备离线中,请检查监控主机配置编号或者检查网络环境";
      RealTimeWarn alarmBean = buildAlarmBean(remark, deviceId, unit);
      alarmBean.setRemark(remark);
      realTimeWarnSevice.save(alarmBean);
      String reportMsg =
          TELService.packageMsgProactiveReporting(
              belongUnitService, alarmBean, 1, TELService.reportMsgFF);
      MqttPushClient.getInstance()
          .publish2CsharpClient(GConfig.MQ_TOPIC_CSHARP_CLIENT_ALERT_INFO, reportMsg);
      log.info("设备：{}异常，上报告警[{}]",deviceId,reportMsg);
    }
  }

  private static BelongUnit getUnitByDeviceId(String deviceId) {
    LambdaQueryWrapper<BelongUnit> lqwUnit = new LambdaQueryWrapper<BelongUnit>();
    lqwUnit.eq(BelongUnit::getHostNum, deviceId);
    BelongUnit one = belongUnitService.getOne(lqwUnit);
    return one;
  }

  private static RealTimeWarn getWarnByDeviceId(String deviceId) {
    LambdaQueryWrapper<RealTimeWarn> lqw = new LambdaQueryWrapper<RealTimeWarn>();
    lqw.eq(RealTimeWarn::getHostNumber, deviceId);
    lqw.eq(RealTimeWarn::getAlarmType, alarmType);
    lqw.eq(RealTimeWarn::getAlarmStatus, GConfig.alarmStatus_realTime);
    RealTimeWarn bean = realTimeWarnSevice.getOne(lqw);
    return bean;
  }

  private static RealTimeWarn buildAlarmBean(String remark, String deviceId, BelongUnit unit) {
    Date currentTime = new Date();
    RealTimeWarn tempWarn =
        RealTimeWarn.builder()
            .hostNumber(deviceId)
            .alarmTimes(1)
            .attr1(GConfig.ALARM_REPORT_FLAG_0)
            .alarmStatus(GConfig.alarmStatus_realTime)
            .alarmName("[" + deviceId + "]监控主机离线")
            .belongStationId(unit.getStationId())
            .belongStationName(unit.getStationName())
            .alarmTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(currentTime))
            .equipmentType(GConfig.equipmentType_machine)
            .alarmType(alarmType)
            .build();
    return tempWarn;
  }
}
