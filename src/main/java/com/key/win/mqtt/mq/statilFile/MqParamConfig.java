package com.key.win.mqtt.mq.statilFile;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/10/12 16:48
 */
public class MqParamConfig {

  public static final String ID_SUFFIX = "reader_"; // id 前缀

  // 1，开始标志
  public static final String PREFIX_SUFFIX = "7e"; // 前缀后缀

  // 2， 命令类型  0x01表示系统命令，0x02表示RFID相关命令，0x04 表示参数查询配置相关命令
  public static final String SYSTEM_ORDER = "01"; // 系统命令
  public static final String RFID_ORDER = "02"; // RFID相关
  public static final String PARAM_CONFIG_ORDER = "04"; // 参数查询配置相关命令

  // 4， 上下行标志：1个字节，0x00表示下行命令，0x01表示上行命令
  public static final String DOWN = "00"; //  下行
  public static final String UP = "01"; //   上行

  public static final String MSG_SUCCESS = "01";
  public static final String MSG_BUSYING = "02";
  public static final String MSG_ERR = "03";
  // 5， 功能代码：1个字节，按照命令类型进行分类，下面详细说明
  // 5.1  监测主机命令
  public static final String HEART_BEAT = "01"; // 心跳指令
  public static final String SYSTEM_RESTART = "02"; // 系统重置

  // 5.2 RFID 命令
  public static final String FAULT_DIAGNOSIS = "10"; // 故障诊断
  public static final String ACCURATE_READING = "11"; // 精确读取
  public static final String STATUS_REPORTIN = "12"; //  状态上报
  public static final String ROAD_LOSS_REPOR = "13"; // 路损上报

  // 5.3 参数查询配置命令
  public static final String QUERY_DEVICE_PROBE = "01"; // 查询设备探针
  public static final String SET_DEVICE_PROBE = "02"; // 设置设备探针

  public static final String DELETE_DEVICE_PROBE_BATCH = "03"; //  批量删除设备探针
  public static final String DELETE_DEVICE_PROBE = "06"; //  删除设备探针
  public static final String WARE_INFO_QUERY = "08"; //  告警信息查询
  public static final String PARAMETER_QUERY = "09"; // 参数查询
  public static final String PARAMETER_SETTING = "0a"; // 参数设置

  // 6   命令执行结果：1个字节，发起填充0xFF；应答，0x01表示正常状态，0x02表示系统忙，0x03表示设备故障，。

  public static final String ORDER_RESULT_FILL = "FF"; //  发起填充0xFF
  public static final String ORDER_RESULT_NORMAL = "01"; // 正常状态
  public static final String ORDER_RESULT_SYSTEM_BUSY = "02"; // 系统繁忙
  public static final String ORDER_RESULT_EQUIPMENT_FAILURE = "03"; // 设备故障

  // 7, 数据长度：2个字节。
  public static final String DATA_LENGTH_FOUR = "0400"; // 数据长度，4个长度 左高右低

  // 8, 数据单元：字节数不定。
  // 9  校验：方式（x16+x12+x5+1   1021）
  // 10  结束标志：0x7E

}
