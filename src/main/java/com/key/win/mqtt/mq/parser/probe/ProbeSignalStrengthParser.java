package com.key.win.mqtt.mq.parser.probe;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.alarm.model.RealTimeWarn;
import com.key.win.biz.alarm.service.RealTimeWarnSevice;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.utils.DateUtils;
import com.key.win.utils.SpringUtils;
import java.util.Date;
import java.util.Map;

/** 信源强度解析 */
@SuppressWarnings("rawtypes")
public class ProbeSignalStrengthParser {

  private static RealTimeWarnSevice realTimeWarnSevice =
      SpringUtils.getBean(RealTimeWarnSevice.class);

  private static String alarmType = GConfig.alarmType_probeSignalStrength; // 信源强度类型

  public static void resolve(Map payload) {

    String deviceId = MapUtil.getStr(payload, "deviceId");
    String sid = MapUtil.getStr(payload, "sid");
    String antName = MapUtil.getStr(payload, "antName");
    double lost = MapUtil.getDouble(payload, "lost");
    double limitLost = MapUtil.getDouble(payload, "limitLost");
    String lineId = MapUtil.getStr(payload, "lineId");
    String lineName = MapUtil.getStr(payload, "lineName");
    String stationId = MapUtil.getStr(payload, "stationId");
    String stationName = MapUtil.getStr(payload, "stationName");

    Date currentTime = new Date();

    RealTimeWarn probeAlarmBean = getProbeSignalStrengthAlarmBean(deviceId, antName);
    if (lost > limitLost) {
      if (probeAlarmBean != null) {
        probeAlarmBean.setAlarmTimes(probeAlarmBean.getAlarmTimes() + 1);
        probeAlarmBean.setAttr1(GConfig.ALARM_REPORT_FLAG_0);
        realTimeWarnSevice.updateById(probeAlarmBean);
      } else {
        probeAlarmBean = new RealTimeWarn();
        probeAlarmBean.setAlarmName("[" + deviceId + "][" + antName + "]天线信源偏弱");
        probeAlarmBean.setAlarmType(alarmType);
        probeAlarmBean.setEquipmentType(GConfig.equipmentType_Ant);
        probeAlarmBean.setHostNumber(deviceId);
        probeAlarmBean.setNetworkName(antName);
        probeAlarmBean.setBelongStationId(stationId);
        probeAlarmBean.setAttr1(GConfig.ALARM_REPORT_FLAG_0);
        probeAlarmBean.setBelongStationName(stationName);
        probeAlarmBean.setAlarmTime(DateUtils.dateTimeToStr(currentTime));
        probeAlarmBean.setAlarmStatus(GConfig.alarmStatus_realTime); // 实时告警
        probeAlarmBean.setRemark(
            "信源强度偏弱:[" + deviceId + "][" + antName + "]信源强度:" + lost + ",阈值:" + limitLost);
        probeAlarmBean.setOperationPeople("SYS_CHECK");
        realTimeWarnSevice.save(probeAlarmBean);
      }
    } else {
      if (probeAlarmBean != null) {
        probeAlarmBean.setAlarmStatus(GConfig.alarmStatus_His_clear); // 实时告警
        probeAlarmBean.setRemark("信源强度恢复:[" + deviceId + "][" + antName + "]信源强度恢复正常,自动删除故障信息");
        probeAlarmBean.setOperationPeople("SYS_CHECK");
        probeAlarmBean.setAttr1(GConfig.ALARM_REPORT_FLAG_1);
        probeAlarmBean.setOperationTime(DateUtils.dateTimeToStr(currentTime));
        realTimeWarnSevice.updateById(probeAlarmBean);
      }
    }
  }

  private static RealTimeWarn getProbeSignalStrengthAlarmBean(String deviceId, String antName) {
    LambdaQueryWrapper<RealTimeWarn> lqwRwrning = new LambdaQueryWrapper<RealTimeWarn>();
    lqwRwrning.eq(RealTimeWarn::getAlarmType, alarmType);
    lqwRwrning.eq(RealTimeWarn::getHostNumber, deviceId);
    lqwRwrning.eq(RealTimeWarn::getNetworkName, antName);
    lqwRwrning.eq(RealTimeWarn::getAlarmStatus, GConfig.alarmStatus_realTime); // 实时告警
    RealTimeWarn probeAlarmBean = realTimeWarnSevice.getOne(lqwRwrning);
    return probeAlarmBean;
  }
}
