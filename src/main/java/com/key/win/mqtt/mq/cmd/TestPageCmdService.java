package com.key.win.mqtt.mq.cmd;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSONObject;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.model.Probe;
import com.key.win.config.mqtt.MqttPushClient;
import com.key.win.config.mqtt.MqttSubClient;
import com.key.win.mqtt.mq.statilFile.MqParamConfig;
import com.key.win.mqtt.mq.statilFile.MqStaticMap;
import com.key.win.mqtt.mq.statilFile.OpticalStaticList;
import com.key.win.mqtt.mq.vo.ResultVo;
import com.key.win.mqtt.topic.producer.ProduceEmpt;
import com.key.win.mqtt.topic.producer.ProduceService;
import com.key.win.utils.ByteUtil;
import com.key.win.utils.StringToHex;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 主要面向功能测试界面里面的按钮操作
 * @date 2021/10/13 16:09
 */
@Service
@Slf4j
public class TestPageCmdService {

  /** 精确读取 */
  public ResultVo accurateReadingByDeviceId(String deviceId) {
    String fullCmd =
        MqParamConfig.RFID_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.ACCURATE_READING
            + MqParamConfig.ORDER_RESULT_FILL
            + MqParamConfig.DATA_LENGTH_FOUR
            + "01040000";
    log.info("精确读取:{}", fullCmd);
    String key = deviceId + MqParamConfig.RFID_ORDER + "**" + MqParamConfig.ACCURATE_READING;
    int wait = 30 * 1000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /** 主动给设备发送心跳指令 */
  public ResultVo heartBeat1(String deviceId) throws InterruptedException {
    // 心跳给设备
    String fullCmd = "01000101040000000000";
    String key =
        deviceId
            + MqParamConfig.SYSTEM_ORDER
            + "**"
            + MqParamConfig.HEART_BEAT
            + "**"
            + "0400"
            + "00000000";
    int wait = 2500;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /**
   * 告警信息读取
   *
   * @param deviceId 设备ID 如:88883333
   * @return
   * @throws InterruptedException
   */
  public ResultVo alarmInfoRead(String deviceId) {
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.WARE_INFO_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0000";
    log.info("告警信息读取:{}", fullCmd);

    String key = deviceId + MqParamConfig.PARAM_CONFIG_ORDER + "**" + MqParamConfig.WARE_INFO_QUERY;
    int wait = 2500;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /** 故障诊断 7e ffffffff 02 00 10 FF 0400 01040000 09c7 7e */
  public ResultVo faultDiagnosisByDeviceId(String deviceId) {
    int wait = 10 * 1000;
    return faultDiagnosisByDeviceId(deviceId, wait);
  }

  /**
   * 故障诊断
   *
   * @param deviceId 设备编号
   * @param wait,最小值为8000毫秒,最大执行时间为 1分钟 等待时长 ，单位：毫秒
   * @return
   */
  public ResultVo faultDiagnosisByDeviceId(String deviceId, long wait) {
    String fullCmd =
        MqParamConfig.RFID_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.FAULT_DIAGNOSIS
            + MqParamConfig.ORDER_RESULT_FILL
            + MqParamConfig.DATA_LENGTH_FOUR
            + "01040000";
    log.info("故障诊断指令:{},执行等待耗时:{}毫秒", fullCmd, wait);
    String key = deviceId + MqParamConfig.RFID_ORDER + "**" + MqParamConfig.STATUS_REPORTIN;
    if (wait <= 8000 || wait > 1000 * 60) {
      wait = 8000;
    }
    return getResultVoById((int) wait, key, fullCmd, deviceId);
  }

  /**
   * 系统重置命令 0xffffffff 0x01 0x00 0x02 0xFF 0x0000 无 Crc16 0x7e
   *
   * @param deviceId 设备编号
   * @return
   */
  public ResultVo systemReBootByDeviceId(String deviceId) {
    String fullCmd =
        MqParamConfig.SYSTEM_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.SYSTEM_RESTART
            + MqParamConfig.ORDER_RESULT_FILL
            + "0000";
    log.info("系统重置命令{}:", fullCmd);
    String key = deviceId + MqParamConfig.SYSTEM_ORDER + "**" + MqParamConfig.SYSTEM_RESTART;
    int wait = 2500;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /**
   * 查看IP 7E 11118888 04 00 09 FF 0700 07 A800 00000000 641C 7E 0xffffffff 0x01 0x00 0x02 0xFF
   * 0x0000 无 Crc16 0x7e
   *
   * @param deviceId 设备编号
   * @return
   */
  public ResultVo lookIp(String deviceId) {
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "1700"
            + "17A8000000000000000000000000000000000000000000";
    log.info("查看IP:{}", fullCmd);
    String key =
        deviceId
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_QUERY
            + "01"
            + "1700"
            + "17"
            + "a800";
    int wait = 1500;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /**
   * 查询设备探针 7e ffffffff 04 00 01 ff 0000 716f 7e
   *
   * @param deviceId
   * @return
   */
  public ResultVo lookDeviceProbe(String deviceId) {
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.QUERY_DEVICE_PROBE
            + MqParamConfig.ORDER_RESULT_FILL
            + "0000";
    log.info("查询设备探针:{}", fullCmd);
    String key =
        deviceId + MqParamConfig.PARAM_CONFIG_ORDER + "**" + MqParamConfig.QUERY_DEVICE_PROBE;
    int wait = 2500;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /**
   * 开始结束功率 7E 11118888 04 00 09 FF 0800 04 BA01 00 04 BB01 00 8FCC 7E
   *
   * @param deviceId
   * @return
   */
  public ResultVo startEndPower(String deviceId) {
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0800"
            + "04BA010004BB0100";
    log.info("查询起始结束功率:{}", fullCmd);
    String key =
        deviceId
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_QUERY
            + "01"
            + "0800"
            + "04"
            + "ba01";
    int wait = 1000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  public ResultVo queryStartPower(String deviceId) {
    String fullCmd =
            MqParamConfig.PARAM_CONFIG_ORDER
                    + MqParamConfig.DOWN
                    + MqParamConfig.PARAMETER_QUERY
                    + MqParamConfig.ORDER_RESULT_FILL
                    + "0400"
                    + "04BA0100";
    log.info("查询起始功率:{}", fullCmd);
    String key =
            deviceId
                    + MqParamConfig.PARAM_CONFIG_ORDER
                    + "**"
                    + MqParamConfig.PARAMETER_QUERY
                    + "01"
                    + "0400"
                    + "04"
                    + "ba01";
    int wait = 1000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  public ResultVo queryEndPower(String deviceId) {
    String fullCmd =
            MqParamConfig.PARAM_CONFIG_ORDER
                    + MqParamConfig.DOWN
                    + MqParamConfig.PARAMETER_QUERY
                    + MqParamConfig.ORDER_RESULT_FILL
                    + "0400"
                    + "04BB0100";
    log.info("查询结束功率:{}", fullCmd);
    String key =
            deviceId
                    + MqParamConfig.PARAM_CONFIG_ORDER
                    + "**"
                    + MqParamConfig.PARAMETER_QUERY
                    + "01"
                    + "0400"
                    + "04"
                    + "ba01";
    int wait = 1000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /**
   * 功率步进
   * @param deviceId
   * @return
   */
  public ResultVo powerStep(String deviceId) {

    String fullCmd =
            MqParamConfig.PARAM_CONFIG_ORDER
                    + MqParamConfig.DOWN
                    + MqParamConfig.PARAMETER_QUERY
                    + MqParamConfig.ORDER_RESULT_FILL
                    + "0400"
                    + "04C60100";
    log.info("查询功率步进:{}", fullCmd);
    String key =
            deviceId
                    + MqParamConfig.PARAM_CONFIG_ORDER
                    + "**"
                    + MqParamConfig.PARAMETER_QUERY
                    + "01"
                    + "0400"
                    + "04"
                    + "c601";
    int wait = 1000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /**
   * 执行-光交换机光功率查询（包括：端口状态，光收功率，光发功率）
   * @param deviceId
   * @return
   */
  public ResultVo queryOpticalSwitchMsg(String deviceId){
      ResultVo result = new ResultVo();
      /** 光交换机光端口状态 (8个端口) */
      this.opticalPort(deviceId);

      /** 光交换机光收功率(8个端口) */
      String mulRevPowerCmd_1 = OpticalStaticList.receivePowerList_1.stream()
                                                  .map(element -> element + "0000") // 给每个元素追加"0000"
                                                  .collect(Collectors.joining());
      String mulRevPowerCmd_2 = OpticalStaticList.receivePowerList_2.stream()
                                                .map(element -> element + "0000") // 给每个元素追加"0000"
                                                .collect(Collectors.joining());
      //发送光收功率端口(1~4)指令
      this.opticalReceivePower(deviceId,mulRevPowerCmd_1);
      //发送光收功率端口(5~8)指令
      this.opticalReceivePower(deviceId,mulRevPowerCmd_2);

      /** 光交换机光发功率 */
      String mulOutPowerCmd_1 = OpticalStaticList.outputPowerList_1.stream()
                                                .map(element -> element + "0000") // 给每个元素追加"00" //补00查询
                                                .collect(Collectors.joining()); //
      String mulOutPowerCmd_2 = OpticalStaticList.outputPowerList_2.stream()
                                                .map(element -> element + "0000") // 给每个元素追加"00" //补00查询
                                                .collect(Collectors.joining()); //
      //发送光发功率端口(1~4)指令
      this.opticalOutputPower(deviceId,mulOutPowerCmd_1);
      //发送光发功率端口(1~4)指令
      this.opticalOutputPower(deviceId,mulOutPowerCmd_2);
      return result;
  }

  /**
   * 光交换机光端口状态
   * 对8个光端口指令组合成一个指令进行发送：
   * 组合规则是：
   * 1、固定
   *    7E01000100040009FF
   * 2、总长度(32-转换位16进制则值是20)
   *   2000
   * 3、本单元长度+OID+内容
   *  04+C901+00
   * 4、CRC
   *
   * 如网管组合发送指令是：7e16011223040009ff 2000 04c70100 04c80100 04c90100 04ca0100 04cb0100 04cc0100 04cd0100 04ce0100 5e5d257e
   * 如组合后主机反馈指令是：7e1601122304010901 2000 04c70101 04c80101 04c90100 04ca0101 04cb0101 04cc0101 04cd0101 04ce0101 93cd 7e
   * @param deviceId
   * @return
   */
  public ResultVo opticalPort(String deviceId) {
    String mulPortCmd = OpticalStaticList.statusList.stream()
            .map(element -> element + "00") // 给每个元素追加"00" //对每个端口指令补00查询
            .collect(Collectors.joining());

    log.info("查询光端口组合指令:{}",mulPortCmd);
    String fullCmd =
            MqParamConfig.PARAM_CONFIG_ORDER
                    + MqParamConfig.DOWN
                    + MqParamConfig.PARAMETER_QUERY
                    + MqParamConfig.ORDER_RESULT_FILL
                    + "2000" //长度是4*8=32 再对32值转16进制值=0020，再高地位转换=2000
                    + mulPortCmd;
    log.info("查询光交换机光端口状态Cmd:{}",  fullCmd);
    String key =
            deviceId
                    + MqParamConfig.PARAM_CONFIG_ORDER
                    + "**"
                    + MqParamConfig.PARAMETER_QUERY
                    + "01"
                    + "2000"
                    + mulPortCmd;
    int wait = 4000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /**
   * 查询-光交换机光收功率（端口组合）
   * 注意: 8个端口会byte会超长，不能超过50Byte
   * @param deviceId
   * @return
   */
  public ResultVo opticalReceivePower(String deviceId,String mulReceivePowerCmd) {
    String fullCmd =
            MqParamConfig.PARAM_CONFIG_ORDER
                    + MqParamConfig.DOWN
                    + MqParamConfig.PARAMETER_QUERY
                    + MqParamConfig.ORDER_RESULT_FILL
                    + "1400" // 4*5 = 20个长度 再对20转16进制得到14
                    + mulReceivePowerCmd;
    log.info("查询光交换机光【收功率】1-4端口组合指令:{}",  fullCmd);
    String key =
            deviceId
                    + MqParamConfig.PARAM_CONFIG_ORDER
                    + "**"
                    + MqParamConfig.PARAMETER_QUERY
                    + "01"
                    + "1400"
                    + mulReceivePowerCmd;
    int wait = 5000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }



  /**
   * 查询-光交换机光发功率（后4个端口组合） 8个端口会byte会超长（不能超过50Byte）所以4个组合发指令
   * @param deviceId
   * @return
   */
  public ResultVo opticalOutputPower(String deviceId,String outOutPowerCmd) {
    String fullCmd =
            MqParamConfig.PARAM_CONFIG_ORDER
                    + MqParamConfig.DOWN
                    + MqParamConfig.PARAMETER_QUERY
                    + MqParamConfig.ORDER_RESULT_FILL
                    + "1400" // 4个*5长度 = 共20个长度 再对20转16进制得到14
                    + outOutPowerCmd;
    log.info("查询光交换机【发功率】Cmd:{}",  fullCmd);
    String key =
            deviceId
                    + MqParamConfig.PARAM_CONFIG_ORDER
                    + "**"
                    + MqParamConfig.PARAMETER_QUERY
                    + "01"
                    + "1400"
                    + outOutPowerCmd;
    int wait = 5000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }


  /**
   * 发射功率 7E 11118888 04 00 09 FF 0400 04 B301 00 EF7D 7E
   *
   * @param deviceId
   * @return
   */
  public ResultVo launchPower(String deviceId) {
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04B30100";
    log.info("发射功率:{}", fullCmd);
    String key =
        deviceId
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_QUERY
            + "01"
            + "0400"
            + "04"
            + "b301";
    int wait = 2500;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /**
   * 开始功率
   *
   * @param deviceId
   * @return
   */
  public ResultVo startPower(String deviceId) {
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04BA0100";
    log.info("起始功率:{}", fullCmd);
    String key =
        deviceId
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_QUERY
            + "01"
            + "0800"
            + "04"
            + "ba01";
    int wait = 1000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /**
   * 结束功率
   *
   * @param deviceId
   * @return
   */
  public ResultVo endPower(String deviceId) {

    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04BB0100";
    log.info("结束功率:{}", fullCmd);
    String key =
        deviceId
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_QUERY
            + "01"
            + "0800"
            + "04"
            + "bb01";
    int wait = 1000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /**
   * 设备时间 7E 11118888 04 00 09 FF 0A00 0A A300 00000000000000 6568 7E
   *
   * @param deviceId
   * @return
   */
  public ResultVo equipmenTime(String deviceId) {

    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0a00"
            + "0aa30000000000000000";
    log.info("设备时间:{}", fullCmd);
    String key =
        deviceId
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_QUERY
            + "01"
            + "0a00"
            + "0a"
            + "a300";
    int wait = 1000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /**
   * 软件版本
   *
   * @param deviceId 7E 11118888 04 00 09 FF 1700 17 A000 0000000000000000000000000000000000000000
   *     E3EB 7E
   * @return
   */
  public ResultVo softVersion(String deviceId) {
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "1700"
            + "17a0000000000000000000000000000000000000000000";
    log.info("软件版本:{}", fullCmd);
    String key =
        deviceId
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_QUERY
            + "01"
            + "1700"
            + "17"
            + "a000";
    int wait = 1000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /**
   * 设备型号
   *
   * @param deviceId
   * @return
   */
  public ResultVo deviceVersion(String deviceId) {
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "1700"
            + "17A100"
            + "0000000000000000000000000000000000000000";
    log.info("设备型号:{}", fullCmd);
    String key =
        deviceId
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_QUERY
            + "01"
            + "1700"
            + "17"
            + "a100";
    int wait = 1000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /**
   * 生产系列号
   *
   * @param deviceId
   * @return
   */
  public ResultVo productSerVersion(String deviceId) {
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "1700"
            + "17A200"
            + "0000000000000000000000000000000000000000";
    log.info("生产系列号:{}", fullCmd);
    String key =
        deviceId
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_QUERY
            + "01"
            + "1700"
            + "17"
            + "a200";
    int wait = 1000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /**
   * 服务器端口号 7E 11118888 04 00 09 FF 0500 05 A900 0000 3616 7E
   *
   * @param deviceId
   * @return
   */
  public ResultVo serverAno(String deviceId) {

    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0500"
            + "05A9000000";
    log.info("服务器端口号:{}", fullCmd);
    String key =
        deviceId
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_QUERY
            + "01"
            + "0500"
            + "05"
            + "a900";
    int wait = 1000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /**
   * 故障诊断周期 7E 11118888 04 00 09 FF 0500 05 B601 0000 4FEE 7E
   *
   * @param deviceId
   * @return
   */
  public ResultVo faultDiagnosisCycle(String deviceId) {
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0500"
            + "05B6010000";
    log.info("故障诊断周期:{}", fullCmd);
    String key =
        deviceId
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_QUERY
            + "01"
            + "0500"
            + "05"
            + "b601";
    int wait = 1000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /**
   * 故障诊断时间 7E 11118888 04 00 09 FF 0500 05 B501 0000 [CRCX] 7E
   *
   * @param deviceId
   * @return
   */
  public ResultVo faultDiagnosisTime(String deviceId) {
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0500"
            + "05"
            + "B501"
            + "0000";
    log.info("故障诊断时间:{}", fullCmd);
    String key =
        deviceId
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_QUERY
            + "01"
            + "0500"
            + "05"
            + "b501";
    int wait = 1000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /**
   * 故障诊断次数 7E 11118888 04 00 09 FF 0400 04 B701 00 2FA1 7E
   *
   * @param deviceId
   * @return
   */
  public ResultVo faultDiagnosisTimes(String deviceId) {
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04DF0100";
    log.info("故障诊断次数:{}", fullCmd);
    String key =
        deviceId
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_QUERY
            + "01"
            + "0400"
            + "04"
            + "DF01";
    int wait = 1000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /**
   * 频率步进
   *
   * @param deviceId
   * @return
   */
  public ResultVo lookFrequencyStep(String deviceId) {

    String frequencyStep = "0000";
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0500"
            + "05"
            + "b201"
            + frequencyStep;
    log.info("查看频率步进:{}", fullCmd);
    String key =
        deviceId
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_QUERY
            + "01"
            + "0500"
            + "05"
            + "b201";
    int wait = 1000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /**
   * 开始频率
   *
   * @param deviceId
   * @return
   */
  public ResultVo frequencyStart(String deviceId) {

    String dataFill = "00000000";
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0700"
            + "07"
            + "b001"
            + dataFill;
    String key =
        deviceId
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_QUERY
            + "01"
            + "0700"
            + "07"
            + "b001";
    int wait = 1000;
    log.info("Query开始频率:{}", fullCmd);
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /**
   * 结束频率
   *
   * @param deviceId
   * @return
   */
  public ResultVo frequencyEnd(String deviceId) {

    String dataFill = "00000000";
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0700"
            + "07"
            + "b101"
            + dataFill;
    log.info("Query结束频率:{}", fullCmd);
    String key =
        deviceId
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_QUERY
            + "01"
            + "0700"
            + "07"
            + "b101";
    int wait = 1000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /**
   * 查询故障开关
   *
   * @param deviceId
   * @return
   */
  public ResultVo lookFaultSwitch(String deviceId) {
    String troubleshooting = "00";
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04"
            + "b401"
            + troubleshooting;
    log.info("Query查询故障开关:{}", fullCmd);
    String key =
        deviceId
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_QUERY
            + "01"
            + "0400"
            + "04"
            + "b401";
    int wait = 1000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /**
   * 查询精确读取开关
   *
   * @param deviceId
   * @return
   */
  public ResultVo lookAccurateReadingSwitch(String deviceId) {
    String accurateReading = "00";
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04"
            + "b801"
            + accurateReading;
    log.info("查询精确读取开关:{}", fullCmd);
    String key =
        deviceId
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_QUERY
            + "01"
            + "0400"
            + "04"
            + "b801";
    int wait = 1000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  /**
   * 查看精确读取修正值
   *
   * @param deviceId
   * @return
   */
  public ResultVo lookAccuratelyReadTheCorrectionValue(String deviceId) {

    String sourcePowerCorrection = "00";
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04"
            + "b901"
            + sourcePowerCorrection;
    log.info("查看精确读取修正值:{}", fullCmd);
    String key =
        deviceId
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_QUERY
            + "01"
            + "0400"
            + "04"
            + "b901";
    int wait = 1000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  // ================================================================ 下面是设置
  // =============================================

  // fullCmd = "7e11118888040002FF0d000400000000002107220474111191e37e"; //设置天线

  /**
   * 设置设备探针
   *
   * @param probe
   * @return
   */
  public ResultVo setUpProbe(Probe probe) {

    String antCoding = StringUtils.defaultIfBlank(probe.getCoding(), ""); // 探针编号
    String probeSid = StringUtils.defaultIfBlank(probe.getNumber(), "00"); // 探针序号

    int num = 24 - antCoding.length();
    String fillZero = StringUtils.repeat("0", num);
    antCoding = fillZero + antCoding;

    String probeSidStrHex = Integer.toHexString(Integer.parseInt(probeSid));
    if (probeSidStrHex.length() < 2) {
      probeSidStrHex = "0" + probeSidStrHex;
    }

    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.SET_DEVICE_PROBE
            + MqParamConfig.ORDER_RESULT_FILL
            + "0d00"
            + probeSidStrHex
            + antCoding;
    log.info("设置探针,序号:{},编号:{}", probeSidStrHex, antCoding);
    String key =
        probe.getHostNumber()
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.SET_DEVICE_PROBE
            + "**"
            + "0d00";
    int wait = 5000;
    return getResultVoById(wait, key, fullCmd, probe.getHostNumber());
  }

  /**
   * 删除设备探针
   *
   * @param probe
   * @return
   */
  public ResultVo deleteProbe(Probe probe) {

    String antCoding = StringUtils.defaultIfBlank(probe.getCoding(), ""); // 探针编号
    String probeSid = StringUtils.defaultIfBlank(probe.getNumber(), "0");

    int num = 24 - antCoding.length();
    String codingNum = StringUtils.repeat("0", num);
    // 将探针序号转成为数字类型，用于转16进制

    String probeSidHex = Integer.toHexString(Integer.parseInt(probeSid));
    if (probeSidHex.length() < 2) {
      probeSidHex = "0" + probeSidHex;
    }

    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.DELETE_DEVICE_PROBE
            + MqParamConfig.ORDER_RESULT_FILL
            + "0d00"
            + probeSidHex
            + codingNum
            + probe.getCoding();
    log.info("删除探针:{}", fullCmd);
    String key =
        probe.getHostNumber()
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.DELETE_DEVICE_PROBE;
    int wait = 2500;
    return getResultVoById(wait, key, fullCmd, probe.getHostNumber());
  }

  /**
   * 批量删除设备探针
   *
   * @param deviceId
   * @return
   */
  public ResultVo deleteProbeBatch(String deviceId) {

    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.DELETE_DEVICE_PROBE_BATCH
            + MqParamConfig.ORDER_RESULT_FILL
            + "0000";
    log.info("批量删除天线探针:{}", fullCmd);
    String key =
        deviceId
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.DELETE_DEVICE_PROBE_BATCH;
    int wait = 2000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  // 设置开始频率
  public ResultVo setUpStartFrequency(BelongUnit belongUnit) {

    String startFrequency = StringUtils.defaultIfBlank(belongUnit.getStartFrequency(), "0");
    int startFrequencyInteger = (int) (Double.parseDouble(startFrequency) * 100);
    startFrequency = ByteUtil.decimalToHexadecimal(startFrequencyInteger);

    int num = 8 - startFrequency.length();
    String fillZero = StringUtils.repeat("0", num);
    // 前面品拼接0
    startFrequency = fillZero + startFrequency;
    // 高低位转化
    startFrequency = ByteUtil.reverseHex(startFrequency);

    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0700"
            + "07"
            + "b001"
            + startFrequency;

    log.info("设置开始频率:{}", fullCmd);
    String key =
        belongUnit.getHostNum()
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_SETTING
            + "**"
            + "0700"
            + "07"
            + "b001";
    int wait = 5000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  // 设置结束频率
  public ResultVo setUpEndFrequency(BelongUnit belongUnit) {

    // 结束频率
    String endFrequency = StringUtils.defaultIfBlank(belongUnit.getEndFrequency(), "");
    int endFrequencyInteger = (int) (Double.parseDouble(endFrequency) * 100);
    endFrequency = ByteUtil.decimalToHexadecimal(endFrequencyInteger);

    int num = 8 - endFrequency.length();
    String fillZero = StringUtils.repeat("0", num);

    // 前面品拼接0
    endFrequency = fillZero + endFrequency;
    // 高低位转化
    endFrequency = ByteUtil.reverseHex(endFrequency);

    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0700"
            + "07"
            + "b101"
            + endFrequency;

    log.info("设置结束频率:{}", fullCmd);
    String key =
        belongUnit.getHostNum()
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_SETTING
            + "**"
            + "0700"
            + "07"
            + "b101";
    int wait = 5000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  // 设置开始结束频率
  public ResultVo setUpStartAndEndFrequency1(BelongUnit belongUnit) {
    // 开始频率
    String startFrequency = StringUtils.defaultIfBlank(belongUnit.getStartFrequency(), null);
    int start = (int) (Double.parseDouble(startFrequency) * 100);
    startFrequency = ByteUtil.decimalToHexadecimal(start);

    // 结束频率
    String endFrequency = belongUnit.getEndFrequency();
    int end = (int) (Double.parseDouble(endFrequency) * 100);
    endFrequency = ByteUtil.decimalToHexadecimal(end);

    int startNum = 8 - endFrequency.length();
    String fillZeroStart = StringUtils.repeat("0", startNum);
    startFrequency = fillZeroStart + startFrequency;
    // 高低位转化
    startFrequency = ByteUtil.reverseHex(startFrequency);

    int endNum = 8 - endFrequency.length();
    String fillZeroEnd = StringUtils.repeat("0", endNum);

    // 前面品拼接0
    endFrequency = fillZeroEnd + endFrequency;
    // 高低位转化
    endFrequency = ByteUtil.reverseHex(endFrequency);

    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0e00"
            + "07"
            + "b001"
            + startFrequency
            + "07"
            + "b101"
            + endFrequency;

    log.info("设置开始结束频率(联合指令):{}", fullCmd);
    String key =
        belongUnit.getHostNum() + MqParamConfig.PARAMETER_SETTING + "**" + "0e00" + "07" + "b001";
    int wait = 2500;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  // 步进频率
  public ResultVo setFrequencyStep(BelongUnit belongUnit) {

    String frequencyStep = belongUnit.getFrequencyStep();
    int start = (int) (Double.parseDouble(frequencyStep) * 100);
    frequencyStep = ByteUtil.decimalToHexadecimal(start);
    // 高低位转化
    frequencyStep = ByteUtil.reverseHex(frequencyStep);

    int num = 4 - frequencyStep.length();
    String fillZero = StringUtils.repeat("0", num);
    frequencyStep = fillZero + frequencyStep;
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0500"
            + "05"
            + "b201"
            + frequencyStep;
    log.info("步进频率:{}", fullCmd);
    String key =
        belongUnit.getHostNum()
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_SETTING
            + "**"
            + "0500"
            + "05"
            + "b201";
    int wait = 5000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  // 设置发射功率
  public ResultVo setTransmitPower(BelongUnit belongUnit) {

    String transmitPower = belongUnit.getTransmitPower();
    byte[] start = {Byte.parseByte(transmitPower)};
    transmitPower = ByteUtil.bytesToHexString(start);

    if (transmitPower.length() == 1) {
      transmitPower = "0" + transmitPower;
    }
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04"
            + "b301"
            + transmitPower;
    log.info("设置发射功率指令:{}", fullCmd);
    String key =
        belongUnit.getHostNum()
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_SETTING
            + "**"
            + "0400"
            + "04"
            + "b301";
    int wait = 5000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  // 设置故障开关
  public ResultVo setFaultSwitch(BelongUnit belongUnit) {

    String troubleshooting = "0" + (belongUnit.getTroubleshooting() ? 1 : 0);
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04"
            + "b401"
            + troubleshooting;
    log.info("设置故障开关指令:{}", fullCmd);
    String key =
        belongUnit.getHostNum()
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_SETTING
            + "**"
            + "0400"
            + "04"
            + "b401";
    int wait = 5000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  // 设置精确读取开关
  public ResultVo setAccurateReadingSwitch(BelongUnit belongUnit) {
    String accurateReading = "0" + (belongUnit.getAccurateReading() ? 1 : 0);
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04"
            + "b801"
            + accurateReading;
    log.info("设置精确读取开关指令:{}", fullCmd);
    String key =
        belongUnit.getHostNum()
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_SETTING
            + "**"
            + "0400"
            + "04"
            + "b801";
    int wait = 5000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  // 设置精确读取修正值
  public ResultVo setAccuratelyReadTheCorrectionValue(BelongUnit belongUnit) {
    String sourcePowerCorrection = belongUnit.getSourcePowerCorrection();
    byte[] start = {Byte.parseByte(sourcePowerCorrection)};
    sourcePowerCorrection = ByteUtil.bytesToHexString(start);
    if (sourcePowerCorrection.length() == 1) {
      sourcePowerCorrection = "0" + sourcePowerCorrection;
    }
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04"
            + "b901"
            + sourcePowerCorrection;
    log.info("设置精确读取修正值指令:{}", fullCmd);
    String key =
        belongUnit.getHostNum()
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_SETTING
            + "**"
            + "0400"
            + "04"
            + "b901";
    int wait = 7000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  // 设置起始功率,结束功率
  public ResultVo setStartPowerEndPower1(BelongUnit belongUnit) {

    String startPower = belongUnit.getStartPower();
    byte[] start = {Byte.parseByte(startPower)};
    startPower = ByteUtil.bytesToHexString(start);

    String endPower = belongUnit.getEndPower();
    byte[] end = {Byte.parseByte(endPower)};
    endPower = ByteUtil.bytesToHexString(end);

    if (startPower.length() == 1) {
      startPower = "0" + startPower;
    }

    if (endPower.length() == 1) {
      endPower = "0" + endPower;
    }

    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0800"
            + "04"
            + "ba01"
            + startPower
            + "04"
            + "bb01"
            + endPower;

    log.info("设置起始功率,结束功率(联合指令):", fullCmd);
    String key =
        belongUnit.getHostNum() + MqParamConfig.PARAMETER_SETTING + "**" + "0800" + "04" + "ba01";
    int wait = 2500;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  /**
   * 设置起始功率
   *
   * @param belongUnit
   * @return
   */
  public ResultVo setStartPower(BelongUnit belongUnit) {

    String startPower = belongUnit.getStartPower();
    byte[] start = {Byte.parseByte(startPower)};
    startPower = ByteUtil.bytesToHexString(start);

    if (startPower.length() == 1) {
      startPower = "0" + startPower;
    }

    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04"
            + "ba01"
            + startPower;

    log.info("设置起始功率:{}", fullCmd);
    String key =
        belongUnit.getHostNum()
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_SETTING
            + "**"
            + "0400"
            + "04"
            + "ba01";
    int wait = 5000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  // 设置结束功率
  public ResultVo setEndPower(BelongUnit belongUnit) {

    String endPower = belongUnit.getEndPower();
    byte[] end = {Byte.parseByte(endPower)};
    endPower = ByteUtil.bytesToHexString(end);

    if (endPower.length() == 1) {
      endPower = "0" + endPower;
    }

    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04"
            + "bb01"
            + endPower;

    log.info("设置结束功率{}:", fullCmd);
    String key =
        belongUnit.getHostNum()
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_SETTING
            + "**"
            + "0400"
            + "04"
            + "bb01";
    int wait = 5000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  // 设置功率步进
  public ResultVo setPowerStep(BelongUnit belongUnit) {
    String powerStep = belongUnit.getPowerStep();
    byte[] step = {Byte.parseByte(powerStep)};
    powerStep = ByteUtil.bytesToHexString(step);

    if (powerStep.length() == 1) {
      powerStep = "0" + powerStep;
    }

    String fullCmd =
            MqParamConfig.PARAM_CONFIG_ORDER
                    + MqParamConfig.DOWN
                    + MqParamConfig.PARAMETER_SETTING
                    + MqParamConfig.ORDER_RESULT_FILL
                    + "0400"
                    + "04"
                    + "c601"
                    + powerStep;

    log.info("设置功率步进:{}", fullCmd);
    String key =
            belongUnit.getHostNum()
                    + MqParamConfig.PARAM_CONFIG_ORDER
                    + "**"
                    + MqParamConfig.PARAMETER_SETTING
                    + "**"
                    + "0400"
                    + "04"
                    + "c601";
    int wait = 5000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  // 设置告警判断次数（2024-05-23）
  public ResultVo setAlarmJudgeCount(BelongUnit belongUnit) {
    String alarmJudgeCount = belongUnit.getAlarmJudgeCount();
    int start = Integer.parseInt(alarmJudgeCount);
    alarmJudgeCount = ByteUtil.decimalToHexadecimal(start);
    if (alarmJudgeCount.length() == 1) {
      alarmJudgeCount = "0" + alarmJudgeCount;
    }
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04"
            + "e001"
            + alarmJudgeCount;
    log.info("设置告警判断次数:{}", fullCmd);
    String key =
        belongUnit.getHostNum() + MqParamConfig.PARAMETER_SETTING + "**" + "0400" + "04" + "e001";
    int wait = 7000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  // 设置故障诊断次数
  public ResultVo setFaultDiagnosisNumber(BelongUnit belongUnit) {
    String faultDiagnosisTime = belongUnit.getFaultDiagnosisNumber();
    int start = Integer.parseInt(faultDiagnosisTime);
    faultDiagnosisTime = ByteUtil.decimalToHexadecimal(start);
    if (faultDiagnosisTime.length() == 1) {
      faultDiagnosisTime = "0" + faultDiagnosisTime;
    }
    String fullCmd =
            MqParamConfig.PARAM_CONFIG_ORDER
                    + MqParamConfig.DOWN
                    + MqParamConfig.PARAMETER_SETTING
                    + MqParamConfig.ORDER_RESULT_FILL
                    + "0400"
                    + "04"
                    + "df01"
                    + faultDiagnosisTime;
    log.info("设置故障诊断次数:{}", fullCmd);
    String key =
            belongUnit.getHostNum() + MqParamConfig.PARAMETER_SETTING + "**" + "0400" + "04" + "df01";
    int wait = 7000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  /**
   * 故障诊断周期 (与故障诊断时间是同一个意思，即：诊断时间是1分钟，周期就是一分钟一次)
   *
   * @param belongUnit
   * @return
   */
  public ResultVo setTheFaultDiagnosisCycle(BelongUnit belongUnit) {
    String faultDiagnosisCycle = belongUnit.getFaultDiagnosisCycle();
    if (StringUtils.isBlank(faultDiagnosisCycle)) {
      log.error("参数【故障诊断周期】为空～");
      return null;
    }
    if (faultDiagnosisCycle.length() == 1) {
      faultDiagnosisCycle = "0" + faultDiagnosisCycle;
    }
    byte[] str2Bcd = ByteUtil.str2Bcd(faultDiagnosisCycle);
    String diagnosisCycle = ByteUtil.bytesToHexString(str2Bcd);

    // BCD码不用高低位转换
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0500"
            + "05"
            + "b601"
            + diagnosisCycle;
    log.info("设置故障诊断周期:{}", fullCmd);
    String key =
        belongUnit.getHostNum() + MqParamConfig.PARAMETER_SETTING + "**" + "0500" + "05" + "b601";
    int wait = 2500;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  // 设置故障诊断周期时间 （故障诊断周期与故障诊断时间 合并 ）
  public ResultVo setFaultDiagnosisTime(BelongUnit belongUnit) {
    String faultCycleTime = belongUnit.getFaultDiagnosisTime();
    if (StringUtils.isBlank(faultCycleTime)) {
      log.error("参数【设置故障诊断周期时间】为空～");
      return null;
    }
    if (faultCycleTime.length() == 1) {
      faultCycleTime = "0" + faultCycleTime;
    }
    int num = 4 - faultCycleTime.length();
    String fillZero  = StringUtils.repeat("0", num); //补充缺少的0
    faultCycleTime =  faultCycleTime + fillZero;

    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0500"
            + "05"
            + "b601"
            + faultCycleTime;
    log.info("设置故障诊断周期时间:{}", fullCmd);
    String key =
        belongUnit.getHostNum() + MqParamConfig.PARAMETER_SETTING + "**" + "0500" + "05" + "b601";
    int wait = 5000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  // 设置服务器ip
  public ResultVo setUpTheServer(BelongUnit belongUnit) {
    String ipAddress = belongUnit.getIpAddress();
    log.info("设置服务器ip指令(转义前报文):{}", ipAddress);
    if (StringUtils.isBlank(ipAddress)) {
      return null;
    }
    ipAddress = StringToHex.convertStringToHex(ipAddress);
    int num = 40 - ipAddress.length();
    String fillZero = StringUtils.repeat("0", num);

    ipAddress = ipAddress + fillZero;
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "1700"
            + "17"
            + "ab00"
            + ipAddress;
    log.info("设置服务器ip（转义后）:{},完整指令:{}", ipAddress, fullCmd);
    String key =
        belongUnit.getHostNum() + MqParamConfig.PARAMETER_SETTING + "**" + "1700" + "17" + "ab00";
    int wait = 5000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  /**
   * 设置端口号
   *
   * @param belongUnit 主机相关信息
   * @return 结果
   */
  public ResultVo setUpThePort(BelongUnit belongUnit) {

    String port = belongUnit.getPort();
    int start = Integer.parseInt(port);
    port = ByteUtil.decimalToHexadecimal(start);
    int num = 4 - port.length();
    String fillZero = StringUtils.repeat("0", num);
    port = fillZero + port;
    // 高低位转化
    port = ByteUtil.reverseHex(port);
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0500"
            + "05"
            + "A900"
            + port;
    log.info("设置端口号指令:{}", fullCmd);
    String key =
        belongUnit.getHostNum() + MqParamConfig.PARAMETER_SETTING + "**" + "0500" + "05" + "05";
    int wait = 2500;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  /**
   * 设置设备时间
   * @param belongUnit
   * @return
   */
  public ResultVo setEquipmenTime(BelongUnit belongUnit) {
    String nowTime = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
    log.info("nowTime:{}",nowTime);
    nowTime = ByteUtil.convertToBcdHex(nowTime);
    String fullCmd =
            MqParamConfig.PARAM_CONFIG_ORDER
                    + MqParamConfig.DOWN
                    + MqParamConfig.PARAMETER_SETTING
                    + MqParamConfig.ORDER_RESULT_FILL
                    + "0A00"
                    + "0A"
                    + "A300"
                    + nowTime;
    log.info("设置设备时间指令:{}", fullCmd);
    String key = belongUnit.getHostNum() + MqParamConfig.PARAMETER_SETTING + "**" + "0A00"+ "0A" + "A300";
    int wait = 2500;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  private ResultVo getResultVoById(int wait, String key, String fullCmd, String deviceId) {
    // 4个字节高低位转换
    String originCode = getResultId(fullCmd, deviceId);
    //log.info("originCode:{},length:{}",originCode,originCode.length());
    MqStaticMap.map.put(key, new ArrayList<>());

    ThreadPoolExecutor p = ThreadUtil.newExecutorByBlockingCoefficient(0.5f);

    MqttSubClient.getInstance().subscribe("reader_" + deviceId);

    ProduceService myThread =
        new ProduceService(MqttPushClient.getInstance(), deviceId, originCode, wait);
    p.execute(myThread);
    ProduceEmpt produce = new ProduceEmpt(wait);
    p.execute(produce);
    p.shutdown();
    while (!p.isTerminated()) {}

    ResultVo resultVo = new ResultVo();
    resultVo.setOriginCode(originCode);
    List<String> list = MqStaticMap.map.remove(key);
    resultVo.setResultCode(list);
    return resultVo;
  }

  private String getResultId(String fullCmd, String deviceId) {
    deviceId = ByteUtil.reverseHex(deviceId);
    fullCmd = deviceId + fullCmd;
    fullCmd = fullCmd.replace(" ", "");
    String crc16 = ByteUtil.crc(fullCmd);
    String _cmd = fullCmd + crc16;
    byte[] bytes = ByteUtil.hexStringToByteArray(_cmd);
    String escapedStr = ByteUtil.escapeBefore(bytes);
    fullCmd = MqParamConfig.PREFIX_SUFFIX + escapedStr + MqParamConfig.PREFIX_SUFFIX;
    log.info("设备主机:{},CRC:{},转义前:{},转义后:{},完整指令:{}", deviceId, crc16, _cmd, escapedStr, fullCmd);
    return fullCmd;
  }
}
