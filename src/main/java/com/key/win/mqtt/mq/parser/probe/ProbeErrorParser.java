package com.key.win.mqtt.mq.parser.probe;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.TEL.bootstrap.TELService;
import com.key.win.biz.alarm.model.RealTimeWarn;
import com.key.win.biz.alarm.service.RealTimeWarnSevice;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.config.RedisUtil;
import com.key.win.config.mqtt.MqttPushClient;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.utils.SpringUtils;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@SuppressWarnings("rawtypes")
@Slf4j
public class ProbeErrorParser {

  private static BelongUnitService belongUnitService = SpringUtils.getBean(BelongUnitService.class);
  private static RedisUtil redisUtil = SpringUtils.getBean(RedisUtil.class);
  private static RealTimeWarnSevice realTimeWarnSevice =
      SpringUtils.getBean(RealTimeWarnSevice.class);

  private static String alarmType = GConfig.alarmType_antErr;

  public static void resolve(Map payload) {

    String deviceId = MapUtil.getStr(payload, "deviceId");
    String sid = MapUtil.getStr(payload, "sid");
    String probeId = MapUtil.getStr(payload, "probeId");
    String antName = MapUtil.getStr(payload, "antName");
    String nodeDesc = MapUtil.getStr(payload, "nodeDesc");
    nodeDesc = StringUtils.defaultIfBlank(nodeDesc, "暂无");
    RealTimeWarn probeAlarmBean = getProbeAlarmBean(deviceId, antName);
    if (probeAlarmBean != null) {
      probeAlarmBean.setAlarmTimes(probeAlarmBean.getAlarmTimes() + 1);
      probeAlarmBean.setRemark("天线异常: [" + antName + "]天线故障 位置: " + nodeDesc);
      probeAlarmBean.setAttr1(GConfig.ALARM_REPORT_FLAG_0);
      realTimeWarnSevice.updateById(probeAlarmBean);
    } else {
      BelongUnit unit = getUnitByDeviceId(deviceId);
      probeAlarmBean = buildAlarmBean(deviceId, antName, nodeDesc, unit);
      realTimeWarnSevice.save(probeAlarmBean);

      // 获取配置项，是否开启实时推送
      String _whetherToReportAlarmsInRealTime = SpringUtils.getProperty("sf.alarm.report");
      if (Boolean.parseBoolean(_whetherToReportAlarmsInRealTime)) {
        log.info(">>>>>>>>>>>>>>>告警上报模式:[实时]上报>>>>>>>>>>>>>>>");
        // 此处实时告警主动上报代码块
        String reportMsg =
            TELService.packageMsgProactiveReporting(
                belongUnitService, probeAlarmBean, 1, TELService.reportMsgFF);
        if (StringUtils.isBlank(reportMsg)) {
          return;
        }
        MqttPushClient.getInstance()
            .publish2CsharpClient(GConfig.MQ_TOPIC_CSHARP_CLIENT_ALERT_INFO, reportMsg);
        log.info("设备：{}，天线：{}，sid:{}，发生故障，告警上报[{}]", deviceId, antName, sid, reportMsg);
        // 主动告警代码==结束
      } else {
        String _delayTime = SpringUtils.getProperty("sf.alarm.delay");
        int delayTime = Integer.parseInt(_delayTime);
        String key = "sf:alarm:report:" + probeAlarmBean.getId();
        String now = DateUtil.now();
        redisUtil.set(key, now, delayTime);
        log.info(">>>>>>>>>>>>>>>告警上报模式:[延时]上报,延迟时间:{},当前监听key:{}>>>>>>>>>>>>>>>", delayTime, key);
      }
    }
  }

  private static RealTimeWarn getProbeAlarmBean(String deviceId, String antName) {
    LambdaQueryWrapper<RealTimeWarn> lqwRwrning = new LambdaQueryWrapper<RealTimeWarn>();
    lqwRwrning.eq(RealTimeWarn::getAlarmType, alarmType);
    lqwRwrning.eq(RealTimeWarn::getHostNumber, deviceId);
    lqwRwrning.eq(RealTimeWarn::getNetworkName, antName);
    lqwRwrning.eq(RealTimeWarn::getAlarmStatus, GConfig.alarmStatus_realTime); // 实时告警
    RealTimeWarn probeAlarmBean = realTimeWarnSevice.getOne(lqwRwrning);
    return probeAlarmBean;
  }

  private static BelongUnit getUnitByDeviceId(String deviceId) {
    LambdaQueryWrapper<BelongUnit> lqwUnit = new LambdaQueryWrapper<BelongUnit>();
    lqwUnit.eq(BelongUnit::getHostNum, deviceId);
    BelongUnit one = belongUnitService.getOne(lqwUnit);
    return one;
  }

  private static RealTimeWarn buildAlarmBean(
      String deviceId, String antName, String nodeDesc, BelongUnit unit) {
    nodeDesc = StringUtils.defaultIfBlank(nodeDesc, "暂无");
    Date currentTime = new Date();
    RealTimeWarn alarmBean =
        RealTimeWarn.builder()
            .hostNumber(deviceId)
            .alarmTimes(1)
            .attr1(GConfig.ALARM_REPORT_FLAG_0)
            .alarmStatus(GConfig.alarmStatus_realTime)
            .alarmName("[" + deviceId + "] [" + antName + "] 天线故障")
            .belongStationId(unit.getStationId())
            .belongStationName(unit.getStationName())
            .alarmTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(currentTime))
            .remark("天线异常: [" + antName + "]天线故障 位置: " + nodeDesc)
            .networkName(antName)
            .equipmentType(GConfig.equipmentType_Ant)
            .alarmType(alarmType)
            .build();
    return alarmBean;
  }
}
