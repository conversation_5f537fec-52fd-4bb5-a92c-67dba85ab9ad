package com.key.win.mqtt.mq.cmd;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.model.Probe;
import com.key.win.config.mqtt.MqttPushClient;
import com.key.win.config.mqtt.MqttSubClient;
import com.key.win.mqtt.mq.statilFile.MqParamConfig;
import com.key.win.mqtt.mq.statilFile.MqStaticMap;
import com.key.win.mqtt.mq.vo.ResultVo;
import com.key.win.mqtt.topic.producer.ProduceEmpt;
import com.key.win.mqtt.topic.producer.ProduceService;
import com.key.win.utils.ByteUtil;
import com.key.win.utils.StringToHex;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 设置指令业务类
 * @date 2021/10/13 16:09
 */
@Service
@Slf4j
public class SettingCommandService {

  /*  public static void main(String[] args) {
    String v = "1703232004000AFF040004bb0121";


    String crc16 = ByteUtil.crc(v);
    byte[] bytes = ByteUtil.hexStringToBytes(crc16);
    String crcSend = ByteUtil.crcSend(bytes);
    String fullCmd = MqParamConfig.PREFIX_SUFFIX + v + crcSend + MqParamConfig.PREFIX_SUFFIX;
    log.info("设备主机:{},完整的报文指令:{},CRC校验值:{}", 2222, fullCmd, crcSend);
  }*/

  /**
   * 设置设备探针
   *
   * @param probe
   * @return
   */
  public ResultVo setUpProbe(Probe probe) {

    String antCoding = StringUtils.defaultIfBlank(probe.getCoding(), ""); // 探针编号
    String probeSid = StringUtils.defaultIfBlank(probe.getNumber(), "00"); // 探针序号

    int num = 24 - antCoding.length();
    String fillZero = StringUtils.repeat("0", num);
    antCoding = fillZero + antCoding;

    String probeSidStrHex = Integer.toHexString(Integer.parseInt(probeSid));
    if (probeSidStrHex.length() < 2) {
      probeSidStrHex = "0" + probeSidStrHex;
    }

    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.SET_DEVICE_PROBE
            + MqParamConfig.ORDER_RESULT_FILL
            + "0d00"
            + probeSidStrHex
            + antCoding;
    log.info("设置探针:{}", fullCmd);
    String key =
        probe.getHostNumber()
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.SET_DEVICE_PROBE
            + "**"
            + "0d00";
    int wait = 5000;
    return getResultVoById(wait, key, fullCmd, probe.getHostNumber());
  }

  /**
   * 删除设备探针
   *
   * @param probe
   * @return
   */
  public ResultVo deleteProbe(Probe probe) {

    String antCoding = StringUtils.defaultIfBlank(probe.getCoding(), ""); // 探针编号
    String probeSid = StringUtils.defaultIfBlank(probe.getNumber(), "0");

    int num = 24 - antCoding.length();
    String codingNum = StringUtils.repeat("0", num);
    // 将探针序号转成为数字类型，用于转16进制

    String probeSidHex = Integer.toHexString(Integer.parseInt(probeSid));
    if (probeSidHex.length() < 2) {
      probeSidHex = "0" + probeSidHex;
    }

    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.DELETE_DEVICE_PROBE
            + MqParamConfig.ORDER_RESULT_FILL
            + "0d00"
            + probeSidHex
            + codingNum
            + probe.getCoding();
    log.info("删除探针:{}", fullCmd);
    String key =
        probe.getHostNumber()
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.DELETE_DEVICE_PROBE;
    int wait = 2500;
    return getResultVoById(wait, key, fullCmd, probe.getHostNumber());
  }

  /**
   * 批量删除设备探针
   *
   * @param deviceId
   * @return
   */
  public ResultVo deleteProbeBatch(String deviceId) {

    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.DELETE_DEVICE_PROBE_BATCH
            + MqParamConfig.ORDER_RESULT_FILL
            + "0000";
    log.info("批量删除天线探针:{}", fullCmd);
    String key =
        deviceId
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.DELETE_DEVICE_PROBE_BATCH;
    int wait = 2000;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  // 设置开始频率
  public ResultVo setUpStartFrequency(BelongUnit belongUnit) {

    String startFrequency = StringUtils.defaultIfBlank(belongUnit.getStartFrequency(), "0");
    int startFrequencyInteger = (int) (Double.parseDouble(startFrequency) * 100);
    startFrequency = ByteUtil.decimalToHexadecimal(startFrequencyInteger);

    int num = 8 - startFrequency.length();
    String fillZero = StringUtils.repeat("0", num);
    // 前面品拼接0
    startFrequency = fillZero + startFrequency;
    // 高低位转化
    startFrequency = ByteUtil.reverseHex(startFrequency);

    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0700"
            + "07"
            + "b001"
            + startFrequency;

    log.info("设置开始频率:{}", fullCmd);
    String key =
        belongUnit.getHostNum()
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_SETTING
            + "**"
            + "0700"
            + "07"
            + "b001";
    int wait = 5000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  // 设置结束频率
  public ResultVo setUpEndFrequency(BelongUnit belongUnit) {

    // 结束频率
    String endFrequency = StringUtils.defaultIfBlank(belongUnit.getEndFrequency(), "");
    int endFrequencyInteger = (int) (Double.parseDouble(endFrequency) * 100);
    endFrequency = ByteUtil.decimalToHexadecimal(endFrequencyInteger);

    int num = 8 - endFrequency.length();
    String fillZero = StringUtils.repeat("0", num);

    // 前面品拼接0
    endFrequency = fillZero + endFrequency;
    // 高低位转化
    endFrequency = ByteUtil.reverseHex(endFrequency);

    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0700"
            + "07"
            + "b101"
            + endFrequency;

    log.info("设置结束频率:{}", fullCmd);
    String key =
        belongUnit.getHostNum()
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_SETTING
            + "**"
            + "0700"
            + "07"
            + "b101";
    int wait = 5000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  // 设置开始结束频率
  public ResultVo setUpStartAndEndFrequency1(BelongUnit belongUnit) {
    // 开始频率
    String startFrequency = StringUtils.defaultIfBlank(belongUnit.getStartFrequency(), null);
    int start = (int) (Double.parseDouble(startFrequency) * 100);
    startFrequency = ByteUtil.decimalToHexadecimal(start);

    // 结束频率
    String endFrequency = belongUnit.getEndFrequency();
    int end = (int) (Double.parseDouble(endFrequency) * 100);
    endFrequency = ByteUtil.decimalToHexadecimal(end);

    int startNum = 8 - endFrequency.length();
    String fillZeroStart = StringUtils.repeat("0", startNum);
    startFrequency = fillZeroStart + startFrequency;
    // 高低位转化
    startFrequency = ByteUtil.reverseHex(startFrequency);

    int endNum = 8 - endFrequency.length();
    String fillZeroEnd = StringUtils.repeat("0", endNum);

    // 前面品拼接0
    endFrequency = fillZeroEnd + endFrequency;
    // 高低位转化
    endFrequency = ByteUtil.reverseHex(endFrequency);

    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0e00"
            + "07"
            + "b001"
            + startFrequency
            + "07"
            + "b101"
            + endFrequency;

    log.info("设置开始结束频率(联合指令):{}", fullCmd);
    String key =
        belongUnit.getHostNum() + MqParamConfig.PARAMETER_SETTING + "**" + "0e00" + "07" + "b001";
    int wait = 2500;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  // 步进频率
  public ResultVo setFrequencyStep(BelongUnit belongUnit) {

    String frequencyStep = belongUnit.getFrequencyStep();
    int start = (int) (Double.parseDouble(frequencyStep) * 100);
    frequencyStep = ByteUtil.decimalToHexadecimal(start);
    // 高低位转化
    frequencyStep = ByteUtil.reverseHex(frequencyStep);

    int num = 4 - frequencyStep.length();
    String fillZero = StringUtils.repeat("0", num);
    frequencyStep = fillZero + frequencyStep;
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0500"
            + "05"
            + "b201"
            + frequencyStep;
    log.info("步进频率:{}", fullCmd);
    String key =
        belongUnit.getHostNum()
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_SETTING
            + "**"
            + "0500"
            + "05"
            + "b201";
    int wait = 5000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  // 设置发射功率
  public ResultVo setTransmitPower(BelongUnit belongUnit) {

    String transmitPower = belongUnit.getTransmitPower();
    byte[] start = {Byte.parseByte(transmitPower)};
    transmitPower = ByteUtil.bytesToHexString(start);

    if (transmitPower.length() == 1) {
      transmitPower = "0" + transmitPower;
    }
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04"
            + "b301"
            + transmitPower;
    log.info("设置发射功率指令:{}", fullCmd);
    String key =
        belongUnit.getHostNum()
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_SETTING
            + "**"
            + "0400"
            + "04"
            + "b301";
    int wait = 5000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  /**
   * 设置功率步进（2024-05-22添加）
   * @param belongUnit
   * @return
   */
  public ResultVo setPowerStep(BelongUnit belongUnit) {
    String powerStep = belongUnit.getPowerStep();
    if(StringUtils.isBlank(powerStep)){
      log.info("网管平台未设置功率步进值");
      return null;
    }
    byte[] start = {Byte.parseByte(powerStep)};
    powerStep = ByteUtil.bytesToHexString(start);

    if (powerStep.length() == 1) {
      powerStep = "0" + powerStep;
    }
    String fullCmd =
            MqParamConfig.PARAM_CONFIG_ORDER
                    + MqParamConfig.DOWN
                    + MqParamConfig.PARAMETER_SETTING
                    + MqParamConfig.ORDER_RESULT_FILL
                    + "0400"
                    + "04"
                    + "c601"
                    + powerStep;
    log.info("设置功率步进指令:{}", fullCmd);
    String key =
            belongUnit.getHostNum()
                    + MqParamConfig.PARAM_CONFIG_ORDER
                    + "**"
                    + MqParamConfig.PARAMETER_SETTING
                    + "**"
                    + "0400"
                    + "04"
                    + "c601";
    int wait = 5000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  /**
   * 设置设备时间
   * @param deviceId
   * @return
   */
  public ResultVo setEquipmenTime(String  deviceId) {
    String nowTime = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
    log.info("nowTime:{}",nowTime);
    nowTime = ByteUtil.convertToBcdHex(nowTime);
    String fullCmd =
            MqParamConfig.PARAM_CONFIG_ORDER
                    + MqParamConfig.DOWN
                    + MqParamConfig.PARAMETER_SETTING
                    + MqParamConfig.ORDER_RESULT_FILL
                    + "0A00"
                    + "0A"
                    + "A300"
                    + nowTime;
    log.info("设置设备时间指令:{}", fullCmd);
    String key = deviceId + MqParamConfig.PARAMETER_SETTING + "**" + "0A00"+"0A" + "A300";
    int wait = 2500;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  // 设置故障开关
  public ResultVo setFaultSwitch(BelongUnit belongUnit) {

    String troubleshooting = "0" + (belongUnit.getTroubleshooting() ? 1 : 0);
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04"
            + "b401"
            + troubleshooting;
    log.info("设置故障开关指令:{}", fullCmd);
    String key =
        belongUnit.getHostNum()
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_SETTING
            + "**"
            + "0400"
            + "04"
            + "b401";
    int wait = 5000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  // 设置精确读取开关
  public ResultVo setAccurateReadingSwitch(BelongUnit belongUnit) {
    String accurateReading = "0" + (belongUnit.getAccurateReading() ? 1 : 0);
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04"
            + "b801"
            + accurateReading;
    log.info("设置精确读取开关指令:{}", fullCmd);
    String key =
        belongUnit.getHostNum()
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_SETTING
            + "**"
            + "0400"
            + "04"
            + "b801";
    int wait = 5000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  // 设置精确读取修正值
  public ResultVo setAccuratelyReadTheCorrectionValue(BelongUnit belongUnit) {
    String sourcePowerCorrection = belongUnit.getSourcePowerCorrection();
    byte[] start = {Byte.parseByte(sourcePowerCorrection)};
    sourcePowerCorrection = ByteUtil.bytesToHexString(start);
    if (sourcePowerCorrection.length() == 1) {
      sourcePowerCorrection = "0" + sourcePowerCorrection;
    }
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04"
            + "b901"
            + sourcePowerCorrection;
    log.info("设置精确读取修正值指令:{}", fullCmd);
    String key =
        belongUnit.getHostNum()
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_SETTING
            + "**"
            + "0400"
            + "04"
            + "b901";
    int wait = 7000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  // 设置起始功率,结束功率
  public ResultVo setStartPowerEndPower1(BelongUnit belongUnit) {

    String startPower = belongUnit.getStartPower();
    byte[] start = {Byte.parseByte(startPower)};
    startPower = ByteUtil.bytesToHexString(start);

    String endPower = belongUnit.getEndPower();
    byte[] end = {Byte.parseByte(endPower)};
    endPower = ByteUtil.bytesToHexString(end);

    if (startPower.length() == 1) {
      startPower = "0" + startPower;
    }

    if (endPower.length() == 1) {
      endPower = "0" + endPower;
    }

    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0800"
            + "04"
            + "ba01"
            + startPower
            + "04"
            + "bb01"
            + endPower;

    log.info("设置起始功率,结束功率(联合指令):", fullCmd);
    String key =
        belongUnit.getHostNum() + MqParamConfig.PARAMETER_SETTING + "**" + "0800" + "04" + "ba01";
    int wait = 2500;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  /**
   * 设置起始功率
   *
   * @param belongUnit
   * @return
   */
  public ResultVo setStartPower(BelongUnit belongUnit) {

    String startPower = belongUnit.getStartPower();
    byte[] start = {Byte.parseByte(startPower)};
    startPower = ByteUtil.bytesToHexString(start);

    if (startPower.length() == 1) {
      startPower = "0" + startPower;
    }

    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04"
            + "ba01"
            + startPower;

    log.info("设置起始功率:{}", fullCmd);
    String key =
        belongUnit.getHostNum()
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_SETTING
            + "**"
            + "0400"
            + "04"
            + "ba01";
    int wait = 5000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  // 设置结束功率
  public ResultVo setEndPower(BelongUnit belongUnit) {

    String endPower = belongUnit.getEndPower();
    byte[] end = {Byte.parseByte(endPower)};
    endPower = ByteUtil.bytesToHexString(end);

    if (endPower.length() == 1) {
      endPower = "0" + endPower;
    }

    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04"
            + "bb01"
            + endPower;

    log.info("设置结束功率{}:", fullCmd);
    String key =
        belongUnit.getHostNum()
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_SETTING
            + "**"
            + "0400"
            + "04"
            + "bb01";
    int wait = 5000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  // 设置故障诊断次数
  public ResultVo setFaultDiagnosisNumber(BelongUnit belongUnit) {
    String faultDiagnosisTime = belongUnit.getFaultDiagnosisNumber();
    int start = Integer.parseInt(faultDiagnosisTime);
    faultDiagnosisTime = ByteUtil.decimalToHexadecimal(start);
    if (faultDiagnosisTime.length() == 1) {
      faultDiagnosisTime = "0" + faultDiagnosisTime;
    }
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04"
            + "b701"
            + faultDiagnosisTime;
    log.info("设置故障诊断次数:{}", fullCmd);
    String key =
        belongUnit.getHostNum() + MqParamConfig.PARAMETER_SETTING + "**" + "0400" + "04" + "b901";
    int wait = 7000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  /**
   * 故障诊断周期
   *
   * @param belongUnit
   * @return
   */
  public ResultVo setTheFaultDiagnosisCycle(BelongUnit belongUnit) {

    DateTime parse = DateUtil.parse(belongUnit.getFaultDiagnosisCycle());
    String format = new SimpleDateFormat("HHmm").format(parse.toJdkDate());
    byte[] str2Bcd = ByteUtil.str2Bcd(format);
    String diagnosisCycle = ByteUtil.bytesToHexString(str2Bcd);

    // BCD码不用高低位转换
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0500"
            + "05"
            + "b601"
            + diagnosisCycle;
    log.info("设置故障诊断周期:{}", fullCmd);
    String key =
        belongUnit.getHostNum() + MqParamConfig.PARAMETER_SETTING + "**" + "0500" + "05" + "b601";
    int wait = 2500;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  // 设置故障诊断时间
  public ResultVo setFaultDiagnosisTime(BelongUnit belongUnit) {
    String faultTime = belongUnit.getFaultDiagnosisTime();
    DateTime parse = DateUtil.parse(faultTime);
    String hh24mm = new SimpleDateFormat("HHmm").format(parse.toJdkDate());

    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0500"
            + "05"
            + "b501"
            + hh24mm;
    log.info("设置故障诊断时间:{}", fullCmd);
    String key =
        belongUnit.getHostNum() + MqParamConfig.PARAMETER_SETTING + "**" + "0500" + "05" + "b501";
    int wait = 5000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }


  // 设置服务器ip
  public ResultVo setUpTheServer(BelongUnit belongUnit) {
    String ipAddress = belongUnit.getIpAddress();
    log.info("设置服务器ip指令(转义前报文):{}", ipAddress);
    if (StringUtils.isBlank(ipAddress)) {
      return null;
    }
    ipAddress = StringToHex.convertStringToHex(ipAddress);
    int num = 40 - ipAddress.length();
    String fillZero = StringUtils.repeat("0", num);

    ipAddress = ipAddress + fillZero;
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "1700"
            + "17"
            + "ab00"
            + ipAddress;
    log.info("设置服务器ip（转义后）:{},完整指令:{}", ipAddress, fullCmd);
    String key =
        belongUnit.getHostNum() + MqParamConfig.PARAMETER_SETTING + "**" + "1700" + "17" + "ab00";
    int wait = 5000;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  /**
   * 设置端口号
   *
   * @param belongUnit 主机相关信息
   * @return 结果
   */
  public ResultVo setUpThePort(BelongUnit belongUnit) {

    String port = belongUnit.getPort();
    int start = Integer.parseInt(port);
    port = ByteUtil.decimalToHexadecimal(start);
    int num = 4 - port.length();
    String fillZero = StringUtils.repeat("0", num);
    port = fillZero + port;
    // 高低位转化
    port = ByteUtil.reverseHex(port);
    String fullCmd =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0500"
            + "05"
            + "A900"
            + port;
    log.info("设置端口号指令:{}", fullCmd);
    String key =
        belongUnit.getHostNum() + MqParamConfig.PARAMETER_SETTING + "**" + "0500" + "05" + "05";
    int wait = 2500;
    if (belongUnit.getTimer() != null) {
      wait = belongUnit.getTimer();
    }
    return getResultVoById(wait, key, fullCmd, belongUnit.getHostNum());
  }

  private ResultVo getResultVoById(int wait, String key, String fullCmd, String deviceId) {
    // 4个字节高低位转换
    String originCode = getResultId(fullCmd, deviceId);
    MqStaticMap.map.put(key, new ArrayList<>());

    ThreadPoolExecutor p = ThreadUtil.newExecutorByBlockingCoefficient(0.5f);

    MqttSubClient.getInstance().subscribe("reader_" + deviceId);

    ProduceService myThread =
        new ProduceService(MqttPushClient.getInstance(), deviceId, originCode, wait);
    p.execute(myThread);
    ProduceEmpt produce = new ProduceEmpt(wait);
    p.execute(produce);
    p.shutdown();
    while (!p.isTerminated()) {}

    ResultVo resultVo = new ResultVo();
    resultVo.setOriginCode(originCode);
    List<String> list = MqStaticMap.map.remove(key);
    resultVo.setResultCode(list);
    return resultVo;
  }

  public ResultVo rebootSystem(String deviceId) {
    String fullCmd =
        MqParamConfig.SYSTEM_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.SYSTEM_RESTART
            + MqParamConfig.ORDER_RESULT_FILL
            + "0000";
    log.info("系统重置命令{}:", fullCmd);
    String key = deviceId + MqParamConfig.SYSTEM_ORDER + "**" + MqParamConfig.SYSTEM_RESTART;
    int wait = 2500;
    return getResultVoById(wait, key, fullCmd, deviceId);
  }

  private String getResultId(String fullCmd, String deviceId) {
    deviceId = ByteUtil.reverseHex(deviceId);
    fullCmd = deviceId + fullCmd;
    fullCmd = fullCmd.replace(" ", "");
    String crc16 = ByteUtil.crc(fullCmd);
    String _cmd = fullCmd + crc16;
    byte[] bytes = ByteUtil.hexStringToByteArray(_cmd);
    String escapedStr = ByteUtil.escapeBefore(bytes);
    fullCmd = MqParamConfig.PREFIX_SUFFIX + escapedStr + MqParamConfig.PREFIX_SUFFIX;
    log.info("设备主机:{},CRC:{},转义前:{},转义后:{},完整指令:{}", deviceId, crc16, _cmd, escapedStr, fullCmd);
    return fullCmd;
  }
}
