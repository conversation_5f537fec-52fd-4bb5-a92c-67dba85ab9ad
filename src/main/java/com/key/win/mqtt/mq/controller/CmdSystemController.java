package com.key.win.mqtt.mq.controller;

import com.key.win.mqtt.mq.cmd.EmqRequestService;
import com.key.win.mqtt.mq.cmd.TestPageCmdService;
import com.key.win.mqtt.mq.vo.ResultVo;
import java.util.ArrayList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/cmd/system/*")
public class CmdSystemController {

  @Autowired private TestPageCmdService testPageCmdService;

  @Autowired private EmqRequestService emqRequestService;

  @PostMapping("/reboot/{deviceId}")
  public ResultVo systemRestartByDeviceId(@PathVariable("deviceId") String deviceId) {
    boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + deviceId);
    if (!clientIsOnline) {
      return new ResultVo("当前设备处于离线状态,请检查网路链接!", new ArrayList<>());
    }
    return testPageCmdService.systemReBootByDeviceId(deviceId);
  }
}
