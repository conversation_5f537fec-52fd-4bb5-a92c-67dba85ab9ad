package com.key.win.mqtt.mq.statilFile;

import com.key.win.mqtt.mq.mqconst.MqConst;
import com.key.win.mqtt.mq.mqconst.OpticalConst;

import java.util.ArrayList;
import java.util.List;

/**
 * 光交换机光功率 【光口状态】【光收功率】【光发功率】的协议指令值
 */
public class OpticalStaticList {
    public static List<String> statusList;//光交换机光口状态列表

    public static List<String> receivePowerList_1; //光交换机光收功率端口
    public static List<String> receivePowerList_2; //光交换机光收功率端口

    public static List<String> outputPowerList_1 ; //光交换机光发功率端口
    public static List<String> outputPowerList_2 ; //光交换机光发功率端口

    static {
        statusList = new ArrayList<String>();
        receivePowerList_1 = new ArrayList<String>();
        receivePowerList_2 = new ArrayList<String>();
        outputPowerList_1 = new ArrayList<String>();
        outputPowerList_2 = new ArrayList<String>();

        statusList.add(OpticalConst.CODE_OPTICAL_PORT_1);//光交换机光口状态1
        statusList.add(OpticalConst.CODE_OPTICAL_PORT_2);//光交换机光口状态2
        statusList.add(OpticalConst.CODE_OPTICAL_PORT_3);//光交换机光口状态3
        statusList.add(OpticalConst.CODE_OPTICAL_PORT_4);//光交换机光口状态4
        statusList.add(OpticalConst.CODE_OPTICAL_PORT_5);//光交换机光口状态5
        statusList.add(OpticalConst.CODE_OPTICAL_PORT_6);//光交换机光口状态6
        statusList.add(OpticalConst.CODE_OPTICAL_PORT_7);//光交换机光口状态7
        statusList.add(OpticalConst.CODE_OPTICAL_PORT_8);//光交换机光口状态8

        receivePowerList_1.add(OpticalConst.CODE_OPTICAL_RECEIVE_POWER_1);//光交换机光收功率端口1
        receivePowerList_1.add(OpticalConst.CODE_OPTICAL_RECEIVE_POWER_2);//光交换机光收功率端口2
        receivePowerList_1.add(OpticalConst.CODE_OPTICAL_RECEIVE_POWER_3);//光交换机光收功率端口3
        receivePowerList_1.add(OpticalConst.CODE_OPTICAL_RECEIVE_POWER_4);//光交换机光收功率端口4

        receivePowerList_2.add(OpticalConst.CODE_OPTICAL_RECEIVE_POWER_5);//光交换机光收功率端口5
        receivePowerList_2.add(OpticalConst.CODE_OPTICAL_RECEIVE_POWER_6);//光交换机光收功率端口6
        receivePowerList_2.add(OpticalConst.CODE_OPTICAL_RECEIVE_POWER_7);//光交换机光收功率端口7
        receivePowerList_2.add(OpticalConst.CODE_OPTICAL_RECEIVE_POWER_8);//光交换机光收功率端口8

        outputPowerList_1.add(OpticalConst.CODE_OPTICAL_OUTPUT_POWER_1);//光交换机光发功率端口1
        outputPowerList_1.add(OpticalConst.CODE_OPTICAL_OUTPUT_POWER_2);//光交换机光发功率端口2
        outputPowerList_1.add(OpticalConst.CODE_OPTICAL_OUTPUT_POWER_3);//光交换机光发功率端口3
        outputPowerList_1.add(OpticalConst.CODE_OPTICAL_OUTPUT_POWER_4);//光交换机光发功率端口4

        outputPowerList_2.add(OpticalConst.CODE_OPTICAL_OUTPUT_POWER_5);//光交换机光发功率端口5
        outputPowerList_2.add(OpticalConst.CODE_OPTICAL_OUTPUT_POWER_6);//光交换机光发功率端口6
        outputPowerList_2.add(OpticalConst.CODE_OPTICAL_OUTPUT_POWER_7);//光交换机光发功率端口7
        outputPowerList_2.add(OpticalConst.CODE_OPTICAL_OUTPUT_POWER_8);//光交换机光发功率端口8
    }
}
