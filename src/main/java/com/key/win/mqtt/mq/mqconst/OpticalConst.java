package com.key.win.mqtt.mq.mqconst;

/**
 * 光交换机光口相关常量类
 */
public final class OpticalConst {

    /** 光交换机光口状态1 */
    public static final String CODE_OPTICAL_PORT_1 = "04C701";
    /** 光交换机光口状态2 */
    public static final String CODE_OPTICAL_PORT_2 = "04C801";
    /** 光交换机光口状态3 */
    public static final String CODE_OPTICAL_PORT_3 = "04C901";
    /** 光交换机光口状态4 */
    public static final String CODE_OPTICAL_PORT_4 = "04CA01";
    /** 光交换机光口状态5 */
    public static final String CODE_OPTICAL_PORT_5 = "04CB01";
    /** 光交换机光口状态6 */
    public static final String CODE_OPTICAL_PORT_6 = "04CC01";
    /** 光交换机光口状态7 */
    public static final String CODE_OPTICAL_PORT_7 = "04CD01";
    /** 光交换机光口状态8 */
    public static final String CODE_OPTICAL_PORT_8 = "04CE01";

    /** 光交换机光收功率端口1 */
    public static final String CODE_OPTICAL_RECEIVE_POWER_1 = "05CF01";
    /** 光交换机光收功率端口2 */
    public static final String CODE_OPTICAL_RECEIVE_POWER_2 = "05D001";
    /** 光交换机光收功率端口3 */
    public static final String CODE_OPTICAL_RECEIVE_POWER_3 = "05D101";
    /** 光交换机光收功率端口4 */
    public static final String CODE_OPTICAL_RECEIVE_POWER_4 = "05D201";
    /** 光交换机光收功率端口5 */
    public static final String CODE_OPTICAL_RECEIVE_POWER_5 = "05D301";
    /** 光交换机光收功率端口6 */
    public static final String CODE_OPTICAL_RECEIVE_POWER_6 = "05D401";
    /** 光交换机光收功率端口7 */
    public static final String CODE_OPTICAL_RECEIVE_POWER_7 = "05D501";
    /** 光交换机光收功率端口8 */
    public static final String CODE_OPTICAL_RECEIVE_POWER_8 = "05D601";


    /** 光交换机光发功率端口1 */
    public static final String CODE_OPTICAL_OUTPUT_POWER_1 = "05D701";
    /** 光交换机光发功率端口2 */
    public static final String CODE_OPTICAL_OUTPUT_POWER_2 = "05D801";
    /** 光交换机光发功率端口3 */
    public static final String CODE_OPTICAL_OUTPUT_POWER_3 = "05D901";
    /** 光交换机光发功率端口4 */
    public static final String CODE_OPTICAL_OUTPUT_POWER_4 = "05DA01";
    /** 光交换机光发功率端口5 */
    public static final String CODE_OPTICAL_OUTPUT_POWER_5 = "05DB01";
    /** 光交换机光发功率端口6 */
    public static final String CODE_OPTICAL_OUTPUT_POWER_6 = "05DC01";
    /** 光交换机光发功率端口7 */
    public static final String CODE_OPTICAL_OUTPUT_POWER_7 = "05DD01";
    /** 光交换机光发功率端口8 */
    public static final String CODE_OPTICAL_OUTPUT_POWER_8 = "05DE01";


}
