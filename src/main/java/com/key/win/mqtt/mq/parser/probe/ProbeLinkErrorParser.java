package com.key.win.mqtt.mq.parser.probe;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.TEL.bootstrap.TELService;
import com.key.win.biz.alarm.model.RealTimeWarn;
import com.key.win.biz.alarm.service.RealTimeWarnSevice;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.config.mqtt.MqttPushClient;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
@Slf4j
public class ProbeLinkErrorParser {

  private static BelongUnitService belongUnitService = SpringUtils.getBean(BelongUnitService.class);
  private static RealTimeWarnSevice realTimeWarnSevice =
      SpringUtils.getBean(RealTimeWarnSevice.class);
  private static String alarmType = GConfig.alarmType_probeLinkErr;

  public static void resolve(Map payload) {
    String deviceId = MapUtil.getStr(payload, "deviceId");
    RealTimeWarn probeAlarmBean = getProbeAlarmBean(deviceId);
    if (probeAlarmBean != null) {
      probeAlarmBean.setAlarmTimes(probeAlarmBean.getAlarmTimes() + 1);
      probeAlarmBean.setAttr1(GConfig.ALARM_REPORT_FLAG_0);
      realTimeWarnSevice.updateById(probeAlarmBean);
    } else {
      BelongUnit unit = getUnitByDeviceId(deviceId);
      probeAlarmBean = buildAlarmBean(deviceId, unit);
      realTimeWarnSevice.save(probeAlarmBean);
      String reportMsg =
          TELService.packageMsgProactiveReporting(
              belongUnitService, probeAlarmBean, 1, TELService.reportMsgFF);
      MqttPushClient.getInstance()
          .publish2CsharpClient(GConfig.MQ_TOPIC_CSHARP_CLIENT_ALERT_INFO, reportMsg);
      log.info("设备：{}，链路发生故障，告警上报[{}]",deviceId,reportMsg);
    }
  }

  private static RealTimeWarn getProbeAlarmBean(String deviceId) {
    LambdaQueryWrapper<RealTimeWarn> lqwRwrning = new LambdaQueryWrapper<RealTimeWarn>();
    lqwRwrning.eq(RealTimeWarn::getAlarmType, alarmType);
    lqwRwrning.eq(RealTimeWarn::getHostNumber, deviceId);
    lqwRwrning.eq(RealTimeWarn::getAlarmStatus, GConfig.alarmStatus_realTime); // 实时告警
    RealTimeWarn probeAlarmBean = realTimeWarnSevice.getOne(lqwRwrning);
    return probeAlarmBean;
  }

  private static BelongUnit getUnitByDeviceId(String deviceId) {
    LambdaQueryWrapper<BelongUnit> lqwUnit = new LambdaQueryWrapper<BelongUnit>();
    lqwUnit.eq(BelongUnit::getHostNum, deviceId);
    BelongUnit one = belongUnitService.getOne(lqwUnit);
    return one;
  }

  private static RealTimeWarn buildAlarmBean(String deviceId, BelongUnit unit) {
    Date currentTime = new Date();
    RealTimeWarn alarmBean =
        RealTimeWarn.builder()
            .hostNumber(deviceId)
            .alarmTimes(1)
            .attr1(GConfig.ALARM_REPORT_FLAG_0)
            .alarmStatus(GConfig.alarmStatus_realTime)
            .alarmName("[" + deviceId + "]可能存在链路异常")
            .belongStationId(unit.getStationId())
            .belongStationName(unit.getStationName())
            .alarmTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(currentTime))
            .remark("链路异常: [" + deviceId + "]可能存在链路异常,请检查!")
            .equipmentType(GConfig.equipmentType_Other)
            .alarmType(alarmType)
            .build();
    return alarmBean;
  }
}
