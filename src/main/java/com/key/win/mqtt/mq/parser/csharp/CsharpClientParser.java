package com.key.win.mqtt.mq.parser.csharp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.TEL.bootstrap.TELService;
import com.key.win.biz.alarm.model.RealTimeWarn;
import com.key.win.biz.alarm.service.RealTimeWarnSevice;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.biz.topo.enums.DeviceType;
import com.key.win.biz.topo.model.TopoNodes;
import com.key.win.biz.topo.service.TopoNodesService;
import com.key.win.config.mqtt.MqttPushClient;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.utils.SpringUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/** 信源强度解析 */
@Slf4j
@SuppressWarnings("rawtypes")
public class CsharpClientParser {

    private static RealTimeWarnSevice realTimeWarnSevice =
            SpringUtils.getBean(RealTimeWarnSevice.class);
    private static BelongUnitService belongUnitService = SpringUtils.getBean(BelongUnitService.class);
    private static TopoNodesService topoNodesService = SpringUtils.getBean(TopoNodesService.class);

    public static void loadAntMap() {
        LambdaQueryWrapper<TopoNodes> lqwNodes = new LambdaQueryWrapper<>();
        lqwNodes.orderByAsc(TopoNodes::getHostNum);
        lqwNodes.and(
                lq ->
                        lq.eq(TopoNodes::getType, DeviceType.TX_BG.getDeviceTypeCode())
                                .or()
                                .eq(TopoNodes::getType, DeviceType.TX_BZ.getDeviceTypeCode())
                                .or()
                                .eq(TopoNodes::getType, DeviceType.TX_DSZQ.getDeviceTypeCode())
                                .or()
                                .eq(TopoNodes::getType, DeviceType.TX_DXXD.getDeviceTypeCode())
                                .or()
                                .eq(TopoNodes::getType, DeviceType.TX_QT.getDeviceTypeCode())
                                .or()
                                .eq(TopoNodes::getType, DeviceType.TX_QXXD.getDeviceTypeCode()));
        List<TopoNodes> antNodeList = topoNodesService.list(lqwNodes);
        if(antNodeList.size()>0){
            TELService.getAntNodeMap().clear();
        }
        for (TopoNodes node : antNodeList) {
            String hostNum = node.getHostNum();
            String nodeName = node.getName();
            String probeSid = node.getProbeSid();
            // 如果hostNum在antNodeMap中不存在，则创建一个新的HashMap并将其与hostNum关联

            // 然后将nodeName作为键，probeSid作为值存入该HashMap中
            TELService.getAntNodeMap()
                    .computeIfAbsent(hostNum, k -> new HashMap<>())
                    .put(nodeName, probeSid);
        }
        log.info("TELService antennaMap 加载完毕:{}", TELService.getAntNodeMap().size());
    }

    public static void resolve(String payload) {

        if (StringUtils.isEmpty(payload)) {
            return;
        }

        long startTime = System.currentTimeMillis();

        // 如果有来自集中告警的请求，那么以下代码开始进行集中告警的推送相关
        // 1.获取当前告警列表
        LambdaQueryWrapper<RealTimeWarn> lqw = new LambdaQueryWrapper<>();
        lqw.eq(RealTimeWarn::getAttr1, "0");
        lqw.eq(RealTimeWarn::getAlarmStatus, "0");
        List<RealTimeWarn> list = realTimeWarnSevice.list(lqw);
        Integer size = 0;
        if (list.size() > 0) {
            loadAntMap();
        }
        // 2.将每一条告警信息封装成报文
        List<String> reportMsgList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            RealTimeWarn warn = list.get(i);
            size = size + 1;
            String msg = TELService.packageMsgAsync(belongUnitService, warn, size);
            if (StringUtils.isBlank(msg)) {
                size = size - 1;
                continue;
            }
            reportMsgList.add(msg);
        }

        String hexValue = String.format("%04X", size); // 将十进制转换为16进制，并填充到4个字符 (2个字节)
        log.info("TEL:告警同步信息共计:{}条,告警报文共计:{}条", size, reportMsgList.size());
        String reportMsg = "FAFAFA" + TELService.subSystemCode + hexValue;
        MqttPushClient.getInstance().publish2CsharpClient(GConfig.MQ_TOPIC_CSHARP_CLIENT_START, "1");

        MqttPushClient client = MqttPushClient.getInstance();
        client.publish2CsharpClient(GConfig.MQ_TOPIC_CSHARP_CLIENT_ALERT_INFO, reportMsg);
        log.info("开始...>{}",reportMsg);
        // 3.发送推送开始标识
        reportMsgList.parallelStream()
                .forEach(
                        msg -> {
                            client.publish2CsharpClient(GConfig.MQ_TOPIC_CSHARP_CLIENT_ALERT_INFO, msg);
                        });

        // 4.发送推送结束标识
        client.publish2CsharpClient(GConfig.MQ_TOPIC_CSHARP_CLIENT_END, "1");
        client.publishString(
                GConfig.MQ_TOPIC_CSHARP_CLIENT_ALERT_INFO, "F0F0F0" + TELService.subSystemCode);
        log.info("结束...>{}","F0F0F0" + TELService.subSystemCode);
        long l2 = System.currentTimeMillis();
        log.info("同步耗时:{} ms", System.currentTimeMillis() - startTime);
        ;
    }
}
