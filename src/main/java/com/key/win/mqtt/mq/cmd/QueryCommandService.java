package com.key.win.mqtt.mq.cmd;

import com.key.win.config.mqtt.MqttPushClient;
import com.key.win.mqtt.mq.statilFile.MqParamConfig;
import com.key.win.mqtt.mq.statilFile.OpticalStaticList;
import com.key.win.utils.ByteUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 查询指令业务类
 * @date 2021/10/13 16:09
 */
@Service
@Slf4j
public class QueryCommandService {

  private static final long waitTimeMillisecond = 1000;

  /** 精确读取,等待时间为 （结束功率-起始功率）* 因子耗时 */
  public void accurateReadingByDeviceId(String deviceId) {
    String fullCmds =
        MqParamConfig.RFID_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.ACCURATE_READING
            + MqParamConfig.ORDER_RESULT_FILL
            + MqParamConfig.DATA_LENGTH_FOUR
            + "01040000";
    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, waitTimeMillisecond);
  }

  /** 故障诊断 7e ffffffff 02 00 10 FF 0400 01040000 09c7 7e 等待时间为:10s */
  public void faultDiagnosisByDeviceId(String deviceId) {
    String fullCmds =
        MqParamConfig.RFID_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.FAULT_DIAGNOSIS
            + MqParamConfig.ORDER_RESULT_FILL
            + MqParamConfig.DATA_LENGTH_FOUR
            + "01040000";
    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, waitTimeMillisecond);
  }

  /**
   * 查看IP 7E 11118888 04 00 09 FF 0700 07 A800 00000000 641C 7E 0xffffffff 0x01 0x00 0x02 0xFF
   * 0x0000 无 Crc16 0x7e
   *
   * @param deviceId
   * @return
   */
  public void lookIp(String deviceId) {
    String fullCmds =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "1700"
            + "17A8000000000000000000000000000000000000000000";
    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, waitTimeMillisecond);
  }

  /**
   * 查询设备探针 7e ffffffff 04 00 01 ff 0000 716f 7e
   *
   * @param deviceId
   * @return
   */
  public void lookDeviceProbe(String deviceId) {
    String fullCmds =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.QUERY_DEVICE_PROBE
            + MqParamConfig.ORDER_RESULT_FILL
            + "0000";
    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, waitTimeMillisecond);
  }

  /**
   * 开始结束功率 7E 11118888 04 00 09 FF 0800 04 BA01 00 04 BB01 00 8FCC 7E
   *
   * @param deviceId
   * @return
   */
  public void startEndPower(String deviceId) {
    String fullCmds =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0800"
            + "04BA010004BB0100";
    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, waitTimeMillisecond);
  }

  /**
   * 发射功率 7E 11118888 04 00 09 FF 0400 04 B301 00 EF7D 7E
   *
   * @param deviceId
   * @return
   */
  public void launchPower(String deviceId) {
    String fullCmds =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04B30100";

    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, waitTimeMillisecond);
  }

  /**
   * 开始功率
   *
   * @param deviceId
   * @return
   */
  public void startPower(String deviceId) {
    String fullCmds =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04BA0100";

    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, waitTimeMillisecond);
  }

  /**
   * 结束功率
   *
   * @param deviceId
   * @return
   */
  public void endPower(String deviceId) {
    String fullCmds =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04BB0100";
    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, waitTimeMillisecond);
  }

  /**
   * 查询功率步进
   *
   * @param deviceId
   * @return
   */
  public void powerStep(String deviceId) {
    String fullCmds =
            MqParamConfig.PARAM_CONFIG_ORDER
                    + MqParamConfig.DOWN
                    + MqParamConfig.PARAMETER_QUERY
                    + MqParamConfig.ORDER_RESULT_FILL
                    + "0400"
                    + "04c601"
                    + "00";
    log.info("查询功率步进指令:{}", fullCmds);
    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, waitTimeMillisecond);
  }

  /**
   * 设备时间 7E 11118888 04 00 09 FF 0A00 0A A300 00000000000000 6568 7E
   *
   * @param deviceId
   * @return
   */
  public void equipmenTime(String deviceId) {
    String fullCmds =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0a00"
            + "0aa30000000000000000";

    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, waitTimeMillisecond);
  }

  /**
   * 软件版本
   *
   * @param deviceId 7E 11118888 04 00 09 FF 1700 17 A000 0000000000000000000000000000000000000000
   *     E3EB 7E
   * @return
   */
  public void softVersion(String deviceId) {

    String fullCmds =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "1700"
            + "17a0000000000000000000000000000000000000000000";

    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, waitTimeMillisecond);
  }

  /**
   * 设备型号
   *
   * @param deviceId
   * @return
   */
  public void deviceVersion(String deviceId) {
    String fullCmds =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "1700"
            + "17A100"
            + "0000000000000000000000000000000000000000";

    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, waitTimeMillisecond);
  }

  /**
   * 生产系列号
   *
   * @param deviceId
   * @return
   */
  public void productSerVersion(String deviceId) {
    String fullCmds =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "1700"
            + "17A200"
            + "0000000000000000000000000000000000000000";

    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, waitTimeMillisecond);
  }

  /**
   * 服务器端口号 7E 11118888 04 00 09 FF 0500 05 A900 0000 3616 7E
   *
   * @param deviceId
   * @return
   */
  public void serverAno(String deviceId) {
    String fullCmds =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0500"
            + "05A9000000";

    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, waitTimeMillisecond);
  }

  /**
   * 故障诊断周期 7E 11118888 04 00 09 FF 0500 05 B601 0000 4FEE 7E
   *
   * @param deviceId
   * @return
   */
  public void faultDiagnosisCycle(String deviceId) {
    String fullCmds =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0500"
            + "05B6010000";
    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, waitTimeMillisecond);
  }

  /**
   * 故障诊断周期时间 7E 11118888 04 00 09 FF 0500 05 B501 0000 [CRCX] 7E
   *
   * @param deviceId
   * @return
   */
  public void faultDiagnosisTime(String deviceId) {
    String fullCmds =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0500"
            + "05B6010000";

    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, waitTimeMillisecond);
  }

  /**
   * 故障诊断次数 7E 11118888 04 00 09 FF 0400 04 B701 00 2FA1 7E
   *
   * @param deviceId
   * @return
   */
  public void faultDiagnosisTimes(String deviceId) {
    String fullCmds =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04DF0100";

    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, waitTimeMillisecond);
  }

  /**
   * 查询告警判断次数
   * @param deviceId
   */
  public void alarmJudgeCount(String deviceId) {
    String fullCmds =
            MqParamConfig.PARAM_CONFIG_ORDER
                    + MqParamConfig.DOWN
                    + MqParamConfig.PARAMETER_QUERY
                    + MqParamConfig.ORDER_RESULT_FILL
                    + "0400"
                    + "04E00100";

    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, waitTimeMillisecond);
  }

  /**
   * 频率步进
   *
   * @param deviceId
   * @return
   */
  public void lookFrequencyStep(String deviceId) {

    String frequencyStep = "0000";
    String fullCmds =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0500"
            + "05"
            + "b201"
            + frequencyStep;

    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, waitTimeMillisecond);
  }

  /**
   * 开始频率
   *
   * @param deviceId
   * @return
   */
  public void frequencyStart(String deviceId) {
    String dataFill = "00000000";
    String fullCmds =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0700"
            + "07"
            + "b001"
            + dataFill;

    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, waitTimeMillisecond);
  }

  /**
   * 结束频率
   *
   * @param deviceId
   * @return
   */
  public void frequencyEnd(String deviceId) {

    String dataFill = "00000000";
    String fullCmds =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0700"
            + "07"
            + "b101"
            + dataFill;

    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, waitTimeMillisecond);
  }

  /**
   * 查看精确读取修正值
   *
   * @param deviceId
   * @return
   */
  public void lookAccuratelyReadTheCorrectionValue(String deviceId) {
    String sourcePowerCorrection = "00";
    String fullCmds =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_QUERY
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04"
            + "b901"
            + sourcePowerCorrection;

    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, waitTimeMillisecond);
  }

  // 关闭故诊开关设备的轮训任务
  public void setFaultSwitch(String deviceId) {
    String troubleshooting = "00";
    String fullCmds =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04"
            + "b401"
            + troubleshooting;
    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, 100);
  }

  // 关闭精确读取轮训任务的开关
  public void setAccurateReadingSwitch(String deviceId) {
    String accurateReading = "00";
    String fullCmds =
        MqParamConfig.PARAM_CONFIG_ORDER
            + MqParamConfig.DOWN
            + MqParamConfig.PARAMETER_SETTING
            + MqParamConfig.ORDER_RESULT_FILL
            + "0400"
            + "04"
            + "b801"
            + accurateReading;
    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, 100);
  }

  /**
   * 光交换机光端口状态
   * 对8个光端口指令组合成一个指令进行发送：
   * 组合规则是：
   * 1、固定
   *    7E01000100040009FF
   * 2、总长度(32-转换位16进制则值是20)
   *   2000
   * 3、本单元长度+OID+内容
   *  04+C901+00
   * 4、CRC
   * 如网管组合发送指令是：7e16011223040009ff 2000 04c70100 04c80100 04c90100 04ca0100 04cb0100 04cc0100 04cd0100 04ce0100 5e5d257e
   * 如组合后主机反馈指令是：7e1601122304010901 2000 04c70101 04c80101 04c90100 04ca0101 04cb0101 04cc0101 04cd0101 04ce0101 93cd 7e
   * @param deviceId
   * @return
   */
  public void queryOpticalPort(String deviceId) {
      String mulPortCmd = OpticalStaticList.statusList.stream()
              .map(element -> element + "00") // 给每个元素追加"00" //对每个端口指令补00查询
              .collect(Collectors.joining());
      log.info("查询光端口组合指令:{}",mulPortCmd);
      String fullCmds =
              MqParamConfig.PARAM_CONFIG_ORDER
                      + MqParamConfig.DOWN
                      + MqParamConfig.PARAMETER_QUERY
                      + MqParamConfig.ORDER_RESULT_FILL
                      + "2000" //长度是4*8=32 再对32值转16进制值=0020，再高地位转换=2000
                      + mulPortCmd;
      log.info("查询光交换机光端口状态Cmd:{}",  fullCmds);

      fullCmds = getResultId(fullCmds, deviceId);
      deviceId = "reader_" + deviceId;
      MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, 2000);
  }

  /**
   * 查询-光交换机光收功率（前4个端口组合） 8个端口会byte会超长，不能超过50Byte
   * @param deviceId
   * @return
   */
  public void queryOpticalReceivePower_1(String deviceId) {
    String mulReceivePowerCmd = OpticalStaticList.receivePowerList_1.stream()
            .map(element -> element + "0000") // 给每个元素追加"0000"
            .collect(Collectors.joining()); //
    String fullCmds =
            MqParamConfig.PARAM_CONFIG_ORDER
                    + MqParamConfig.DOWN
                    + MqParamConfig.PARAMETER_QUERY
                    + MqParamConfig.ORDER_RESULT_FILL
                    + "1400" // 4*5 = 20个长度 再对20转16进制得到14
                    + mulReceivePowerCmd;
    log.info("查询光交换机光【收功率端口1~4组合指令】:{}",  fullCmds);
    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, 2000);
  }

  /**
   * 查询-光交换机光收功率（后4个端口组合） 8个端口会byte会超长，不能超过50Byte
   * @param deviceId
   * @return
   */
  public void queryOpticalReceivePower_2(String deviceId) {
    String mulReceivePowerCmd = OpticalStaticList.receivePowerList_2.stream()
            .map(element -> element + "0000") // 给每个元素追加"0000"
            .collect(Collectors.joining()); //
    String fullCmds =
            MqParamConfig.PARAM_CONFIG_ORDER
                    + MqParamConfig.DOWN
                    + MqParamConfig.PARAMETER_QUERY
                    + MqParamConfig.ORDER_RESULT_FILL
                    + "1400" // 4*5 = 20个长度 再对20转16进制得到14
                    + mulReceivePowerCmd;
    log.info("查询光交换机光【收功率端口5~8组合指令】:{}",  fullCmds);
    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, 2000);
  }


  /**
   * 查询-光交换机光发功率（前4个端口组合） 8个端口会byte会超长（不能超过50Byte）所以4个组合发指令
   * @param deviceId
   * @return
   */
  public void queryOpticalOutputPower_1(String deviceId) {
      String mulOutputPowerCmd_1 = OpticalStaticList.outputPowerList_1.stream()
              .map(element -> element + "0000") // 给每个元素追加"00" //补00查询
              .collect(Collectors.joining()); //
      String fullCmds =
              MqParamConfig.PARAM_CONFIG_ORDER
                      + MqParamConfig.DOWN
                      + MqParamConfig.PARAMETER_QUERY
                      + MqParamConfig.ORDER_RESULT_FILL
                      + "1400" // 4个*5长度 = 共20个长度 再对20转16进制得到14
                      + mulOutputPowerCmd_1;
      log.info("查询光交换机光【发功率端口1~4组合指令】:{}",  fullCmds);
      fullCmds = getResultId(fullCmds, deviceId);
      deviceId = "reader_" + deviceId;
      MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, 2000);
  }

  /**
   * 查询-光交换机光发功率（后4个端口组合） 8个端口会byte会超长（不能超过50Byte）所以4个组合发指令
   * @param deviceId
   * @return
   */
  public void queryOpticalOutputPower_2(String deviceId) {
    String mulOutputPowerCmd_2 = OpticalStaticList.outputPowerList_2.stream()
            .map(element -> element + "0000") // 给每个元素追加"00" //补00查询
            .collect(Collectors.joining()); //
    String fullCmds =
            MqParamConfig.PARAM_CONFIG_ORDER
                    + MqParamConfig.DOWN
                    + MqParamConfig.PARAMETER_QUERY
                    + MqParamConfig.ORDER_RESULT_FILL
                    + "1400" // 4个*5长度 = 共20个长度 再对20转16进制得到14
                    + mulOutputPowerCmd_2;
    log.info("查询光交换机光【发功率端口5~8组合指令】:{}",  fullCmds);
    fullCmds = getResultId(fullCmds, deviceId);
    deviceId = "reader_" + deviceId;
    MqttPushClient.getInstance().publishWaitTime(0, false, deviceId, fullCmds, 2000);
  }

  private String getResultId(String fullCmd, String deviceId) {
    deviceId = ByteUtil.reverseHex(deviceId);
    fullCmd = deviceId + fullCmd;
    fullCmd = fullCmd.replace(" ", "");
    String crc16 = ByteUtil.crc(fullCmd);
    String _cmd = fullCmd + crc16;
    byte[] bytes = ByteUtil.hexStringToByteArray(_cmd);
    String escapedStr = ByteUtil.escapeBefore(bytes);
    fullCmd = MqParamConfig.PREFIX_SUFFIX + escapedStr + MqParamConfig.PREFIX_SUFFIX;
    log.info("设备主机:{},CRC:{},转义前:{},转义后:{},完整指令:{}", deviceId, crc16, _cmd, escapedStr, fullCmd);
    return fullCmd;
  }
}
