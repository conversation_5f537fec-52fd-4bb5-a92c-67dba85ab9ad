package com.key.win.mqtt.mq.parser.probe;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.alarm.service.HisAntLossSevice;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@Slf4j
public class ProbeHisAntLossParser {

    private static HisAntLossSevice hisAntLossSevice = SpringUtils.getBean(HisAntLossSevice.class);

    private static BelongUnitService belongUnitService = SpringUtils.getBean(BelongUnitService.class);

    public static void resolve(Map payload) {
        String deviceId = MapUtil.getStr(payload, "deviceId");
        String sid = MapUtil.getStr(payload, "sid");
        String probeId = MapUtil.getStr(payload, "probeId");
        String antName = MapUtil.getStr(payload, "antName");
        String loss = MapUtil.getStr(payload, "loss");
        //log.info("deviceId:{},sid:{},antName:{},lass:{},probeId:{}",deviceId,sid,antName,loss,probeId);

        LambdaQueryWrapper<BelongUnit> lqw = new LambdaQueryWrapper<>();
        lqw.eq(BelongUnit::getEnableFlag, true);
        lqw.eq(BelongUnit::getHostNum, deviceId);
        List<BelongUnit> list = belongUnitService.list(lqw);
        if(ObjectUtil.isNotEmpty(list) && list.size() >0){
            BelongUnit belongUnit = list.get(0);
            String lineId = belongUnit.getLineId();
            String lineName = belongUnit.getLineName();
            String stationId =belongUnit.getStationId();
            String stationName = belongUnit.getStationName();

            hisAntLossSevice.saveHisAntLoss(deviceId,lineId,lineName,stationId,stationName, antName,probeId,sid,loss);
        }else{
            log.error("查询不到设备【{}}】",deviceId);
        }

    }
}
