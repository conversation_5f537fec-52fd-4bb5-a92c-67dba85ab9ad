package com.key.win.mqtt.mq.parser;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import com.alibaba.fastjson.JSON;
import com.key.win.config.RedisUtil;
import com.key.win.config.mqtt.MqttPushClient;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.mqtt.mq.mqconst.MqConst;
import com.key.win.mqtt.mq.mqconst.MqFuncitonCodeConst;
import com.key.win.mqtt.mq.mqconst.OpticalConst;
import com.key.win.mqtt.mq.statilFile.MqParamConfig;
import com.key.win.utils.ByteUtil;
import com.key.win.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public final class ChenChaoCmdMessageParser {

  private static RedisUtil redisUtil = SpringUtils.getBean(RedisUtil.class);
  private static Environment environment = SpringUtils.getBean(Environment.class);

  public static void ResolveMqMessage(String msg) {
    boolean matchFlag = false;
    String revert_hostNum = "";

    String  deviceBackOriginMsg = ByteUtil.escapeAfter(ByteUtil.hexStringToByteArray(msg.toLowerCase()));
    log.info("设备主机上报的指令:{}",deviceBackOriginMsg);
    String probeRealRead = environment.getProperty("sf.cmd.probeRealRead.enable");
    boolean enable = Boolean.parseBoolean(probeRealRead);

    /** 参数查询解析匹配 */
    Map<String, Map<String, Integer>> rowMap = MqFuncitonCodeConst.getParamObject().rowMap();
    Set<String> args = rowMap.keySet();
    for (String arg : args) {
      String reg =
          MqConst.EXP_PREFIX
              + MqConst.code_arg_query_DEVICE2OMC
              + MqConst.EXP_DATA_LENGTH
              + arg.toLowerCase()
              + MqConst.EXP_ARG_QUERY_SUFIX;
      if (ReUtil.isMatch(reg, msg.toLowerCase())) {
        Map<String, Integer> map = rowMap.get(arg);
        String desc = map.keySet().toArray(new String[1])[0];
        Integer value = map.get(desc);
        log.debug("获取到设备的响应指令: {} , 数据长度共{}个字节 ,字符串长度:{}", desc, value, value * 2);
        msg = ByteUtil.escapeAfter(ByteUtil.hexStringToByteArray(msg.toLowerCase()));

        // 报文中的主机编码
        String o_hostNum = msg.substring(2, 10);
        // 数据库中的主机编码
        revert_hostNum = ByteUtil.reverseHex(o_hostNum);

        // 报文响应类型
        String msgResponseType = msg.substring(16, 18);
        log.info("来自设备的报文响应类型|FF网管到设备请求填充,01正常,02系统繁忙,03设备故障|当前报文类型:{}", msgResponseType);
        if (msgResponseType.equals(MqParamConfig.MSG_BUSYING)) {
          log.info("系统指令正忙,请稍后重试指令！！");
          return;
        } else if (msgResponseType.equals(MqParamConfig.MSG_ERR)) {
          log.info("设备故障,请稍后重试！！");
          return;
        }

        // 报文信息
        String data = msg.substring(18 + 4, msg.length() - 6).replace(arg.toLowerCase(), "");
        // 监控主机编号
        String hostKey = GConfig.hostKey + revert_hostNum;

        Map<String, Object> hostMap = new LinkedHashMap<>();

        if (data.length() == value * 2) {
          if (StringUtils.containsIgnoreCase("17A800", arg)) { // 监控主机IP
            List<String> strs = new ArrayList<String>();
            StringBuffer ipSbuffer = new StringBuffer();
            while (data.length() > 0) {
              String substring = data.substring(0, 2);
              strs.add(substring);
              data = data.substring(2);
            }
            for (String asc : strs) {
              char c = (char) Integer.parseInt(asc, 16);
              if ("\u0000".equals(String.valueOf(c))) {
                continue;
              }
              ipSbuffer.append(c);
            }
            log.info("IP:{}", ipSbuffer.toString());

            boolean hasKey = redisUtil.hasKey(hostKey);
            if (hasKey) {
              hostMap = redisUtil.hmget2(hostKey);
              hostMap.put("ipAddress", ipSbuffer.toString());
            } else {
              hostMap = new LinkedHashMap<>();
              hostMap.put("ipAddress", ipSbuffer.toString());
            }
            redisUtil.hmset(hostKey, hostMap);

          } else if (StringUtils.containsIgnoreCase("17A000,17A100,17A200", arg)) {
            if (StringUtils.equalsIgnoreCase(arg, "17A000")) {
              log.info("软件版本 ：{}", data);
              String  softwareVersionSbuffer = getParserDataString(data);
              log.info("软件版本解析后值 ：{}", softwareVersionSbuffer);
              boolean hasKey = redisUtil.hasKey(hostKey);
              if (hasKey) {
                hostMap = redisUtil.hmget2(hostKey);
                hostMap.put("softwareVersion", softwareVersionSbuffer);
              } else {
                hostMap = new LinkedHashMap<>();
                hostMap.put("softwareVersion", softwareVersionSbuffer);
              }
              redisUtil.hmset(hostKey, hostMap);
            } else if (StringUtils.equalsIgnoreCase(arg, "17A100")) {
              log.info("设备型号原始值 ：{}", data);
              String  deviceSbuffer = getParserDataString(data);
              log.info("设备型号解析后值 ：{}", deviceSbuffer);
              boolean hasKey = redisUtil.hasKey(hostKey);
              if (hasKey) {
                hostMap = redisUtil.hmget2(hostKey);
                hostMap.put("deviceVersion", deviceSbuffer);
              } else {
                hostMap = new LinkedHashMap<>();
                hostMap.put("deviceVersion", deviceSbuffer);
              }
              redisUtil.hmset(hostKey, hostMap);
            } else if (StringUtils.equalsIgnoreCase(arg, "17A200")) {
              log.info("设备生产系列号 ：{}", data);
              String  deviceSerNoSbuffer = getParserDataString(data);
              log.info("设备生产系列号解析后值 ：{}", deviceSerNoSbuffer);
              boolean hasKey = redisUtil.hasKey(hostKey);
              if (hasKey) {
                hostMap = redisUtil.hmget2(hostKey);
                hostMap.put("productSerVersion", deviceSerNoSbuffer);
              } else {
                hostMap = new LinkedHashMap<>();
                hostMap.put("productSerVersion", deviceSerNoSbuffer);
              }
              redisUtil.hmset(hostKey, hostMap);
            }
          } else if (StringUtils.containsIgnoreCase("0AA300", arg)) { // 设备时间
            log.info("MQ当前时间 ：{}", data);
            boolean hasKey = redisUtil.hasKey(hostKey);
            if (hasKey) {
              hostMap = redisUtil.hmget2(hostKey);
              hostMap.put("hostCurrentTime", data);
            } else {
              hostMap = new LinkedHashMap<>();
              hostMap.put("hostCurrentTime", data);
            }
            redisUtil.hmset(hostKey, hostMap);
          } else if (StringUtils.containsIgnoreCase("05A900", arg)) { // 端口
            String portHex = ByteUtil.reverseHex(data);
            int port = Integer.parseInt(portHex, 16);
            log.info("端口号 : {} ", port);
            boolean hasKey = redisUtil.hasKey(hostKey);
            if (hasKey) {
              hostMap = redisUtil.hmget2(hostKey);
              hostMap.put("port", port);
            } else {
              hostMap = new LinkedHashMap<>();
              hostMap.put("port", port);
            }
            redisUtil.hmset(hostKey, hostMap);
          } else if (StringUtils.containsIgnoreCase("07B001,07B101,05B201", arg)) {
            String mhzHex = ByteUtil.reverseHex(data);
            double hmz = Integer.parseInt(mhzHex, 16);
            hmz = hmz / 100;
            if (StringUtils.equalsIgnoreCase(arg, "07B001")) {
              log.info("开始频率 ：{}", hmz);
              boolean hasKey = redisUtil.hasKey(hostKey);
              if (hasKey) {
                hostMap = redisUtil.hmget2(hostKey);
                hostMap.put("startFrequency", hmz);
              } else {
                hostMap = new LinkedHashMap<>();
                hostMap.put("startFrequency", hmz);
              }
            } else if (StringUtils.equalsIgnoreCase(arg, "07B101")) {
              log.info("结束频率 ：{}", hmz);
              boolean hasKey = redisUtil.hasKey(hostKey);
              if (hasKey) {
                hostMap = redisUtil.hmget2(hostKey);
                hostMap.put("endFrequency", hmz);
              } else {
                hostMap = new LinkedHashMap<>();
                hostMap.put("endFrequency", hmz);
              }
            } else if (StringUtils.equalsIgnoreCase(arg, "05B201")) {
              log.info("频率步进 ：{}", hmz);
              boolean hasKey = redisUtil.hasKey(hostKey);
              if (hasKey) {
                hostMap = redisUtil.hmget2(hostKey);
                hostMap.put("frequencyStep", hmz);
              } else {
                hostMap = new LinkedHashMap<>();
                hostMap.put("frequencyStep", hmz);
              }
            }
          } else if (StringUtils.containsIgnoreCase("05B501", arg)) { // 故障诊断时间 BCD码 2个字节 时:分
            String result = ByteUtil.reverseHex(data);
            String hour = result.substring(0, 2);
            String min = result.substring(2, 4);

            log.info("故障诊断时间(BCD)码: {}:{} ", hour, min);
            boolean hasKey = redisUtil.hasKey(hostKey);
            if (hasKey) {
              hostMap = redisUtil.hmget2(hostKey);
            } else {
              hostMap = new LinkedHashMap<>();
            }
            hostMap.put("faultDiagnosisTime", hour + ":" + min);
          } else if (StringUtils.containsIgnoreCase("05B601", arg)) { // 故障诊断周期
            log.info("故障诊断周期时间-原始值：{}",data);
            String result = ByteUtil.reverseHex(data);
            log.info("故障诊断周期时间-解析值：{}",data);
            String min = result.substring(0, 2);
            String hour = result.substring(2, 4);
            log.info("故障诊断周期: {}时{}分 ", hour, min);
            boolean hasKey = redisUtil.hasKey(hostKey);
            if (hasKey) {
              hostMap = redisUtil.hmget2(hostKey);
            } else {
              hostMap = new LinkedHashMap<>();
            }
            hostMap.put("faultDiagnosisCycle", hour + ":" + min);
          } else if (StringUtils.containsIgnoreCase("04DF01", arg)) { // 故障诊断次数
            int times = Integer.parseInt(data, 16);
            log.info("故障诊断次数:{} ", times);
            boolean hasKey = redisUtil.hasKey(hostKey);
            if (hasKey) {
              hostMap = redisUtil.hmget2(hostKey);
              hostMap.put("faultDiagnosisNumber", times);
            } else {
              hostMap = new LinkedHashMap<>();
              hostMap.put("faultDiagnosisNumber", times);
            }
          } else if (StringUtils.containsIgnoreCase("04B401", arg)) { // 故障诊断开关
            int result = Integer.parseInt(data, 16);
            boolean flag = false;
            if (result == 1) {
              // 开
              flag = true;
            } else if (result == 0) {
              // 关
              flag = false;
            }
            log.info("故障诊断开关状态:{} ", flag ? "开启" : "关闭");
            boolean hasKey = redisUtil.hasKey(hostKey);
            if (hasKey) {
              hostMap = redisUtil.hmget2(hostKey);
              hostMap.put("troubleshooting", flag);
            } else {
              hostMap = new LinkedHashMap<>();
              hostMap.put("troubleshooting", flag);
            }
          } else if (StringUtils.containsIgnoreCase("04B801", arg)) { // 精确读取开关
            int result = Integer.parseInt(data, 16);
            boolean flag = false;
            if (result == 1) {
              // 开
              log.info("精确读取开关状态:{} ", Boolean.TRUE);
              flag = true;
            } else if (result == 0) {
              // 关
              log.info("精确读取开关状态:{} ", Boolean.FALSE);
              flag = false;
            }
            boolean hasKey = redisUtil.hasKey(hostKey);
            if (hasKey) {
              hostMap = redisUtil.hmget2(hostKey);
              hostMap.put("accurateReading", flag);
            } else {
              hostMap = new LinkedHashMap<>();
              hostMap.put("accurateReading", flag);
            }
          } else if (StringUtils.containsIgnoreCase("04B901", arg)) { // 精确读取修正值

            Byte result = (byte) Integer.parseInt(data, 16);
            log.info("精确读取修正值,有符号:{} ", result);
            boolean hasKey = redisUtil.hasKey(hostKey);
            if (hasKey) {
              hostMap = redisUtil.hmget2(hostKey);
              hostMap.put("sourcePowerCorrection", result);
            } else {
              hostMap = new LinkedHashMap<>();
              hostMap.put("sourcePowerCorrection", result);
            }
          } else if (StringUtils.containsIgnoreCase("04B301", arg)) { // 发射功率
            Byte result = (byte) Integer.parseInt(data, 16);
            log.info("发射功率,有符号:{} ", result);
            boolean hasKey = redisUtil.hasKey(hostKey);
            if (hasKey) {
              hostMap = redisUtil.hmget2(hostKey);
              hostMap.put("transmitPower", result);
            } else {
              hostMap = new LinkedHashMap<>();
              hostMap.put("transmitPower", result);
            }
          } else if (StringUtils.containsIgnoreCase("04BA01", arg)) { // 起始功率
            Byte result = (byte) Integer.parseInt(data, 16);
            log.info("起始功率,有符号:{} ", result);
            boolean hasKey = redisUtil.hasKey(hostKey);
            if (hasKey) {
              hostMap = redisUtil.hmget2(hostKey);
              hostMap.put("startPower", result);
            } else {
              hostMap = new LinkedHashMap<>();
              hostMap.put("startPower", result);
            }
          } else if (StringUtils.containsIgnoreCase("04BB01", arg)) { // 结束功率
            Byte result = (byte) Integer.parseInt(data, 16);
            log.info("结束功率,有符号:{} ", result);
            boolean hasKey = redisUtil.hasKey(hostKey);
            if (hasKey) {
              hostMap = redisUtil.hmget2(hostKey);
              hostMap.put("endPower", result);
            } else {
              hostMap = new LinkedHashMap<>();
              hostMap.put("endPower", result);
            }
          } else if (StringUtils.containsIgnoreCase("04C601", arg)) { // 功率步进
            Byte result = (byte) Integer.parseInt(data, 16);
            log.info("解析功率步进:{} ", result);
            boolean hasKey = redisUtil.hasKey(hostKey);
            if (hasKey) {
              hostMap = redisUtil.hmget2(hostKey);
              hostMap.put("powerStep", result);
            } else {
              hostMap = new LinkedHashMap<>();
              hostMap.put("powerStep", result);
            }
          }else if (StringUtils.containsIgnoreCase("04E001", arg)) { // 告警判断次数
            Byte result = (byte) Integer.parseInt(data, 16);
            log.info("告警判断次数:{} ", result);
            boolean hasKey = redisUtil.hasKey(hostKey);
            if (hasKey) {
              hostMap = redisUtil.hmget2(hostKey);
              hostMap.put("alarmJudgeCount", result);
            } else {
              hostMap = new LinkedHashMap<>();
              hostMap.put("alarmJudgeCount", result);
            }
          }
          redisUtil.hmset(hostKey, hostMap);
        }
        matchFlag = true;
        break;
      }
    }

    /** 系统指令以及监控主机指令匹配解析 */
    Map<String, Map<String, Integer>> sysMap = MqFuncitonCodeConst.getSysObject().rowMap();
    Set<String> args2 = sysMap.keySet();
    for (String arg : args2) {
      String reg = MqConst.EXP_PREFIX + arg.toLowerCase() + MqConst.EXP_ARG_QUERY_SUFIX;
      if (ReUtil.isMatch(reg, msg.toLowerCase())) {
        Map<String, Integer> map = sysMap.get(arg);
        String desc = map.keySet().toArray(new String[1])[0];
        Integer value = map.get(desc);
        msg = ByteUtil.escapeAfter(ByteUtil.hexStringToByteArray(msg.toLowerCase()));
        // 报文中的主机编码
        String o_hostNum = msg.substring(2, 10);
        // 数据库中的主机编码
        revert_hostNum = ByteUtil.reverseHex(o_hostNum);
        // 报文信息
        String data = msg.substring(18 + 4, msg.length() - 6).replace(arg.toLowerCase(), "");
        // 监控主机编号
        String hostKey = msg.substring(2, 10);
        hostKey = GConfig.probeKey + revert_hostNum + ":";

        Map<String, Object> hostMap = new LinkedHashMap<>();
        if (data.length() == value * 2) {
          if (StringUtils.containsIgnoreCase("020112010E00", arg)) { // 故障状态上报指令
            // 天线序号
            String probeSid = data.substring(0, 2);
            probeSid = String.valueOf((byte) Integer.parseInt(probeSid, 16));
            if (probeSid.length() < 2) {
              probeSid = "0" + probeSid;
            }
            // 天线编号
            String probeId = data.substring(2, data.length() - 2);
            // 天线状态
            String probeStatus = data.substring(data.length() - 2);

            boolean flag = redisUtil.hasKey(GConfig.PROBE_CHECK_KEY + revert_hostNum);
            Map<String, Object> probeStatusMap = null;
            if (flag) {
              probeStatusMap = redisUtil.hmget2(GConfig.PROBE_CHECK_KEY + revert_hostNum);
              if (probeStatusMap != null) {
                String v = MapUtil.getStr(probeStatusMap, probeSid, "");
                if (v.equals("01")) {
                  log.info(
                      "主机:{}, 探针:{},信息存在,探针值为:{},探针状态正常!", revert_hostNum, probeSid, probeStatus);
                  probeStatus = "01";
                }
              } else {
                probeStatusMap = new LinkedHashMap<>();
                probeStatusMap.put("init2", "");
              }
            } else {
              probeStatusMap = new LinkedHashMap<>();
              probeStatusMap.put("init2", "");
            }
            log.info("↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓【状态上报】指令↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓");
            log.info(
                "监控主机编号: {} ,探针序列号 : {} , 探针编号:{} , probeStatus :{}",
                revert_hostNum,
                probeSid,
                probeId,
                probeStatus.equals("00") ? "*正常!" : "正常");

            hostKey += probeSid;
            boolean hasKey = redisUtil.hasKey(hostKey);
            if (hasKey) {
              hostMap = redisUtil.hmget2(hostKey);
            } else {
              hostMap = new LinkedHashMap<>();
            }
            hostMap.put("hostNum", revert_hostNum);
            hostMap.put("number", probeSid);
            hostMap.put("coding", probeId);

            if (probeStatus.equalsIgnoreCase("00")) {
              hostMap.put("lost", 120);
            } else {
              probeStatusMap.put(probeSid, "01");
              hostMap.put("lost", GConfig.ANT_PATH_LOSS);
            }
            hostMap.put("probeStatus", probeStatus);

            redisUtil.hmset(hostKey, hostMap);

            if (!enable) {
              redisUtil.hmset(
                  GConfig.PROBE_CHECK_KEY + revert_hostNum,
                  probeStatusMap,
                  GConfig.PROBE_CHECK_KEY_EXPIRED_TIME);
            }

          } else if (StringUtils.containsIgnoreCase("020113010E00", arg)) { // 路损上报指令
            // 天线序号
            String probeSid = data.substring(0, 2);
            probeSid = String.valueOf((byte) Integer.parseInt(probeSid, 16));
            if (probeSid.length() < 2) {
              probeSid = "0" + probeSid;
            }
            // 天线编号
            String probeId = data.substring(2, data.length() - 2);
            // 天线路损16进制
            String probeLost = data.substring(data.length() - 2);
            byte lost = (byte) Integer.parseInt(probeLost, 16);
            log.info("↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓{}【路损上报】指令↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓", revert_hostNum);
            log.info(
                "监控主机编号: {} ,探针序列号 : {} , 探针编号:{} , 路损值 :{}",
                revert_hostNum,
                probeSid,
                probeId,
                lost);
            hostKey += probeSid;
            boolean hasKey = redisUtil.hasKey(hostKey);
            if (hasKey) {
              hostMap = redisUtil.hmget2(hostKey);
            } else {
              hostMap = new LinkedHashMap<>();
            }
            // 监控主机编号
            hostMap.put("hostNum", revert_hostNum);
            // 天线序号
            hostMap.put("number", probeSid);
            // 天线编号
            hostMap.put("coding", probeId);
            // 天线状态
            if (lost < 120) {
              hostMap.put("probeStatus", "01");
            } else {
              hostMap.put("probeStatus", "00");
            }
            // 路损值
            hostMap.put("lost", lost);

            redisUtil.hmset(hostKey, hostMap);
          } else if (StringUtils.containsIgnoreCase(
              "040101010D00,040102010D00,040106010D00", arg)) {

            // 天线序号，16进制转成10进制
            String probeSid = data.substring(0, 2);
            probeSid = String.valueOf((byte) Integer.parseInt(probeSid, 16));
            if (probeSid.length() < 2) {
              probeSid = "0" + probeSid;
            }

            // 天线编号
            String probeId = data.substring(2, data.length());
            log.info("↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓{}【探针查询】指令↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓", revert_hostNum);
            log.info("探针序列号 : {} , 探针编号:{} ", probeSid, probeId);
            hostKey += probeSid;
            if (StringUtils.containsIgnoreCase("040101010D00", arg)) { // 探针查询
              boolean hasKey = redisUtil.hasKey(hostKey);
              if (hasKey) {
                hostMap = redisUtil.hmget2(hostKey);
              } else {
                hostMap = new LinkedHashMap<>();
              }
              hostMap.put("hostNum", revert_hostNum);
              // 天线序号
              hostMap.put("number", probeSid);
              // 天线编号
              hostMap.put("coding", probeId);
              redisUtil.hmset(hostKey, hostMap);
            } else if (StringUtils.containsIgnoreCase("040102010D00", arg)) { // 探针设置
              // 探针设置
            } else if (StringUtils.containsIgnoreCase("040106010D00", arg)) { // 探针删除
              // 探针删除
            }
          } else if (StringUtils.containsIgnoreCase("040108010200", arg)) { // 告警信息读取
            String reverseHex = ByteUtil.reverseHex(data);
            String binaryString = Integer.toBinaryString(Integer.parseInt(reverseHex, 16));
            log.info("告警信息读取:(2进制)字符 : {} ", binaryString);
          } else if (StringUtils.containsIgnoreCase("010101FF0400", arg)) {
            // 心跳指令
            String cacheKey = GConfig.aliveKey;
            redisUtil.set(cacheKey + revert_hostNum, new Date());
            // 回心跳给设备
            String hbData = o_hostNum + "01000101040000000000";
            // 回心跳CRC
            String crc = ByteUtil.crc(hbData);
            byte[] crc_byte = ByteUtil.hexStringToByteArray(crc);
            crc = ByteUtil.escapeBefore(crc_byte);

            String hb = "7e" + hbData + crc + "7e".replace(" ", "");
            log.info("c向监控主机【{}】回应心跳: {}", revert_hostNum, hb);
            MqttPushClient.getInstance().publish(0, false, "reader_" + revert_hostNum, hb);
          } else if (StringUtils.containsIgnoreCase("010102010000", arg)) {
            // 系统重置
          } else if (StringUtils.containsIgnoreCase("020110010400", arg)) {
            // 故障诊断指令
          } else if (StringUtils.containsIgnoreCase("020111010400", arg)) {
            // 精确读取指令
          } else if (StringUtils.containsIgnoreCase("040103010100", arg)) {
            // 探针批量删除
          }
        }
        matchFlag = true;
        break;
      }
    }

    /** 解析主机反馈的【光交换机光状态/收发功率】  2024-05-29添加 */
    parserOpticalSwtichMsg(msg);

    if (matchFlag) {
      String cacheKey = GConfig.aliveKey;
      redisUtil.set(cacheKey + revert_hostNum, new Date());
    }
  }

  /**
   * 解析-光交换机光端口状态，光/收功率
   * @param msg
   */
  private static void parserOpticalSwtichMsg(String msg){
        String opticalMsg = ByteUtil.escapeAfter(ByteUtil.hexStringToByteArray(msg.toLowerCase()));
        Map<String, Object> hostMap = new LinkedHashMap<>();

        //光交换机光端口的正则表达式配置（8个指令）
        String portReg = MqConst.EXP_PREFIX
                      + MqConst.code_arg_query_DEVICE2OMC
                      + MqConst.EXP_DATA_LENGTH
                      + MqConst.OPTICAL_PORT_REGEX
                      + MqConst.EXP_ARG_QUERY_SUFIX;
        //光交换机光收功率的正则表达式配置（前4个指令）
        String receiveReg1 = MqConst.EXP_PREFIX
                      + MqConst.code_arg_query_DEVICE2OMC
                      + MqConst.EXP_DATA_LENGTH
                      + MqConst.OPTICAL_RECEIVE_POWER_REGEX_1
                      + MqConst.EXP_ARG_QUERY_SUFIX;
        //光交换机光收功率的正则表达式配置（后4个指令）
        String receiveReg2 = MqConst.EXP_PREFIX
                      + MqConst.code_arg_query_DEVICE2OMC
                      + MqConst.EXP_DATA_LENGTH
                      + MqConst.OPTICAL_RECEIVE_POWER_REGEX_2
                      + MqConst.EXP_ARG_QUERY_SUFIX;
        //光交换机发收功率的正则表达式配置（前4个指令）
        String outputReg1 = MqConst.EXP_PREFIX
                      + MqConst.code_arg_query_DEVICE2OMC
                      + MqConst.EXP_DATA_LENGTH
                      + MqConst.OPTICAL_OUTPUT_POWER_REGEX_1
                      + MqConst.EXP_ARG_QUERY_SUFIX;
        //光交换机发收功率的正则表达式配置（后4个指令）
        String outputReg2 = MqConst.EXP_PREFIX
                      + MqConst.code_arg_query_DEVICE2OMC
                      + MqConst.EXP_DATA_LENGTH
                      + MqConst.OPTICAL_OUTPUT_POWER_REGEX_2
                      + MqConst.EXP_ARG_QUERY_SUFIX;

        // 报文中的主机编码
        String o_hostNum = opticalMsg.substring(2, 10);
        // 数据库中的主机编码
        String revertHostNum = ByteUtil.reverseHex(o_hostNum);
        //redis key
        String hostKey = GConfig.spticalKey + revertHostNum + ":";
        // 报文响应类型
        String msgResponseType = opticalMsg.substring(16, 18);
        //log.info("来自设备的报文响应类型|FF网管到设备请求填充,01正常,02系统繁忙,03设备故障|当前报文类型:{}", msgResponseType);
        if (msgResponseType.equals(MqParamConfig.MSG_BUSYING)) {
          log.info("系统指令正忙,请稍后重试指令！！");
          return;
        } else if (msgResponseType.equals(MqParamConfig.MSG_ERR)) {
          log.info("设备故障,请稍后重试！！");
          return;
        }

        boolean hasKey = redisUtil.hasKey(hostKey);
        if (hasKey) {
          hostMap = redisUtil.hmget2(hostKey);
        } else {
          hostMap = new LinkedHashMap<>();
        }

        String myMsg = opticalMsg.substring(18 + 4, opticalMsg.length() - 6);

        if (ReUtil.isMatch(portReg, opticalMsg.toLowerCase())) {//IF 正则匹配光交换机光状态反馈指令
              log.info("来自设备的报文响应-解析光交换机光【状态端口查询指令】");
              hostMap.put("hostNumber", revertHostNum);
              hostMap.put("port_1", splitOpticalPortCmd(myMsg,OpticalConst.CODE_OPTICAL_PORT_1));
              hostMap.put("port_2", splitOpticalPortCmd(myMsg,OpticalConst.CODE_OPTICAL_PORT_2));
              hostMap.put("port_3", splitOpticalPortCmd(myMsg,OpticalConst.CODE_OPTICAL_PORT_3));
              hostMap.put("port_4", splitOpticalPortCmd(myMsg,OpticalConst.CODE_OPTICAL_PORT_4));
              hostMap.put("port_5", splitOpticalPortCmd(myMsg,OpticalConst.CODE_OPTICAL_PORT_5));
              hostMap.put("port_6", splitOpticalPortCmd(myMsg,OpticalConst.CODE_OPTICAL_PORT_6));
              hostMap.put("port_7", splitOpticalPortCmd(myMsg,OpticalConst.CODE_OPTICAL_PORT_7));
              hostMap.put("port_8", splitOpticalPortCmd(myMsg,OpticalConst.CODE_OPTICAL_PORT_8));
              redisUtil.hmset(hostKey, hostMap);
        }
         // 光交换机光收功率端口查询指令
        if (ReUtil.isMatch(receiveReg1, opticalMsg.toLowerCase()) ){
              log.info("来自设备的报文响应-解析光交换机光【收功率1~4端口查询指令】");
              hostMap.put("hostNumber", revertHostNum);
              hostMap.put("receive_power_1", splitOpticalPowerCmd(myMsg,OpticalConst.CODE_OPTICAL_RECEIVE_POWER_1));
              hostMap.put("receive_power_2", splitOpticalPowerCmd(myMsg,OpticalConst.CODE_OPTICAL_RECEIVE_POWER_2));
              hostMap.put("receive_power_3", splitOpticalPowerCmd(myMsg,OpticalConst.CODE_OPTICAL_RECEIVE_POWER_3));
              hostMap.put("receive_power_4", splitOpticalPowerCmd(myMsg,OpticalConst.CODE_OPTICAL_RECEIVE_POWER_4));
              redisUtil.hmset(hostKey, hostMap);
        }

        if(ReUtil.isMatch(receiveReg2, opticalMsg.toLowerCase())){
              log.info("来自设备的报文响应-解析光交换机光【收功率5~8端口查询指令】");
              hostMap.put("hostNumber", revertHostNum);
              hostMap.put("receive_power_5", splitOpticalPowerCmd(myMsg,OpticalConst.CODE_OPTICAL_RECEIVE_POWER_5));
              hostMap.put("receive_power_6", splitOpticalPowerCmd(myMsg,OpticalConst.CODE_OPTICAL_RECEIVE_POWER_6));
              hostMap.put("receive_power_7", splitOpticalPowerCmd(myMsg,OpticalConst.CODE_OPTICAL_RECEIVE_POWER_7));
              hostMap.put("receive_power_8", splitOpticalPowerCmd(myMsg,OpticalConst.CODE_OPTICAL_RECEIVE_POWER_8));
              redisUtil.hmset(hostKey, hostMap);
        }
        // 光交换机光发功率1~4端口查询指令
        if (ReUtil.isMatch(outputReg1, opticalMsg.toLowerCase())){
            log.info("来自设备的报文响应解析光交换机光【发功率1~4端口查询指令】");
            hostMap.put("hostNumber", revertHostNum);
            hostMap.put("output_power_1", splitOpticalPowerCmd(myMsg,OpticalConst.CODE_OPTICAL_OUTPUT_POWER_1));
            hostMap.put("output_power_2", splitOpticalPowerCmd(myMsg,OpticalConst.CODE_OPTICAL_OUTPUT_POWER_2));
            hostMap.put("output_power_3", splitOpticalPowerCmd(myMsg,OpticalConst.CODE_OPTICAL_OUTPUT_POWER_3));
            hostMap.put("output_power_4", splitOpticalPowerCmd(myMsg,OpticalConst.CODE_OPTICAL_OUTPUT_POWER_4));
            redisUtil.hmset(hostKey, hostMap);
        }

        // 光交换机光发功率5～8端口查询指令
        if(ReUtil.isMatch(outputReg2, opticalMsg.toLowerCase())){
            log.info("来自设备的报文响应解析光交换机光【发功率5~8端口查询指令】");
            hostMap.put("hostNumber", revertHostNum);
            hostMap.put("output_power_5", splitOpticalPowerCmd(myMsg,OpticalConst.CODE_OPTICAL_OUTPUT_POWER_5));
            hostMap.put("output_power_6", splitOpticalPowerCmd(myMsg,OpticalConst.CODE_OPTICAL_OUTPUT_POWER_6));
            hostMap.put("output_power_7", splitOpticalPowerCmd(myMsg,OpticalConst.CODE_OPTICAL_OUTPUT_POWER_7));
            hostMap.put("output_power_8", splitOpticalPowerCmd(myMsg,OpticalConst.CODE_OPTICAL_OUTPUT_POWER_8));
            redisUtil.hmset(hostKey, hostMap);
        }

        //如果获取存储的redis中的MAP数据不为空 && MAP数据长度等于25(包括端口8 + 收功率8 + 发功率8 +1个主机编码 ） 则储存数据到DB中
        if (ObjectUtil.isNotEmpty(hostMap) && hostMap.size() == 25) {
            Map<String, Object> redisMap =  redisUtil.hmget2(hostKey);
            String payloadJsonString = JSON.toJSONString(redisMap);
            MqttPushClient.getInstance().publishProbeInfo(GConfig.MQ_TOPIC_DEVICE_OPTICAL + revertHostNum, payloadJsonString);
            redisUtil.del(hostKey);//数据入库后进行清空处理
        }
  }

  /**
   * 解析上报指令，获取对应端口协议指令的结果值
   * @param msg:主机上报的指令数据
   * @param portCmd：不同光端口的协议指令
   * @return
   */
  public static int splitOpticalPortCmd(String msg,String portCmd) {
      msg = msg.toUpperCase();//转大写
      // 定义正则表达式，匹配 "04C701" 后面紧跟的两个数字
      Pattern pattern = Pattern.compile(portCmd+"(\\d{2})");
      Matcher matcher = pattern.matcher(msg);
      if (matcher.find()) {
        // 如果找到匹配项，则输出匹配的两个数字
        String twoDigits = matcher.group(1);
        int decimalValue = Integer.parseInt(twoDigits, 16); //16转10进制
        return decimalValue;
      } else {
        //未找到匹配的数字
        return 1; //,0正常，1告警
      }
  }

  public static int splitOpticalPowerCmd(String msg,String portCmd) {
    msg = msg.toUpperCase();//转大写
    // 定义正则表达式，匹配 "04C70170fe" 后面紧跟的4个数字
    Pattern pattern = Pattern.compile(portCmd+"(.{4})");
    Matcher matcher = pattern.matcher(msg);
    if (matcher.find()) {
        // 如果找到匹配项，则输出匹配的两个数字
        String fourDigits = matcher.group(1);
       // log.info("fourDigits:{}",fourDigits);
        if("0000".equals(fourDigits)){
           return 0;
        }else{
          fourDigits = ByteUtil.reverseHex(fourDigits);//高低位转换
          int powerParam =  ByteUtil.hexToSignedDecimal(fourDigits);
          return powerParam / 100; //单位换算
        }
    } else {
      //未找到匹配的数字
      return 0; //,0正常，1告警
    }
  }

  /**
   * 重新解析字符串数据
   * @param data
   * @return
   */
  private static String getParserDataString(String data){
    List<String> listStrs = new ArrayList<String>();
    StringBuffer sBuffer = new StringBuffer();
    while (data.length() > 0) {
      String substring = data.substring(0, 2);
      listStrs.add(substring);
      data = data.substring(2);

    }
    for (String asc : listStrs) {
      char c = (char) Integer.parseInt(asc, 16);
      if ("\0".equals(String.valueOf(c))) {
        break;
      }
      if ("\u0000".equals(String.valueOf(c))) {
        continue;
      }
      sBuffer.append(c);
    }
    return sBuffer.toString();
  }
}
