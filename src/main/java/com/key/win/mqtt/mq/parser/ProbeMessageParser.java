package com.key.win.mqtt.mq.parser;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.baseInfo.model.Probe;
import com.key.win.biz.baseInfo.service.ProbeService;
import com.key.win.biz.topo.enums.DeviceStatus;
import com.key.win.biz.topo.model.TopoNodes;
import com.key.win.biz.topo.service.TopoService;
import com.key.win.config.RedisUtil;
import com.key.win.config.mqtt.MqttPushClient;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.utils.ByteUtil;
import com.key.win.utils.SfCmdStringUtils;
import com.key.win.utils.SpringUtils;
import java.util.*;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;

/**
 * 设备探针信息相关的监测事件处理
 *
 * <AUTHOR>
 */
@Slf4j
public final class ProbeMessageParser {

  private static ProbeService probeService = SpringUtils.getBean(ProbeService.class);
  private static TopoService topoService =
      SpringUtils.getBean(com.key.win.biz.topo.service.TopoService.class);

  private static RedisUtil redisUtil = SpringUtils.getBean(RedisUtil.class);
  private static Environment env = SpringUtils.getBean(Environment.class);

  public void resolve(String msg) {
    String originMsg = msg;
    int state = -1;
    String deviceId = "";
    String probeSid = "";
    String probeId = "";
    String probeStatus = "";
    String pathLoss = "";
    String data = "";
    String type = "";

    TopoNodes node = null;

    boolean antFlag = true; // 使能开关
    msg = ByteUtil.escapeAfter(ByteUtil.hexStringToByteArray(msg));
    if (msg.length() == 54 || msg.length() == 56) {
      msg = SfCmdStringUtils.replace7E(msg);
      msg = msg.substring(0, msg.length() - 4); // 去除crc校验位
      deviceId = msg.substring(0, 8);
      deviceId = ByteUtil.reverseHex(deviceId); // 设备ID
      msg = msg.substring(8); // 去除设备编号
      type = msg.substring(0, 12); // 指令类型
      data = msg.substring(12); // data数据部分
      probeSid = data.substring(0, 2); // 天线的序号
      probeSid = String.valueOf((byte) Integer.parseInt(probeSid, 16));
      if (probeSid.length() < 2) {
        probeSid = "0" + probeSid;
      }

      msg = data.substring(2);
      probeId = msg.substring(0, 24);
      msg = msg.substring(24);

      node = topoService.getAntFlag(deviceId, probeSid);
      if (node != null) {
        antFlag = node.isFlagTrue();
      }
    } else {
      return;
    }

    if (type.equals("040101010d00")) { // 探针查询
      LambdaQueryWrapper<Probe> lqwAnt = new LambdaQueryWrapper<Probe>();
      lqwAnt.eq(Probe::getNumber, probeSid);
      lqwAnt.eq(Probe::getHostNumber, deviceId);
      Probe bean = probeService.getOne(lqwAnt);
      if (bean == null) {
        Probe probe =
            Probe.builder()
                .coding(probeId)
                .hostNumber(deviceId)
                .number(probeSid)
                .oCoding(probeId)
                .probeStatus(probeStatus)
                .build();
        if (!antFlag) {
          probe.setProbeStatus("01");
          probe.setLost(Integer.parseInt(GConfig.ANT_PATH_LOSS));
        }
        probeService.save(probe);
      } else {
        bean.setCoding(probeId);
        bean.setOCoding(probeId);
        bean.setProbeStatus(probeStatus);
        if (!antFlag) {
          bean.setProbeStatus("01");
          bean.setLost(Integer.parseInt(GConfig.ANT_PATH_LOSS));
        }
        probeService.updateById(bean);
      }
    } else if (type.equals("020112010e00")) { // 故障诊断
      if (antFlag) {
        probeStatus = msg;
      } else {
        probeStatus = "01";
      }

      boolean hasKey = redisUtil.hasKey(GConfig.PROBE_CHECK_KEY + deviceId);
      if (hasKey) {
        Map<String, Object> deviceProbeStatusMaps =
            redisUtil.hmget2(GConfig.PROBE_CHECK_KEY + deviceId);
        if (deviceProbeStatusMaps != null) {
          String v = MapUtil.getStr(deviceProbeStatusMaps, probeSid, "");
          if (v.equals("01")) {
            log.info(
                "【com.key.win.mqtt.mq.parser.ProbeMessageParser】主机:{}, 探针:{},信息存在,探针值为:{},探针状态正常!",
                deviceId,
                probeSid,
                probeStatus);
            probeStatus = "01";
          }
        }
      }

      Probe probe = getAntennaByDeviceIdAndProbeId(deviceId, probeSid, probeId);
      node = topoService.getTopoNodesByHostNumberAndProbeSid(deviceId, probeSid);
      boolean exist = true;
      String antName = "";
      String nodeDesc = "";
      if (node == null || StringUtils.isBlank(node.getName())) {
        exist = false;
      } else {
        antName = node.getName();
        nodeDesc = StringUtils.defaultIfBlank(node.getNodeDesc(), "");
      }
      if (probe != null) {
        boolean equals = StringUtils.defaultIfBlank(probe.getProbeStatus(), "").equals(probeStatus);
        if (!equals) {
          probe.setProbeStatus(probeStatus);
          if (probeStatus.equalsIgnoreCase("00")) {
            probe.setLost(120);
            state = DeviceStatus.red.getState();
          } else {
            probe.setLost(1);
            state = DeviceStatus.green.getState();
          }
          topoService.updateNodeStatusByHostNumberAndProbeSid(
              probe.getHostNumber(), probe.getNumber(), state);
          probeService.updateById(probe);
        }
        if (exist) {
          Map<String, String> payload = new HashMap<String, String>();
          payload.put("deviceId", deviceId);
          payload.put("sid", probeSid);
          payload.put("probeId", probeId);
          payload.put("antName", antName);
          payload.put("nodeDesc", nodeDesc);
          String payloadJsonString = JSON.toJSONString(payload);
          if (probeStatus.equals("00")) {
            MqttPushClient.getInstance()
                .publishProbeInfo(GConfig.MQ_TOPIC_PROBE_ERR + deviceId, payloadJsonString);
          } else if (probeStatus.equals("01")) {
            MqttPushClient.getInstance()
                .publishProbeInfo(GConfig.MQ_TOPIC_PROBE_ONLINE + deviceId, payloadJsonString);
          }
        }
      }
    } else if (type.equalsIgnoreCase("020113010e00")) { // 精确读取
      if (antFlag) {
        pathLoss = msg;
      } else {
        // 如果开启使能，则探针的路损值为固定值为20（16进制）
        pathLoss = GConfig.ANT_PATH_LOSS;
      }
      Probe probe = getAntennaByDeviceIdAndProbeId(deviceId, probeSid, probeId);
      node = topoService.getTopoNodesByHostNumberAndProbeSid(deviceId, probeSid);

      if (node == null) {
        return;
      }
      log.info("路损查询-->{},{}", node.getHostNum(), node.getProbeSid());
      if (probe != null) {
        byte loss = (byte) Integer.parseInt(pathLoss, 16);

        // 定义一个redis的探针唯一Key
        String lostCheckKey = GConfig.probeKeyPathLossCheck + deviceId + probeSid;
        // 获取Redis中 Key对应的列表所有数据
        List<Object> lossListObj = redisUtil.lGet(lostCheckKey, 0L, -1L);
        // 如果lossListObj为空，则new一个List
        if (CollectionUtil.isEmpty(lossListObj)) {
          lossListObj = new ArrayList<>();
        }
        // 追加新路损值到列表中
        lossListObj.add((int) loss);
        // 对追加新数据之前清空之前的List数据（删除key）
        redisUtil.del(lostCheckKey);
        // 存储路损值数组到redis中(存储时间5分钟)
        redisUtil.lSet(lostCheckKey, lossListObj, 300);
        // 如果上报的路损值次数 >=3次

        String _reportCount = env.getProperty("sf.alarm.reportCount");
        int reportCount = Integer.parseInt(_reportCount);

        if (lossListObj.size() >= reportCount) {
          List<Integer> lostListInteger =
              lossListObj.stream()
                  .map(obj -> (Integer) obj) // 强制类型转换
                  .collect(Collectors.toList());
          // 获取数组中最小的路损值
          int minLost = Collections.min(lostListInteger);
          log.info("{} 上报三次计算后最小路损值:{}", node.getName(), minLost);
          if (minLost >= 120) {
            state = DeviceStatus.red.getState(); // 异常
            probe.setProbeStatus("00");
          } else {
            state = DeviceStatus.green.getState(); // 正常
            probe.setProbeStatus("01");
          }

          redisUtil.del(lostCheckKey); // 删除缓存
          probe.setLost(minLost);

          Map<String, String> payload = new HashMap<String, String>();
          payload.put("deviceId", deviceId);
          payload.put("sid", probeSid);
          payload.put("probeId", probeId);
          payload.put("antName", node.getName());
          payload.put("loss", String.valueOf(loss));
          payload.put("nodeDesc", StringUtils.defaultIfBlank(node.getNodeDesc(), ""));
          String payloadJsonString = JSON.toJSONString(payload);
          probeStatus = StringUtils.defaultIfBlank(probe.getProbeStatus(), "00");

          log.info("probeStatus:{}", probeStatus);
          log.info("************一次路损查询已上报3次 执行保存到缓存redis,hastMap:{}", payload.toString());

          if (probeStatus.equals("00")) {
            MqttPushClient.getInstance()
                .publishProbeInfo(GConfig.MQ_TOPIC_PROBE_ERR + deviceId, payloadJsonString);
          } else if (probeStatus.equals("01")) {
            MqttPushClient.getInstance()
                .publishProbeInfo(GConfig.MQ_TOPIC_PROBE_ONLINE + deviceId, payloadJsonString);
          }

          // 发送消息-记录历史天线路损值 （2024-05-27新增）
          MqttPushClient.getInstance()
              .publishProbeInfo(GConfig.MQ_TOPIC_DEVICE_HIS_ANT_LOSS + deviceId, payloadJsonString);

          topoService.updateNodeStatusByHostNumberAndProbeSid(
              probe.getHostNumber(), probe.getNumber(), state);
          probeService.updateById(probe);
        }
      }
    } else {
      log.info("其他指令:{}", originMsg);
    }
  }

  private Probe getAntennaByDeviceIdAndProbeId(String deviceId, String sid, String probeId) {
    LambdaQueryWrapper<Probe> lqw = new LambdaQueryWrapper<Probe>();
    lqw.eq(Probe::getHostNumber, deviceId);
    lqw.eq(Probe::getNumber, sid);
    lqw.eq(Probe::getCoding, probeId);
    Probe probe = probeService.getOne(lqw);
    return probe;
  }
}
