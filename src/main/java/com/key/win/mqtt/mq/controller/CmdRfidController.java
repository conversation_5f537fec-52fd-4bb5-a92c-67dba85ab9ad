package com.key.win.mqtt.mq.controller;

import com.key.win.mqtt.Scheduling.config.runnable.service.DeviceArgumentSaveService;
import com.key.win.mqtt.mq.cmd.EmqRequestService;
import com.key.win.mqtt.mq.cmd.TestPageCmdService;
import com.key.win.mqtt.mq.vo.ResultVo;
import java.util.ArrayList;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/cmd/rfid/*")
public class CmdRfidController {

  @Autowired private TestPageCmdService testPageCmdService;

  @Autowired private EmqRequestService emqRequestService;

  @Resource private DeviceArgumentSaveService mqInfoRedisAutoService;

  @Autowired private ThreadPoolTaskScheduler threadPoolTaskScheduler;

  @PostMapping("/faultDiagnosisByDeviceId/{deviceId}")
  public ResultVo faultDiagnosisByDeviceId(@PathVariable("deviceId") String deviceId) {
    boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + deviceId);
    if (!clientIsOnline) {
      return new ResultVo("设备当前离线.", new ArrayList<>());
    }

    return testPageCmdService.faultDiagnosisByDeviceId(deviceId);
  }

  @PostMapping("/accurateReadingByDeviceId/{deviceId}")
  public ResultVo accurateReadingByDeviceId(@PathVariable("deviceId") String deviceId)
      throws InterruptedException {
    boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + deviceId);
    if (!clientIsOnline) {
      return new ResultVo("设备当前离线.", new ArrayList<>());
    }
    return testPageCmdService.accurateReadingByDeviceId(deviceId);
  }
}
