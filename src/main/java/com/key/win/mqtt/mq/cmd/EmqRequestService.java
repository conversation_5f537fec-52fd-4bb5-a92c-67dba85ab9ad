package com.key.win.mqtt.mq.cmd;

import cn.hutool.core.map.MapUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.system.config.sf.model.SfGlobalConfig;
import com.key.win.system.config.sf.service.ISfGlobalConfigService;
import com.key.win.system.config.sf.support.SfGlobalConfigString;
import com.key.win.system.config.sf.support.SfGlobalConfigType;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 和MEQ通信相关API
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@SuppressWarnings("rawtypes")
public class EmqRequestService implements InitializingBean {

  private static String uname = "";
  private static String pwd = "";
  String emq_base_url = "";
  String lookup_a_client_in_the_cluster = "/api/v4/clients/";
  @Autowired private BelongUnitService belongUnitService;
  @Autowired private ISfGlobalConfigService configService;

  /**
   * 查看设备是否在线
   *
   * @param clientId
   * @return
   */
  public boolean clientIsOnline(String clientId) {

    String hostNumber = clientId.replace("reader_", "");

    String up = uname + ":" + pwd;
    byte[] encodeBase64 = Base64.encodeBase64(up.getBytes());
    String base64UserMsg = new String(encodeBase64);
    String authorization = "Basic " + base64UserMsg;
    String content_type = "application/x-www-form-urlencoded";

    String resource_url = lookup_a_client_in_the_cluster + clientId;
    String url = emq_base_url + resource_url;

    HttpRequest httpRequest = new HttpRequest(url);
    httpRequest.header(Header.AUTHORIZATION, authorization);
    httpRequest.header(Header.CONTENT_TYPE, content_type);
    boolean isOnline = false;
    HttpResponse response = null;
    try{
      response = httpRequest.execute();
      if (response.getStatus() == HttpStatus.HTTP_OK) {
        String rsponse_string = response.body();
        Map rsponse_map = JSON.parseObject(rsponse_string, Map.class);
        Integer code = MapUtil.getInt(rsponse_map, "code");

        String data_string = MapUtil.getStr(rsponse_map, "data");
        List<Map> data_list = JSON.parseArray(data_string, Map.class);

        if (data_list.size() == 0) {
          isOnline = false;
        }
        if (data_list.size() == 1) {
          isOnline = MapUtil.getBool(data_list.get(0), "connected");
        }
      }
      if (isOnline) {
        belongUnitService.online(hostNumber);
      } else {
        belongUnitService.offline(hostNumber);
      }
    }catch (Exception exception){
        log.error("clientIsOnline 方法执行异常>>>>:{}", exception.getMessage());
    }finally{
      response.close();
    }
    return isOnline;
  }

  @Override
  public void afterPropertiesSet() throws Exception {

    LambdaQueryWrapper<SfGlobalConfig> lqw = new LambdaQueryWrapper<SfGlobalConfig>();
    lqw.eq(SfGlobalConfig::getType, SfGlobalConfigType.CONFIG_MQ_ADDR_SETTING.toString());
    SfGlobalConfig cfgBean = configService.getOne(lqw);
    if (cfgBean == null) {
      this.emq_base_url = "http://127.0.0.1:18083";
      log.warn("[系统警告]:没有配置MQ连接地址,请检查配置信息???????");
      return;
    }
    String configString = StringUtils.defaultString(cfgBean.getConfig(), "{}");
    Map m = JSONArray.parseObject(configString, Map.class);
    Boolean bool = MapUtil.getBool(m, SfGlobalConfigString.ConfigMapKey_enabled, false);
    String ip = MapUtil.getStr(m, SfGlobalConfigString.ConfigMapKey_MQ_ADDR_IP);
    uname = MapUtil.getStr(m, SfGlobalConfigString.ConfigMapKey_MQ_USERNAME, "indoorSystem");
    pwd = MapUtil.getStr(m, SfGlobalConfigString.ConfigMapKey_MQ_PASSWORD, "indoorSystem");
    if (!bool) {
      this.emq_base_url = "http://127.0.0.1:18083";
      log.warn("[系统警告]:当前MQ连接地址{},没有开启配置项,?????", ip);
      return;
    } else {
      this.emq_base_url = "http://" + ip + ":18083";
    }
  }
}
