package com.key.win.mqtt.mq.parser;

import cn.hutool.json.JSONUtil;
import com.key.win.biz.baseInfo.service.OpticalSwitchService;
import com.key.win.biz.baseInfo.vo.OpticalSwitchVo;
import com.key.win.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;

;

/**
 * 光交换机光端口状态，功率解析入库处理
 */
@Slf4j
public class OpticalSwitchMessageParser {

    private static OpticalSwitchService opticalSwitchService = SpringUtils.getBean(OpticalSwitchService.class);

    public static void resolve(String payload) {
        OpticalSwitchVo bean = JSONUtil.toBean(payload, OpticalSwitchVo.class);
        opticalSwitchService.saveOrUpdateOptical(bean);
    }
}
