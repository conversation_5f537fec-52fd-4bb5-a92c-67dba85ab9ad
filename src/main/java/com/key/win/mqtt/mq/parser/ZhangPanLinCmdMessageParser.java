package com.key.win.mqtt.mq.parser;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.config.RedisUtil;
import com.key.win.config.mqtt.MqttPushClient;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.mqtt.mq.statilFile.MqParamConfig;
import com.key.win.mqtt.mq.statilFile.MqStaticMap;
import com.key.win.utils.ByteUtil;
import com.key.win.utils.SpringUtils;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/10/18 13:52
 */
@Slf4j
public class ZhangPanLinCmdMessageParser {

  private String originMsg;

  public void dealWithMessage(String msg, String topic) {

    if (StringUtils.isNotBlank(msg)) {
      originMsg = msg;
      // 校验位数
      if (msg.length() < 24) {
        log.error("====Not enough digits========== 位数不够,结束解析×××××××");
        return;
      }
      // 校验前缀
      // 1 获取前缀
      String suffix = msg.substring(0, 2);
      if (!suffix.equals("7e")) {
        log.error("============== 开始标识符不正确 Incorrect start identifier");
        return;
      }
      msg = msg.substring(2);
      // 校验后缀
      String backSuffix = msg.substring(msg.length() - 2);
      // 2 获取后缀
      if (!backSuffix.equals("7e")) {
        log.error("=========结束标识符不正确===== Incorrect end identifier");
        return;
      }
      msg = msg.substring(0, msg.length() - 2);
      msg = ByteUtil.escapeAfter(ByteUtil.hexStringToByteArray(msg));
      // 效验码
      String crc = msg.substring(msg.length() - 4);
      msg = msg.substring(0, msg.length() - 4);
      String crc16 = ByteUtil.crc(msg);
      if (!crc.equals(crc16)) {
        log.error("=====crc校验不正确========= crc check is incorrect");
        return;
      }
      // 获取设备id#
      String id = msg.substring(0, 8);
      id = ByteUtil.reverseHex(id);
      if (!id.equals(topic)) {
        log.error(
            "===设备id不相等=========== The 【device id】【"
                + id
                + "】"
                + " in the command is not equals 【topic id】【"
                + topic
                + "】");
        return;
      }
      msg = msg.substring(8);

      // 命令类型
      String orderType = msg.substring(0, 2);
      msg = msg.substring(2);

      // 上下行
      String upOrDown = msg.substring(0, 2);
      msg = msg.substring(2);

      // 01 上行，（设备到网关）
      if (MqParamConfig.UP.equals(upOrDown)) {
        log.debug(" 设备到网关命令  Device ------> gateway command：----->" + originMsg);

        switch (orderType) {
            // 01 系统命令
          case MqParamConfig.SYSTEM_ORDER:
            dealWithSystemOrder(topic, msg);
            break;
            // RFID相关命令 ( 有问题)
          case MqParamConfig.RFID_ORDER:
            dealWithRFIDOrder(topic, msg);
            break;
            // 参数查询配置相关命令 ( 有问题)
          case MqParamConfig.PARAM_CONFIG_ORDER:
            dealWithQueryOrder(topic, msg);
            break;
        }
      } else {
        log.debug("网关 到设备 命令  Gateway ----------> device command：----->" + originMsg);
        return;
      }
    }
  }

  // 01 系统命令
  private void dealWithSystemOrder(String topic, String msage) {
    // 功能代码
    String functionOrder = msage.substring(0, 2);
    if (MqParamConfig.HEART_BEAT.equals(functionOrder)) {
      BelongUnitService belongUnitService = SpringUtils.getBean(BelongUnitService.class);
      List<BelongUnit> belongUnitList =
          belongUnitService.list(
              new LambdaQueryWrapper<BelongUnit>().eq(BelongUnit::getHostNum, topic));
      if (CollectionUtils.isEmpty(belongUnitList)) {
        log.debug(
            "=============心跳指令设备未注册  Heartbeat command: No registered device  =========:" + topic);
        return;
      }
      RedisUtil redisUtil = SpringUtils.getBean(RedisUtil.class);
      log.debug("============= 心跳指令  Heartbeat command:  device number： =========" + topic);

      // 存入设备状态到redis
      String cacheKey = GConfig.aliveKey;
      redisUtil.set(cacheKey + topic, new Date());
      log.debug(
          " 心跳信息存入缓存{}{}:{}  Heartbeat information live in the cache{}{}:{}",
          cacheKey,
          topic,
          "alived");

      // 回心跳给设备
      String hbData = topic + "01000101040000000000";
      // 回心跳CRC
      String crc = ByteUtil.crc(hbData);
      String heartBeatCmd = "7e" + hbData + crc + "7e".replace(" ", "");
      String heartBeatCmdEscaped =
          ByteUtil.escapeBefore(ByteUtil.hexStringToByteArray(heartBeatCmd));
      String key =
          topic
              + MqParamConfig.SYSTEM_ORDER
              + "**"
              + MqParamConfig.HEART_BEAT
              + "**"
              + "0400"
              + "00000000";
      putMapValueByKey(key);
      String revert_hostNum = ByteUtil.reverseHex(topic);
      MqttPushClient.getInstance()
          .publish(0, false, "reader_" + revert_hostNum, heartBeatCmdEscaped);
    } else if (MqParamConfig.SYSTEM_RESTART.equals(functionOrder)) {
      String key = topic + MqParamConfig.SYSTEM_ORDER + "**" + functionOrder;
      putMapValueByKey(key);
      log.debug("=============系统重置指令 System reset command  =========");
    }
  }

  // RFID相关命令
  private void dealWithRFIDOrder(String topic, String msage) {
    // 功能代码
    String key;
    String functionOrder = msage.substring(0, 2);
    switch (functionOrder) {
      case "08":
        String keyTwo = topic + MqParamConfig.RFID_ORDER + "**" + "10";
        log.debug("============= 故障诊断返回探针值  Fault diagnosis returns probe value  =========");
        putMapValueByKey(keyTwo);
        break;
      case MqParamConfig.FAULT_DIAGNOSIS:
        String keyOne = topic + MqParamConfig.RFID_ORDER + "**" + MqParamConfig.STATUS_REPORTIN;
        putMapValueByKey(keyOne);
        log.debug("============= 故障诊断 Troubleshooting =========");
        break;
      case MqParamConfig.ACCURATE_READING:
        log.debug("============= 精确读取: Accurate reading  =========");
        key = topic + MqParamConfig.RFID_ORDER + "**" + functionOrder;
        putMapValueByKey(key);
        break;
      case MqParamConfig.STATUS_REPORTIN:
        log.debug("============= 状态上报: Status report  =========");
        keyOne = topic + MqParamConfig.RFID_ORDER + "**" + "12";
        putMapValueByKey(keyOne);
        break;
      case MqParamConfig.ROAD_LOSS_REPOR:
        log.debug("============= 路损上报: Road loss report  =========");
        break;
    }
  }

  private void putMapValueByKey(String key) {
    List<String> list = MqStaticMap.map.get(key);
    if (list == null) {
      list = new ArrayList<>();
    }
    list.add(originMsg);
    MqStaticMap.map.put(key, list);
  }

  // 参数查询配置相关命令
  private void dealWithQueryOrder(String topic, String msage) {
    // 功能代码
    String functionOrder = msage.substring(0, 2);
    msage = msage.substring(2);
    String key;
    switch (functionOrder) {
      case MqParamConfig.QUERY_DEVICE_PROBE:
        log.debug("============= 查询设备探针:  Query device probe  =========");
        key = topic + MqParamConfig.PARAM_CONFIG_ORDER + "**" + functionOrder;
        putMapValueByKey(key);
        break;
      case MqParamConfig.SET_DEVICE_PROBE:
        log.debug("============= 设置设备探针:  Set up the device probe  =========");
        key = topic + MqParamConfig.PARAM_CONFIG_ORDER + "**" + functionOrder;
        putMapValueByKey(key);
        break;
      case MqParamConfig.DELETE_DEVICE_PROBE:
        log.debug("============= 删除设备探针: Delete device probe  =========");
        key = topic + MqParamConfig.PARAM_CONFIG_ORDER + "**" + functionOrder;
        putMapValueByKey(key);
        break;
      case MqParamConfig.DELETE_DEVICE_PROBE_BATCH:
        log.debug("============= 批量删除设备探针: Delete device probe  =========");
        key = topic + MqParamConfig.PARAM_CONFIG_ORDER + "**" + functionOrder;
        putMapValueByKey(key);
        break;
      case MqParamConfig.WARE_INFO_QUERY:
        log.debug("============= 告警信息查询:  Alarm information query  =========");
        key = topic + MqParamConfig.PARAM_CONFIG_ORDER + "**" + MqParamConfig.WARE_INFO_QUERY;
        putMapValueByKey(key);
        break;
      case MqParamConfig.PARAMETER_QUERY:
        log.debug("============= 参数查询:  Parameter query=========");
        if (StringUtils.isNotBlank(msage) && msage.length() >= 14) {
          dealwithQueryMethod(topic, msage);
        }
        break;
      case MqParamConfig.PARAMETER_SETTING:
        dealwithSettingMethod(topic, msage);
        log.debug("============= 参数设置:  parameter settings =========");
        break;
    }
  }

  private void dealwithSettingMethod(String topic, String msage) {
    String filling = msage.substring(0, 2);

    if (!"01".equals(filling)) {
      return;
    }
    String code = msage.substring(2, 12);
    String key =
        topic
            + MqParamConfig.PARAM_CONFIG_ORDER
            + "**"
            + MqParamConfig.PARAMETER_SETTING
            + "**"
            + code;
    putMapValueByKey(key);
    String result = code.substring(6);

    switch (result) {
      case "07b001":
        log.debug("============= 设置开始结束频率: Set start and end frequency  =========");
        break;
      case "05b201":
        log.debug("============= 设备步进频率:  Device step frequency =========");
        break;
      case "04b301":
        log.debug("============= 设置发射功率: Set transmit power  =========");
        break;
      case "04b401":
        log.debug("============= 设置故障开关: Set fault switch  =========");
        break;
      case "04b801":
        log.debug("============= 设置精确读取开关: Set accurate reading switch  =========");
        break;
      case "04b901":
        log.debug("============= 设置精确读取修正值: Set accurate reading correction value  =========");
        break;
      case "04ba01":
        log.debug("============= 设置起始功率,结束功率:   Set start power, end power=========");
        break;
      case "04b701":
        log.debug("============= 设置故障诊断次数  Set fault diagnosis times  =========");
        break;
      case "05b701":
        log.debug("============= 设置故障诊断周期: Set the fault diagnosis cycle  =========");
        break;
      case "05b501":
        log.debug("============= 设置故障诊断时间:   Set fault diagnosis time=========");
        break;
      case "17ab00":
        log.debug("============= 设置服务器ip  Set server ip  =========");
        break;
      case "05A900":
        log.debug("============= 设置服务器端口号:   Set the server port number=========");
        break;
    }
  }

  private void dealwithQueryMethod(String topic, String msage) {
    String filling = msage.substring(0, 2);

    if (!"01".equals(filling)) {
      return;
    }
    String code = msage.substring(0, 12);
    String key =
        topic + MqParamConfig.PARAM_CONFIG_ORDER + "**" + MqParamConfig.PARAMETER_QUERY + code;
    putMapValueByKey(key);
    String result = code.substring(6).toUpperCase();
    switch (result) {
      case "17A000":
        log.debug("============= 软件版本: Software version  =========");
        break;
      case "17A100":
        log.debug("============= 设备型号: Software version  =========");
        break;
      case "17A200":
        log.debug("============= 设备生产系列号: Software SerialVersion =========");
        break;
      case "0AA300":
        log.debug("============= 设备时间: Host Time  =========");
        break;
      case "17A800":
        log.debug("============= 查看IP  View IP  =========");
        break;
      case "05a900":
        log.debug("============= 服务器端口号: Server port number  =========");
        break;
      case "07B001":
        log.debug("============= 开始频率  Start MHz  =========");
        break;
      case "07B101":
        log.debug("============= 结束频率  End MHz  =========");
        break;
      case "05B201":
        log.debug("============= 频率步进 MHz Step  =========");
        break;
      case "04b301":
        log.debug("============= 发射功率: Transmit power  =========");
        break;
      case "04b401":
        log.debug("============= 故障诊断开关:   =========");
        break;
      case "05b501":
        log.debug("============= 故障诊断时间: Fault diagnosis Time  =========");
        break;
      case "05b601":
        log.debug("============= 故障诊断周期: Fault diagnosis cycle  =========");
        break;
      case "04b701":
        log.debug("============= 故障诊断次数:   Fault diagnosis times=========");
        break;
      case "04b801":
        log.debug("============= 精确读取开关:   read Switch=========");
        break;
      case "04b901":
        log.debug("============= 精确读取修正值:  =========");
        break;
      case "04ba01":
        log.debug("============= 查询起始功率: Query starting power  =========");
        break;
      case "04bB01":
        log.debug("============= 查询结束功率: Query End power  =========");
        break;
    }
  }
}
