package com.key.win.mqtt.mq.controller;

import com.key.win.biz.baseInfo.model.Probe;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.mqtt.mq.cmd.EmqRequestService;
import com.key.win.mqtt.mq.cmd.TestPageCmdService;
import com.key.win.mqtt.mq.vo.ResultVo;
import java.util.ArrayList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 全指令接口API，暂时未被使用
 * @date 2023/03/23
 */
@RestController
@RequestMapping("/send")
public class CmdAllController {

  @Autowired private EmqRequestService emqRequestService;

  @Autowired private TestPageCmdService testPageCmdService;

  @PostMapping("/accurateReadingByDeviceId/{deviceId}")
  public ResultVo accurateReadingByDeviceId(@PathVariable("deviceId") String deviceId)
      throws InterruptedException {
    boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + deviceId);
    if (!clientIsOnline) {
      return new ResultVo("当前设备处于离线状态,请检查网路链接!", new ArrayList<>());
    }
    return testPageCmdService.accurateReadingByDeviceId(deviceId);
  }

  @PostMapping("/faultDiagnosisByDeviceId/{deviceId}")
  public ResultVo faultDiagnosisByDeviceId(@PathVariable("deviceId") String deviceId) {
    boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + deviceId);
    if (!clientIsOnline) {
      return new ResultVo("当前设备处于离线状态,请检查网路链接!", new ArrayList<>());
    }
    return testPageCmdService.faultDiagnosisByDeviceId(deviceId);
  }

  @PostMapping("/systemRestart/{deviceId}")
  public ResultVo systemRestartByDeviceId(@PathVariable("deviceId") String deviceId) {
    return testPageCmdService.systemReBootByDeviceId(deviceId);
  }

  @PostMapping("/lookDeviceProbe/{deviceId}")
  public ResultVo lookDeviceProbe(@PathVariable("deviceId") String deviceId) {
    boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + deviceId);
    if (!clientIsOnline) {
      return new ResultVo("当前设备处于离线状态,请检查网路链接!", new ArrayList<>());
    }
    return testPageCmdService.lookDeviceProbe(deviceId);
  }

  @PostMapping("/startEndPower/{deviceId}")
  public ResultVo startEndPower(@PathVariable("deviceId") String deviceId) {
    boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + deviceId);
    if (!clientIsOnline) {
      return new ResultVo("当前设备处于离线状态,请检查网路链接!", new ArrayList<>());
    }
    return testPageCmdService.startEndPower(deviceId);
  }

  @PostMapping("/equipmenTime/{deviceId}")
  public ResultVo equipmenTime(@PathVariable("deviceId") String deviceId) {
    boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + deviceId);
    if (!clientIsOnline) {
      return new ResultVo("当前设备处于离线状态,请检查网路链接!", new ArrayList<>());
    }
    return testPageCmdService.equipmenTime(deviceId);
  }

  @PostMapping("/softVersion/{deviceId}")
  public ResultVo softVersion(@PathVariable("deviceId") String deviceId) {
    boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + deviceId);
    if (!clientIsOnline) {
      return new ResultVo("当前设备处于离线状态,请检查网路链接!", new ArrayList<>());
    }
    return testPageCmdService.softVersion(deviceId);
  }

  @PostMapping("/lookIp/{deviceId}")
  public ResultVo lookIp(@PathVariable("deviceId") String deviceId) {
    boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + deviceId);
    if (!clientIsOnline) {
      return new ResultVo("当前设备处于离线状态,请检查网路链接!", new ArrayList<>());
    }
    return testPageCmdService.lookIp(deviceId);
  }

  @PostMapping("/serverAno/{deviceId}")
  public ResultVo serverAno(@PathVariable("deviceId") String deviceId) {
    boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + deviceId);
    if (!clientIsOnline) {
      return new ResultVo("当前设备处于离线状态,请检查网路链接!", new ArrayList<>());
    }
    return testPageCmdService.serverAno(deviceId);
  }

  @PostMapping("/faultDiagnosisTime/{deviceId}")
  public ResultVo faultDiagnosisTime(@PathVariable("deviceId") String deviceId) {
    boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + deviceId);
    if (!clientIsOnline) {
      return new ResultVo("当前设备处于离线状态,请检查网路链接!", new ArrayList<>());
    }
    return testPageCmdService.faultDiagnosisTime(deviceId);
  }

  @PostMapping("/launchPower/{deviceId}")
  public ResultVo launchPower(@PathVariable("deviceId") String deviceId) {
    boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + deviceId);
    if (!clientIsOnline) {
      return new ResultVo("当前设备处于离线状态,请检查网路链接!", new ArrayList<>());
    }
    return testPageCmdService.launchPower(deviceId);
  }

  @PostMapping("/faultDiagnosisTimes/{deviceId}")
  public ResultVo faultDiagnosisTimes(@PathVariable("deviceId") String deviceId) {
    boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + deviceId);
    if (!clientIsOnline) {
      return new ResultVo("当前设备处于离线状态,请检查网路链接!", new ArrayList<>());
    }
    return testPageCmdService.faultDiagnosisTimes(deviceId);
  }

  @PostMapping("/lookFrequencyStep/{deviceId}")
  public ResultVo lookFrequencyStep(@PathVariable("deviceId") String deviceId) {
    boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + deviceId);
    if (!clientIsOnline) {
      return new ResultVo("当前设备处于离线状态,请检查网路链接!", new ArrayList<>());
    }
    return testPageCmdService.lookFrequencyStep(deviceId);
  }

  @PostMapping("/lookFaultSwitch/{deviceId}")
  public ResultVo lookFaultSwitch(@PathVariable("deviceId") String deviceId) {
    boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + deviceId);
    if (!clientIsOnline) {
      return new ResultVo("当前设备处于离线状态,请检查网路链接!", new ArrayList<>());
    }
    return testPageCmdService.lookFaultSwitch(deviceId);
  }

  @PostMapping("/lookAccurateReadingSwitch/{deviceId}")
  public ResultVo lookAccurateReadingSwitch(@PathVariable("deviceId") String deviceId) {
    boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + deviceId);
    if (!clientIsOnline) {
      return new ResultVo("当前设备处于离线状态,请检查网路链接!", new ArrayList<>());
    }
    return testPageCmdService.lookAccurateReadingSwitch(deviceId);
  }

  @PostMapping("/lookAccuratelyReadTheCorrectionValue/{deviceId}")
  public ResultVo lookAccuratelyReadTheCorrectionValue(@PathVariable("deviceId") String deviceId) {
    boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + deviceId);
    if (!clientIsOnline) {
      return new ResultVo("当前设备处于离线状态,请检查网路链接!", new ArrayList<>());
    }
    return testPageCmdService.lookAccuratelyReadTheCorrectionValue(deviceId);
  }

  // ========================================  下面是设置 ======================================
  @PostMapping("/setUpProbe")
  public ResultVo setUpProbe(@RequestBody Probe probe) {
    boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + probe.getHostNumber());
    if (!clientIsOnline) {
      return new ResultVo("当前设备处于离线状态,请检查网路链接!", new ArrayList<>());
    }
    return testPageCmdService.setUpProbe(probe);
  }

  @PostMapping("/deleteProbe")
  public ResultVo deleteProbe(@RequestBody Probe probe) {
    boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + probe.getHostNumber());
    if (!clientIsOnline) {
      return new ResultVo("当前设备处于离线状态,请检查网路链接!", new ArrayList<>());
    }
    return testPageCmdService.deleteProbe(probe);
  }

  @PostMapping("/setUpStartAndEndFrequency")
  public ResultVo setUpStartAndEndFrequency(@RequestBody BelongUnit belongUnit) {
    //        return sendService.setUpStartAndEndFrequency(belongUnit);
    return new ResultVo();
  }

  @PostMapping("/frequencyStep")
  public ResultVo frequencyStep(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setFrequencyStep(belongUnit);
  }

  @PostMapping("/setTransmitPower")
  public ResultVo setTransmitPower(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setTransmitPower(belongUnit);
  }

  @PostMapping("/setFaultSwitch")
  public ResultVo setFaultSwitch(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setFaultSwitch(belongUnit);
  }

  @PostMapping("/setAccurateReadingSwitch")
  public ResultVo setAccurateReadingSwitch(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setAccurateReadingSwitch(belongUnit);
  }

  @PostMapping("/accuratelyReadTheCorrectionValue")
  public ResultVo accuratelyReadTheCorrectionValue(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setAccuratelyReadTheCorrectionValue(belongUnit);
  }

  @PostMapping("/setStartPowerEndPower")
  public ResultVo SetStartPowerEndPower(@RequestBody BelongUnit belongUnit) {
    //      return sendService.setStartPowerEndPower(belongUnit);
    return new ResultVo();
  }

  /**
   * 故障诊断时间
   * @param belongUnit
   * @return
           */
  @PostMapping("/setFaultDiagnosisTimes")
  public ResultVo setFaultDiagnosisNumber(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setFaultDiagnosisNumber(belongUnit);
  }

  @PostMapping("/setTheFaultDiagnosisCycle")
  public ResultVo setTheFaultDiagnosisCycle(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setTheFaultDiagnosisCycle(belongUnit);
  }

  @PostMapping("/setFaultDiagnosisTime")
  public ResultVo setFaultDiagnosisTime(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setFaultDiagnosisTime(belongUnit);
  }

  @PostMapping("/setUpTheServer")
  public ResultVo setUpTheServer(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setUpTheServer(belongUnit);
  }

  @PostMapping("/setUpThePort")
  public ResultVo setUpThePort(@RequestBody BelongUnit belongUnit) {
    return testPageCmdService.setUpThePort(belongUnit);
  }
}
