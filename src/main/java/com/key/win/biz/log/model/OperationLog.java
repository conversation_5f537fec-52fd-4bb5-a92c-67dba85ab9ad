package com.key.win.biz.log.model;

import javax.persistence.Entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.key.win.common.web.MybatisID;

import lombok.Data;

/** 操作日志 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@TableName("sys_log")
@Entity(name = "sys_log")
public class OperationLog extends MybatisID {

  private String id;

  private String username;

  private String module;

  private String params;

  private String remark;

  private String userId;

  private String flag;

  private String createTime;

  private String content;

  private String operationObject;

  private String operatResult;

  private String operatIp;

  private String pid;

  private String type;
}
