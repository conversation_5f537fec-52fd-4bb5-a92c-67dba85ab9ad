package com.key.win.biz.log.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.key.win.biz.alarm.model.AlarmExportDto;
import com.key.win.biz.log.dao.OperationLogDao;
import com.key.win.biz.log.model.OperationLog;
import com.key.win.biz.log.model.OperationLogExportDto;
import com.key.win.biz.log.param.LogParam;
import com.key.win.biz.log.service.OperationLogService;
import com.key.win.common.web.MybatiesPageServiceTemplate;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import com.key.win.system.aop.annotation.LogType;
import com.key.win.utils.SpringBeanUtilsExt;

/**
 * @description: 操作日志
 * @return:
 * @date: 2021/8/11 16:14
 */
@Service
public class OperationLogImpl extends ServiceImpl<OperationLogDao, OperationLog>
    implements OperationLogService {

  //    @Override
  //    public PageResult<OperationLog> getPageOperationLog(PageRequest<LogParam> pageRequest) {
  //        MybatiesPageServiceTemplate<LogParam, OperationLog> page = new
  // MybatiesPageServiceTemplate<LogParam, OperationLog>(this.baseMapper) {
  //            @Override
  //            protected Wrapper<OperationLog> constructWrapper(LogParam logParam) {
  //                LambdaQueryWrapper<OperationLog> lqw = new LambdaQueryWrapper<OperationLog>();
  //                if (logParam == null) {
  //                    return lqw;
  //                }
  //                if (StringUtils.isNotBlank(logParam.getUsername())) {
  //                    lqw.like(OperationLog::getUsername, logParam.getUsername());
  //                }
  //                if (StringUtils.isNotBlank(logParam.getStartTime()) &&
  // StringUtils.isNotBlank(logParam.getEndTime())) {
  //                    lqw.between(OperationLog::getCreateTime, logParam.getStartTime(),
  // logParam.getEndTime());
  //                }
  //                if (StringUtils.isNotBlank(logParam.getType())) {
  //                    //lqw.eq(OperationLog::getType, logParam.getType());
  //                }
  //                lqw.orderByDesc(OperationLog::getCreateTime);
  //                return lqw;
  //            }
  //        };
  //        PageResult<OperationLog> dataSourceTestPageResult = page.doPagingQuery(pageRequest);
  //        return dataSourceTestPageResult;
  //    }

  @Override
  public PageResult<OperationLog> sysLog(PageRequest<LogParam> pageRequest) {
    MybatiesPageServiceTemplate<LogParam, OperationLog> page =
        new MybatiesPageServiceTemplate<LogParam, OperationLog>(this.baseMapper) {
          @Override
          protected Wrapper<OperationLog> constructWrapper(LogParam logParam) {
            LambdaQueryWrapper<OperationLog> lqw = new LambdaQueryWrapper<OperationLog>();
            if (logParam == null) {
              lqw.and(
                  lq ->
                      lq.eq(OperationLog::getType, LogType.SysLog)
                          .or()
                          .eq(OperationLog::getType, LogType.AuthLog));
              return lqw;
            }
            if (StringUtils.isNotBlank(logParam.getUsername())) {
              String keyword = logParam.getUsername();
              lqw.and(
                  lq ->
                      lq.like(OperationLog::getUsername, keyword)
                          .or()
                          .like(OperationLog::getContent, keyword)
                          .or()
                          .like(OperationLog::getParams, keyword)
                          .or()
                          .like(OperationLog::getModule, keyword)
                          .or()
                          .like(OperationLog::getContent, keyword));
            }

            lqw.and(
                lq ->
                    lq.eq(OperationLog::getType, LogType.SysLog)
                        .or()
                        .eq(OperationLog::getType, LogType.AuthLog));

            if (StringUtils.isNotBlank(logParam.getStartTime())
                && StringUtils.isNotBlank(logParam.getEndTime())) {
              lqw.between(
                  OperationLog::getCreateTime, logParam.getStartTime(), logParam.getEndTime());
            }
            lqw.orderByDesc(OperationLog::getCreateTime);
            return lqw;
          }
        };
    PageResult<OperationLog> dataSourceTestPageResult = page.doPagingQuery(pageRequest);
    return dataSourceTestPageResult;
  }

  @Override
  public PageResult<OperationLog> alarmLog(PageRequest<LogParam> pageRequest) {
    MybatiesPageServiceTemplate<LogParam, OperationLog> page =
        new MybatiesPageServiceTemplate<LogParam, OperationLog>(this.baseMapper) {
          @Override
          protected Wrapper<OperationLog> constructWrapper(LogParam logParam) {
            LambdaQueryWrapper<OperationLog> lqw = new LambdaQueryWrapper<OperationLog>();
            if (logParam == null) {
              lqw.and(lq -> lq.eq(OperationLog::getType, LogType.Alarm));
              return lqw;
            }
            if (StringUtils.isNotBlank(logParam.getUsername())) {
              String keyword = logParam.getUsername();
              lqw.and(
                  lq ->
                      lq.like(OperationLog::getUsername, keyword)
                          .or()
                          .like(OperationLog::getContent, keyword)
                          .or()
                          .like(OperationLog::getParams, keyword)
                          .or()
                          .like(OperationLog::getModule, keyword)
                          .or()
                          .like(OperationLog::getContent, keyword));
            }
            if (StringUtils.isNotBlank(logParam.getStartTime())
                && StringUtils.isNotBlank(logParam.getEndTime())) {
              lqw.between(
                  OperationLog::getCreateTime, logParam.getStartTime(), logParam.getEndTime());
            }
            lqw.and(lq -> lq.eq(OperationLog::getType, LogType.Alarm));
            lqw.orderByDesc(OperationLog::getCreateTime);
            return lqw;
          }
        };
    PageResult<OperationLog> dataSourceTestPageResult = page.doPagingQuery(pageRequest);
    List<OperationLog> data = dataSourceTestPageResult.getData();
    for(int i = 0; i < data.size(); i++) {
      String operatResult = data.get(i).getOperatResult();
      JSONObject jsonObject =  JSON.parseObject(operatResult);
      Map<String,String> result = (Map<String, String>) jsonObject.get("data");
      String belongStationName = result.get("belongStationName");
      String equipmentType = result.get("equipmentType");
      String hostNumber = result.get("hostNumber");
      String remark = result.get("remark");
      String msg = (String) jsonObject.get("msg");
      String newData=belongStationName+"："+equipmentType+"，主机编号："+hostNumber+"，告警内容：[ "+remark+" ]，"+msg;
      data.get(i).setOperatResult(newData);
    }
    return dataSourceTestPageResult;
  }

  @Override
  public PageResult<OperationLog> bizLog(PageRequest<LogParam> pageRequest) {
    MybatiesPageServiceTemplate<LogParam, OperationLog> page =
        new MybatiesPageServiceTemplate<LogParam, OperationLog>(this.baseMapper) {
          @Override
          protected Wrapper<OperationLog> constructWrapper(LogParam logParam) {
            LambdaQueryWrapper<OperationLog> lqw = new LambdaQueryWrapper<OperationLog>();
            if (logParam == null) {
              lqw.and(lq -> lq.eq(OperationLog::getType, LogType.BizLog));
              return lqw;
            }
            if (StringUtils.isNotBlank(logParam.getUsername())) {
              String keyword = logParam.getUsername();
              lqw.and(
                  lq ->
                      lq.like(OperationLog::getUsername, keyword)
                          .or()
                          .like(OperationLog::getContent, keyword)
                          .or()
                          .like(OperationLog::getParams, keyword)
                          .or()
                          .like(OperationLog::getModule, keyword)
                          .or()
                          .like(OperationLog::getContent, keyword));
            }
            lqw.and(lq -> lq.eq(OperationLog::getType, LogType.BizLog));
            if (StringUtils.isNotBlank(logParam.getStartTime())
                && StringUtils.isNotBlank(logParam.getEndTime())) {
              lqw.between(
                  OperationLog::getCreateTime, logParam.getStartTime(), logParam.getEndTime());
            }
            lqw.orderByDesc(OperationLog::getCreateTime);
            return lqw;
          }
        };
    PageResult<OperationLog> dataSourceTestPageResult = page.doPagingQuery(pageRequest);
    return dataSourceTestPageResult;
  }

  @Override
  public List<OperationLogExportDto> logExport(String type) {
    LambdaQueryWrapper<OperationLog> lqw = new LambdaQueryWrapper<OperationLog>();
    if (type.equals("SysLog")) {
      lqw.and(
          lq ->
              lq.eq(OperationLog::getType, LogType.SysLog)
                  .or()
                  .eq(OperationLog::getType, LogType.AuthLog));
    } else {
      lqw.and(lq -> lq.eq(OperationLog::getType, type));
    }
    lqw.orderByDesc(OperationLog::getCreateTime);

    List<OperationLogExportDto> logDtoList = new ArrayList<OperationLogExportDto>();
    List<OperationLog> list = this.list(lqw);
    for (OperationLog log : list) {
      OperationLogExportDto dto = new OperationLogExportDto();
      SpringBeanUtilsExt.copyProperties(log, dto);
      logDtoList.add(dto);
    }
    return logDtoList;
  }
}
