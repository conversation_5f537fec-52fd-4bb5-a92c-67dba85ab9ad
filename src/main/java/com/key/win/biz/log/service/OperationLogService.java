package com.key.win.biz.log.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.key.win.biz.log.model.OperationLog;
import com.key.win.biz.log.model.OperationLogExportDto;
import com.key.win.biz.log.param.LogParam;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;

/** 操作日子 */
public interface OperationLogService extends IService<OperationLog> {

  PageResult<OperationLog> sysLog(PageRequest<LogParam> pageRequest);

  PageResult<OperationLog> alarmLog(PageRequest<LogParam> pageRequest);

  PageResult<OperationLog> bizLog(PageRequest<LogParam> pageRequest);

  public List<OperationLogExportDto> logExport(String type);
}
