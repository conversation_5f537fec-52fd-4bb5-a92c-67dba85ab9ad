package com.key.win.biz.log.controller;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.excel.EasyExcel;
import com.key.win.biz.log.model.OperationLog;
import com.key.win.biz.log.model.OperationLogExportDto;
import com.key.win.biz.log.param.LogParam;
import com.key.win.biz.log.service.OperationLogService;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;

/** 操作日志 */
@RestController
@RequestMapping("/log")
public class OperationLogController {

  @Autowired private OperationLogService operationLogService;

  /**
   * 系统日志
   *
   * @param pageRequest
   * @return
   */
  @PostMapping("/sysLogInfo")
  protected PageResult<OperationLog> sysLogs(@RequestBody PageRequest<LogParam> pageRequest) {
    return operationLogService.sysLog(pageRequest);
  }

  /**
   * 告警日志
   *
   * @param pageRequest
   * @return
   */
  @PostMapping("/alarmLogInfo")
  protected PageResult<OperationLog> alarmLogs(@RequestBody PageRequest<LogParam> pageRequest) {
    return operationLogService.alarmLog(pageRequest);
  }

  /**
   * 业务日志
   *
   * @param pageRequest
   * @return
   */
  @PostMapping("/bizLogInfo")
  protected PageResult<OperationLog> bizLogs(@RequestBody PageRequest<LogParam> pageRequest) {
    return operationLogService.bizLog(pageRequest);
  }

  /**
   * @param type {SysLog|ALARM|BizLog}
   * @param response
   * @throws UnsupportedEncodingException
   * @throws IOException
   */
  @GetMapping("/export/{type}")
  protected void export(@PathVariable String type, HttpServletResponse response)
      throws UnsupportedEncodingException, IOException {
    String time = new SimpleDateFormat("hh24:mm:ss").format(new Date());
    List<OperationLogExportDto> logData = operationLogService.logExport(type);
    EasyExcel.write(
            getResponse(response, type + "-log-" + time).getOutputStream(),
            OperationLogExportDto.class)
        .sheet("日志列表")
        .doWrite(logData);
  }

  private HttpServletResponse getResponse(HttpServletResponse response, String fileName)
      throws UnsupportedEncodingException {
    response.setContentType("application/vnd.ms-excel");
    response.setCharacterEncoding("utf-8");
    String fname = URLEncoder.encode(fileName, "UTF-8");
    response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fname + ".xlsx");
    return response;
  }
}
