package com.key.win.biz.log.model;

import java.io.Serializable;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

@Data
@ColumnWidth(15)
@HeadRowHeight(30)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@HeadFontStyle(fontHeightInPoints = 11)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OperationLogExportDto implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  @ExcelProperty(value = "操作人")
  private String username;

  @ExcelProperty(value = "日志模块")
  private String module;

  @ExcelProperty(value = "记录参数")
  private String params;

  @ExcelProperty(value = "记录时间")
  private String createTime;

  @ExcelProperty(value = "执行动作")
  private String content;

  @ExcelProperty(value = "操作目标")
  private String operationObject;

  @ExcelProperty(value = "操作内容")
  private String operatResult;

  @ExcelProperty(value = "记录IP")
  private String operatIp;

  @ExcelProperty(value = "日志类型")
  private String type;
}
