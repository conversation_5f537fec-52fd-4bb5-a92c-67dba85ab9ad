package com.key.win.biz.topo.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import lombok.Data;

@Data
@TableName("sf_topo_node")
@Entity(name = "sf_topo_node")
public class TopoNodes {

  @Id @TableId private String id;

  private String pid;

  private String hostNum;

  private int nodeId;

  private int type;

  @Transient
  @TableField(exist = false)
  private String typeName;

  private int x;

  private int y;

  private String name;

  private double loss;

  private String rfidlabel;

  private String probeSid;

  private int state;

  private String lineId;

  private String lineName;

  private String stationId;

  private String stationName;

  private boolean flagTrue = true;

  private String nodeDesc;
}
