package com.key.win.biz.topo.controller;

import com.key.win.biz.statistic.vo.StatisticVO;
import com.key.win.biz.topo.enums.DeviceStatus;
import com.key.win.biz.topo.enums.DeviceType;
import com.key.win.biz.topo.model.TopoLinks;
import com.key.win.biz.topo.model.TopoNodes;
import com.key.win.biz.topo.service.TopoService;
import com.key.win.biz.topo.vo.OTopo;
import com.key.win.biz.topo.vo.OTopoLinks;
import com.key.win.biz.topo.vo.OTopoNodes;
import com.key.win.common.web.Result;
import com.key.win.mqtt.mq.cmd.EmqRequestService;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/topo/*")
public class TopoController {

  @Autowired private TopoService topoService;

  @Autowired private EmqRequestService emqRequestService;

  /**
   * 存储topo图数据
   *
   * @param topoInfo 从前台传递过来的原始topo数据信息
   * @return
   */
  @PostMapping("/save")
  public Result<Boolean> saveTopo(@RequestBody OTopo topoInfo) {

    if (StringUtils.isEmpty(topoInfo.getHostNum())) {
      return Result.failed(false, "监控主机编号不存在,无法新增存储!");
    }

    if (StringUtils.isEmpty(topoInfo.getPid())) {
      return Result.failed(false, "关联的监控主机记录信息不存在,请刷新重试!");
    }

    boolean hostIsOnline = emqRequestService.clientIsOnline("reader_" + topoInfo.getHostNum());

    List<OTopoNodes> onodes = topoInfo.getNodes();
    List<TopoNodes> dnods = new ArrayList<TopoNodes>(onodes.size());
    onodes.forEach(
        x -> {
          TopoNodes node = new TopoNodes();
          node.setPid(topoInfo.getPid());
          node.setHostNum(topoInfo.getHostNum());
          node.setName(x.getName());
          node.setNodeId(x.getId());
          node.setType(x.getType());
          node.setX(x.getX());
          node.setY(x.getY());
          node.setFlagTrue(x.isFlagTrue());
          node.setNodeDesc(x.getNodeDesc());

          // 默认是灰色
          int state = DeviceStatus.gray.getState();
          if (x.getType() == DeviceType.HOST.getDeviceTypeCode()) {
            state = hostIsOnline ? DeviceStatus.green.getState() : DeviceStatus.red.getState();
            node.setState(state);
          } else {
            node.setState(DeviceStatus.gray.getState());
          }
          if (x.getType() != DeviceType.TX_BG.getDeviceTypeCode()
              && x.getType() != DeviceType.TX_BZ.getDeviceTypeCode()
              && x.getType() != DeviceType.TX_DSZQ.getDeviceTypeCode()
              && x.getType() != DeviceType.TX_DXXD.getDeviceTypeCode()
              && x.getType() != DeviceType.TX_QT.getDeviceTypeCode()
              && x.getType() != DeviceType.TX_QXXD.getDeviceTypeCode()) {
            node.setLoss(0);
            node.setRfidlabel("");
            node.setProbeSid("");
          } else {
            if (StringUtils.isNotBlank(x.getProbeSid())) {
              node.setProbeSid(x.getProbeSid());
            }
            node.setLoss(x.getLoss());
          }
          dnods.add(node);
        });

    List<OTopoLinks> olinks = topoInfo.getLinks();
    List<TopoLinks> dlinks = new ArrayList<TopoLinks>(olinks.size());
    olinks.forEach(
        x -> {
          TopoLinks topoLinks = new TopoLinks();
          topoLinks.setPid(topoInfo.getPid());
          topoLinks.setHostNum(topoInfo.getHostNum());
          topoLinks.setDestId(x.getTo());
          topoLinks.setSrcId(x.getFrom());
          topoLinks.setLinktype(x.getLinktype());
          topoLinks.setLinktext(x.getLinktext());
          dlinks.add(topoLinks);
        });

    Boolean flag = topoService.saveOrUpdateTopo(topoInfo.getPid(), dnods, dlinks);
    if (flag) {
      return Result.succeed(flag, "存储成功");
    } else {
      return Result.failed(flag, "存储失败");
    }
  }

  /**
   * 获取topo图数据
   *
   * @param pid 监控主机主键ID
   * @return nodes 集合 以及 links连线集合
   */
  @PostMapping("/get/{pid}")
  public Result<Map> getTopoByPid(@PathVariable String pid) {
    Map<String, Object> result = topoService.getTopo(pid);
    return Result.succeed(result, "success");
  }

  @PostMapping("/statistic/{stationId}")
  public Result<List<StatisticVO>> statisticByStation(@PathVariable("stationId") String stationId) {
    List<StatisticVO> statisticData = topoService.statisticByStation(stationId);
    return Result.succeed(statisticData, "数据获取完毕");
  }
}
