package com.key.win.biz.topo.model;

import javax.persistence.Entity;
import javax.persistence.Id;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

@Data
@Entity(name = "sf_topo_links")
@TableName("sf_topo_links")
public class TopoLinks {

  @Id @TableId private String id;

  private String pid;

  private String hostNum;

  private int linktype;

  private int srcId;

  private int destId;

  private String linktext ;//2024-05-23新添加字段（连接线的文本信息）
}
