package com.key.win.biz.topo.service;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.model.Probe;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.biz.baseInfo.service.ProbeService;
import com.key.win.biz.topo.enums.DeviceStatus;
import com.key.win.biz.topo.enums.DeviceType;
import com.key.win.biz.topo.model.TopoNodes;
import java.util.*;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TopoExtService {

  @Autowired private TopoNodesService topoNodesService;

  @Autowired private BelongUnitService belongUnitService;

  @Autowired private ProbeService probeService;

  /** 从数据库读取所有的topo节点，只读天线类型的 */
  public Map<String, Object> loadAllAntNodeFromDBByAntStatus(String status) {

    if (!status.equals("online") && !status.equals("offline") && !status.equals("unknown")) {
      return new HashMap<>();
    }

    List<BelongUnit> belongUnitList = belongUnitService.list();
    LinkedHashMap<String, BelongUnit> belongUnitMap =
        belongUnitList.stream()
            .collect(
                Collectors.toMap(
                    node -> node.getHostNum(), node -> node, (k1, k2) -> k2, LinkedHashMap::new));
    LinkedHashMap<String, BelongUnit> belongUnitIdMap =
        belongUnitList.stream()
            .collect(
                Collectors.toMap(
                    node -> node.getId(), node -> node, (k1, k2) -> k2, LinkedHashMap::new));

    // 第一步，加载所有的天线：从数据库获取所有的topo节点，天线数据
    LambdaQueryWrapper<TopoNodes> lqwNodes = new LambdaQueryWrapper<>();
    lqwNodes.orderByAsc(TopoNodes::getName);
    lqwNodes.and(
        lq ->
            lq.eq(TopoNodes::getType, DeviceType.TX_BG.getDeviceTypeCode())
                .or()
                .eq(TopoNodes::getType, DeviceType.TX_BZ.getDeviceTypeCode())
                .or()
                .eq(TopoNodes::getType, DeviceType.TX_DSZQ.getDeviceTypeCode())
                .or()
                .eq(TopoNodes::getType, DeviceType.TX_DXXD.getDeviceTypeCode())
                .or()
                .eq(TopoNodes::getType, DeviceType.TX_QT.getDeviceTypeCode())
                .or()
                .eq(TopoNodes::getType, DeviceType.TX_QXXD.getDeviceTypeCode()));
    List<TopoNodes> nodeList = topoNodesService.list(lqwNodes);

    // 第二步，获取所有的探针数据，并将list转为以 探针序号（01.02）为key的map，方便后续天线根据序号从探针中查询是否存在对应探针
    LambdaQueryWrapper<Probe> lqwAnt = new LambdaQueryWrapper<Probe>();
    lqwAnt.orderByAsc(Probe::getNumber);
    List<Probe> ants = probeService.list(lqwAnt);
    LinkedHashMap<String, Probe> probMap =
        ants.stream()
            .collect(
                Collectors.toMap(
                    node -> node.getHostNumber() + ":" + node.getNumber(),
                    node -> node,
                    (k1, k2) -> k2,
                    LinkedHashMap::new));

    // 第三步 匹配数据根据 业务需求进行匹配
    // 该集合下的天线node都是绑定了探针，没有绑定的 则是 未知天线
    List<TopoNodes> onlineNodes = new ArrayList<>(); // online天线
    List<TopoNodes> offlineNodes = new ArrayList<>(); // offline天线
    List<TopoNodes> unknownNodes = new ArrayList<>(); // unknown的天线

    nodeList.forEach(
        x -> {
          String hostNum = x.getHostNum();
          BelongUnit belongUnit = belongUnitMap.get(hostNum);
          boolean hostIsOnline = belongUnit == null || belongUnit.getDeviceStatus().equals("在线");

          String probeSid = x.getProbeSid(); // 探针序号，如 01  02
          Probe probe = MapUtil.get(probMap, hostNum + ":" + probeSid, Probe.class);
          if (probe == null && !hostIsOnline) {
            offlineNodes.add(x);
          } else if (probe == null && hostIsOnline) {
            unknownNodes.add(x);
          } else {
            if (hostIsOnline) {
              if (!StringUtils.isBlank(probe.getProbeStatus())
                  && StringUtils.equals(probe.getProbeStatus(), "01")) {
                x.setState(DeviceStatus.green.getState());
                onlineNodes.add(x);
              } else if (!StringUtils.isBlank(probe.getProbeStatus())
                  && StringUtils.equals(probe.getProbeStatus(), "00")) {
                x.setState(DeviceStatus.red.getState());
                offlineNodes.add(x);
              } else {
                x.setState(DeviceStatus.gray.getState());
                unknownNodes.add(x);
              }
            } else {
              offlineNodes.add(x);
            }
          }
        });

    List<TopoNodes> nodes = null;
    if (status.equals("online")) {
      nodes = onlineNodes;
    } else if (status.equals("offline")) {
      nodes = offlineNodes;
    } else if (status.equals("unknown")) {
      nodes = unknownNodes;
    }

    // 将所有的绑定探针的天线 转为 key为pid，值为 天线node的map
    LinkedHashMap<String, List<TopoNodes>> AntNodeMap =
        nodes.stream()
            .collect(
                Collectors.groupingBy(TopoNodes::getPid, LinkedHashMap::new, Collectors.toList()));

    // 将 真天线 node的pid反向查找监控主机的信息，并将监控主机信息存储在list中
    List<BelongUnit> belongUnits = new ArrayList<>();
    AntNodeMap.keySet()
        .forEach(
            x -> {
              String belongUnitId = x;
              BelongUnit unit = belongUnitIdMap.get(belongUnitId);
              if (unit != null) {
                belongUnits.add(unit);
              }
            });

    Map<String, Object> map = new LinkedHashMap<>();
    map.put("hosts", belongUnits);
    map.put("antMap", AntNodeMap);
    return map;
  }
}
