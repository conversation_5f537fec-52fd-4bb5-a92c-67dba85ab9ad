package com.key.win.biz.topo.service;

import com.key.win.biz.topo.dao.NodeLinkDao;
import com.key.win.biz.topo.vo.NodeLinkVO;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.key.win.biz.topo.dao.TopoLinksDao;
import com.key.win.biz.topo.model.TopoLinks;

@Service
public class TopoLinksService extends ServiceImpl<TopoLinksDao, TopoLinks> {

  @Autowired private NodeLinkDao nodeLinkDao;

  /**
   * 获取天线节点的link链路信息列表
   *
   * @param hostNumber 主机编号
   * @return 天线节点的link信息
   */
  public int getAntNodeLinks(String hostNumber) {
    return nodeLinkDao.getAntNodeLinks(hostNumber);
  }
}
