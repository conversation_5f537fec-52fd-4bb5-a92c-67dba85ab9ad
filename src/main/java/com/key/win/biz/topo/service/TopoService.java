package com.key.win.biz.topo.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.model.Probe;
import com.key.win.biz.baseInfo.model.SiteInfo;
import com.key.win.biz.baseInfo.service.BaseLineService;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.biz.baseInfo.service.ProbeService;
import com.key.win.biz.baseInfo.service.SiteInfoService;
import com.key.win.biz.statistic.dao.TopoStatisticDao;
import com.key.win.biz.statistic.vo.StatisticVO;
import com.key.win.biz.topo.dao.DataManagerDao;
import com.key.win.biz.topo.enums.DeviceStatus;
import com.key.win.biz.topo.enums.DeviceType;
import com.key.win.biz.topo.model.TopoLinks;
import com.key.win.biz.topo.model.TopoNodes;
import com.key.win.mqtt.mq.cmd.EmqRequestService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.transaction.Transactional;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TopoService {

  @Resource private BaseLineService baseLineService;

  @Autowired private EmqRequestService emqRequestService;

  @Autowired private TopoNodesService topoNodesService;

  @Autowired private TopoLinksService topoLinksService;

  @Autowired private BelongUnitService belongUnitService;

  @Autowired private SiteInfoService siteInfoService;

  @Autowired private TopoStatisticDao topoStatisticDao;

  @Autowired private ProbeService probeService;

  @Autowired private DataManagerDao dataManagerDao;

  @Transactional
  public Boolean saveOrUpdateTopo(
      String pid, List<TopoNodes> topoNodes, List<TopoLinks> topoLinks) {

    LambdaQueryWrapper<BelongUnit> lqwUnit = new LambdaQueryWrapper<BelongUnit>();
    lqwUnit.eq(BelongUnit::getId, pid);
    BelongUnit unit = belongUnitService.getOne(lqwUnit);

    for (TopoNodes node : topoNodes) {
      node.setLineId(unit.getLineId());
      node.setLineName(unit.getLineName());
      String nodeName = node.getName();
      boolean notBlank = StringUtils.isNotBlank(nodeName);
      if (notBlank) {
        node.setName(nodeName.trim());
      }
      node.setStationId(unit.getStationId());
      node.setStationName(unit.getStationName());
    }
    try {
      LambdaQueryWrapper<TopoNodes> lqwNodes = new LambdaQueryWrapper<TopoNodes>();
      lqwNodes.eq(TopoNodes::getPid, pid);
      topoNodesService.remove(lqwNodes);

      LambdaQueryWrapper<TopoLinks> lqwLinks = new LambdaQueryWrapper<TopoLinks>();
      lqwLinks.eq(TopoLinks::getPid, pid);
      topoLinksService.remove(lqwLinks);

      topoNodesService.saveBatch(topoNodes);
      topoLinksService.saveBatch(topoLinks);
      return Boolean.TRUE;
    } catch (Exception e) {
      e.printStackTrace();
      return Boolean.FALSE;
    }
  }

  /**
   * 根据设备编号查询当前设备下的所有天线节点
   *
   * @param hostNumber 监控主机编号
   * @param isTest 是否为测试
   */
  public List<TopoNodes> getAntNodesByHostNum(String hostNumber, boolean isTest) {
    boolean clientIsOnline = false;
    if (isTest) {
      clientIsOnline = isTest;
    } else {
      clientIsOnline = emqRequestService.clientIsOnline("reader_" + hostNumber);
    }
    LambdaQueryWrapper<TopoNodes> lqwNodes = new LambdaQueryWrapper<TopoNodes>();
    lqwNodes.eq(TopoNodes::getHostNum, hostNumber);
    lqwNodes.and(
        lq ->
            lq.eq(TopoNodes::getType, DeviceType.TX_BG.getDeviceTypeCode())
                .or()
                .eq(TopoNodes::getType, DeviceType.TX_BZ.getDeviceTypeCode())
                .or()
                .eq(TopoNodes::getType, DeviceType.TX_DSZQ.getDeviceTypeCode())
                .or()
                .eq(TopoNodes::getType, DeviceType.TX_DXXD.getDeviceTypeCode())
                .or()
                .eq(TopoNodes::getType, DeviceType.TX_QT.getDeviceTypeCode())
                .or()
                .eq(TopoNodes::getType, DeviceType.TX_QXXD.getDeviceTypeCode()));
    lqwNodes.orderByAsc(TopoNodes::getProbeSid);
    List<TopoNodes> antNodesList = topoNodesService.list(lqwNodes);

    LambdaQueryWrapper<Probe> lqwAnt = new LambdaQueryWrapper<Probe>();
    lqwAnt.eq(Probe::getHostNumber, hostNumber);
    List<Probe> probeList = probeService.list(lqwAnt);
    Map<String, Probe> antMap =
        probeList.stream().collect(Collectors.toMap(Probe::getNumber, v -> v));

    for (TopoNodes node : antNodesList) {
      node.setTypeName(deviceType(node.getType()));
      if (!clientIsOnline) {
        node.setState(DeviceStatus.gray.getState());
        node.setLoss(120);
        for (Probe ant : probeList) {
          if (StringUtils.isNotBlank(node.getProbeSid())
              && node.getProbeSid().equals(ant.getNumber())) {
            node.setRfidlabel(ant.getOCoding());
            break;
          }
        }
      } else {
        if (StringUtils.isNotBlank(node.getProbeSid())) {
          Probe probe = antMap.get(node.getProbeSid());
          if (probe != null) {
            node.setRfidlabel(probe.getCoding());
            node.setLoss(probe.getLost());
            String probeStatus = StringUtils.defaultString(probe.getProbeStatus(), "");
            boolean flag = StringUtils.isNoneBlank(probeStatus);

            if (flag && probeStatus.equals("00")) {
              node.setState(DeviceStatus.red.getState());
            } else if (flag && probeStatus.equals("01")) {
              node.setState(DeviceStatus.green.getState());
            } else {
              node.setState(DeviceStatus.gray.getState());
            }
          } else {
            node.setState(DeviceStatus.gray.getState());
            node.setLoss(120);
            node.setRfidlabel("-");
          }
        }
      }
    }
    return antNodesList;
  }

  public Map<String, Object> getTopo(String pid) {
    BelongUnit host = belongUnitService.getById(pid);
    if (ObjectUtil.isEmpty(host)) {
      HashMap<String, Object> hashMap = new HashMap<String, Object>();
      hashMap.put("nodes", new ArrayList<>());
      hashMap.put("links", new ArrayList<>());
      return hashMap;
    }

    LambdaQueryWrapper<TopoNodes> lqwNodes = new LambdaQueryWrapper<TopoNodes>();
    lqwNodes.eq(TopoNodes::getHostNum, host.getHostNum());
    List<TopoNodes> nodes = topoNodesService.list(lqwNodes);

    if (nodes.size() == 0) {
      Map<String, Object> result = new HashMap<String, Object>();
      result.put("nodes", new ArrayList<TopoNodes>());
      result.put("links", new ArrayList<TopoLinks>());
      return result;
    }

    LambdaQueryWrapper<Probe> lqwAnt = new LambdaQueryWrapper<Probe>();
    lqwAnt.eq(Probe::getHostNumber, host.getHostNum());
    List<Probe> probeList = probeService.list(lqwAnt);
    boolean clientIsOnline = false;
    for (TopoNodes node : nodes) {
      if (node.getType() == DeviceType.HOST.getDeviceTypeCode()) {
        String hostNumber = node.getHostNum();
        clientIsOnline = emqRequestService.clientIsOnline("reader_" + hostNumber);
        if (!clientIsOnline) {
          for (TopoNodes n : nodes) {
            n.setState(DeviceStatus.gray.getState());
          }
        } else {
          node.setState(DeviceStatus.green.getState());
        }
        break;
      }
    }

    for (Probe ant : probeList) {
      for (TopoNodes node : nodes) {
        boolean matched = false;
        if (StringUtils.isNotBlank(node.getProbeSid())
            && node.getProbeSid().equals(ant.getNumber())) {
          node.setRfidlabel(ant.getCoding());
          matched = true;
        }
        if (matched && clientIsOnline) {
          String probeStatus = StringUtils.defaultString(ant.getProbeStatus(), "");
          if (probeStatus.equals("00")) {
            node.setState(DeviceStatus.red.getState());
          } else if (probeStatus.equals("01")) {
            node.setState(DeviceStatus.green.getState());
          } else {
            node.setState(DeviceStatus.gray.getState());
          }
          break;
        }
      }
    }

    LambdaQueryWrapper<TopoLinks> lqwLinks = new LambdaQueryWrapper<TopoLinks>();
    lqwLinks.eq(TopoLinks::getHostNum, host.getHostNum());
    List<TopoLinks> links = topoLinksService.list(lqwLinks);

    Map<String, Object> result = new HashMap<String, Object>();
    result.put("nodes", nodes);
    result.put("links", links);

    return result;
  }

  /**
   * 根据车站统计资产信息
   *
   * @param stationId
   */
  public List<StatisticVO> statisticByStation(String stationId) {
    List<StatisticVO> topoStatistic = topoStatisticDao.topoStatistic(stationId);
    return topoStatistic;
  }

  /**
   * 根据监控主机编号以及topo的name修改当前node的state值 此方法主要提供给 天线故障诊断后，将天线状态 00 01 同步到当前节点的state上 00 故障为红色 01
   * 正常为绿色
   *
   * @param hostNumber 主键编码
   * @param probeSid 探针序号，探针对应的序号如 01 | 02 |...
   * @param state 当前节点的状态
   * @return 是否修改成功
   */
  public boolean updateNodeStatusByHostNumberAndProbeSid(
      String hostNumber, String probeSid, int state) {
    LambdaQueryWrapper<TopoNodes> lqw = new LambdaQueryWrapper<>();
    lqw.eq(TopoNodes::getHostNum, hostNumber);
    lqw.eq(TopoNodes::getProbeSid, probeSid);
    lqw.and(
        lq ->
            lq.eq(TopoNodes::getType, DeviceType.TX_BG.getDeviceTypeCode())
                .or()
                .eq(TopoNodes::getType, DeviceType.TX_BZ.getDeviceTypeCode())
                .or()
                .eq(TopoNodes::getType, DeviceType.TX_DSZQ.getDeviceTypeCode())
                .or()
                .eq(TopoNodes::getType, DeviceType.TX_DXXD.getDeviceTypeCode())
                .or()
                .eq(TopoNodes::getType, DeviceType.TX_QT.getDeviceTypeCode())
                .or()
                .eq(TopoNodes::getType, DeviceType.TX_QXXD.getDeviceTypeCode()));
    // 此时能获取一个天线node节点
    TopoNodes antNode = topoNodesService.getOne(lqw, false);

    if (antNode != null && antNode.getState() != state) {
      antNode.setState(state);
      return topoNodesService.updateById(antNode);
    }
    return false;
  }

  /**
   * 根据监控主机编号获取天线节点的所有link
   *
   * @param hostNumber 监控主机编号
   * @return 天线异常个数
   */
  public int getAntNodeLinks(String hostNumber) {
    return topoLinksService.getAntNodeLinks(hostNumber);
  }

  /**
   * 根据监控主机编号和节点name查询节点信息
   *
   * @param hostNumber 监控主机编号
   * @param probeSid 探针序号
   * @return 当前节点
   */
  public TopoNodes getTopoNodesByHostNumberAndProbeSid(String hostNumber, String probeSid) {
    LambdaQueryWrapper<TopoNodes> lqw = new LambdaQueryWrapper<>();
    lqw.eq(TopoNodes::getProbeSid, probeSid);
    lqw.eq(TopoNodes::getHostNum, hostNumber);
    TopoNodes node = topoNodesService.getOne(lqw, false);
    return node;
  }

  /**
   * 导出监控主机下的Topo图
   *
   * @param pid 监控主机的主键ID
   * @return 结果Map
   */
  public Map<String, Object> exportTopo(String pid) {
    BelongUnit host = belongUnitService.getById(pid);
    if (ObjectUtil.isEmpty(host)) {
      HashMap<String, Object> hashMap = new LinkedHashMap<String, Object>();
      hashMap.put("hostNumber", host.getHostNum());
      hashMap.put("desc", "请勿乱修改改文件内容");
      hashMap.put("nodes", new ArrayList<>());
      hashMap.put("links", new ArrayList<>());
      return hashMap;
    }

    LambdaQueryWrapper<TopoNodes> lqwNodes = new LambdaQueryWrapper<TopoNodes>();
    lqwNodes.eq(TopoNodes::getHostNum, host.getHostNum());
    List<TopoNodes> nodes = topoNodesService.list(lqwNodes);

    if (nodes.size() == 0) {
      Map<String, Object> result = new LinkedHashMap<String, Object>();
      result.put("hostNumber", host.getHostNum());
      result.put("desc", "请勿乱修改改文件内容");
      result.put("nodes", new ArrayList<TopoNodes>());
      result.put("links", new ArrayList<TopoLinks>());
      return result;
    }

    // 处理所有的topo节点
    for (TopoNodes node : nodes) {
      node.setState(DeviceStatus.gray.getState());
      node.setHostNum("");
      node.setId("");
      node.setPid("");
      node.setRfidlabel("");
      node.setStationName("");
      node.setStationId("");
      node.setLineName("");
      node.setLineId("");
    }

    // 处理所有的连线信息
    LambdaQueryWrapper<TopoLinks> lqwLinks = new LambdaQueryWrapper<TopoLinks>();
    lqwLinks.eq(TopoLinks::getHostNum, host.getHostNum());
    List<TopoLinks> links = topoLinksService.list(lqwLinks);
    for (TopoLinks link : links) {
      link.setHostNum("");
      link.setPid("");
      link.setId("");
    }

    LinkedHashMap<String, Object> result = new LinkedHashMap<String, Object>();
    result.put("hostNumber", host.getHostNum());
    result.put("desc", "请勿乱修改改文件内容");
    result.put("nodes", nodes);
    result.put("links", links);
    return result;
  }

  /**
   * 导入Topo图
   *
   * @param pid 主机主键ID
   * @param map topo模板json文件映射后生成的map
   */
  public void importTopo(String pid, Map map) {
    BelongUnit host = belongUnitService.getById(pid);
    if (host != null) {
      String lineName = host.getLineName();
      String lineId = host.getLineId();
      String id = host.getId();
      String hostNum = host.getHostNum();
      String stationName = host.getStationName();
      String stationId = host.getStationId();

      Object nodesObject = map.get("nodes");
      String nodesStr = JSON.toJSONString(nodesObject);
      List<TopoNodes> topoNodes = JSONArray.parseArray(nodesStr, TopoNodes.class);
      for (TopoNodes node : topoNodes) {
        node.setHostNum(hostNum);
        if (node.getType() == DeviceType.HOST.getDeviceTypeCode()) {
          node.setName(hostNum + "监控主机");
        }
        node.setLineId(lineId);
        node.setLineName(lineName);
        node.setStationId(stationId);
        node.setStationName(stationName);
        node.setPid(id);
      }

      Object linksObject = map.get("links");
      String linksStr = JSON.toJSONString(linksObject);
      List<TopoLinks> topoLinks = JSONArray.parseArray(linksStr, TopoLinks.class);
      for (TopoLinks link : topoLinks) {
        link.setPid(id);
        link.setHostNum(hostNum);
      }
      LambdaQueryWrapper<TopoNodes> lqwNodes = new LambdaQueryWrapper<TopoNodes>();
      lqwNodes.eq(TopoNodes::getPid, pid);
      topoNodesService.remove(lqwNodes);

      LambdaQueryWrapper<TopoLinks> lqwLinks = new LambdaQueryWrapper<TopoLinks>();
      lqwLinks.eq(TopoLinks::getPid, pid);
      topoLinksService.remove(lqwLinks);

      topoNodesService.saveBatch(topoNodes);
      topoLinksService.saveBatch(topoLinks);
    }
  }

  /** 导出室分站点、监控主机、链路、节点 */
  public Map<String, Object> exportTopo() {

    List<String> lineCodeList = baseLineService.operationLineList();
    Map<String, Object> result = null;
    if (lineCodeList.size() != 0) {
      LambdaQueryWrapper<SiteInfo> lqwSiteInfo = new LambdaQueryWrapper<>();
      lqwSiteInfo.in(SiteInfo::getLineCode, lineCodeList);
      List<SiteInfo> siteList = siteInfoService.list(); // 室分站点
      List<BelongUnit> hostList = belongUnitService.list(); // 监控主机列表
      List<TopoLinks> links = topoLinksService.list(); // 连线
      List<TopoNodes> nodes = topoNodesService.list(); // 节点

      result = new HashMap<>();
      result.put("sites", siteList);
      result.put("hosts", hostList);
      result.put("links", links);
      result.put("nodes", nodes);
    } else {
      result = new HashMap<>();
      result.put("sites", new ArrayList<>());
      result.put("hosts", new ArrayList<>());
      result.put("links", new ArrayList<>());
      result.put("nodes", new ArrayList<>());
    }
    return result;
  }

  @Transactional
  public boolean importTopo(Map map) {
    Object sitesObject = map.get("sites");
    String sitesStr = JSON.toJSONString(sitesObject);
    List<SiteInfo> sites = JSONArray.parseArray(sitesStr, SiteInfo.class);

    Object hostsObject = map.get("hosts");
    String hostsStr = JSON.toJSONString(hostsObject);
    List<BelongUnit> hosts = JSONArray.parseArray(hostsStr, BelongUnit.class);

    Object linksObject = map.get("links");
    String linksStr = JSON.toJSONString(linksObject);
    List<TopoLinks> links = JSONArray.parseArray(linksStr, TopoLinks.class);

    Object nodesObject = map.get("nodes");
    String nodesStr = JSON.toJSONString(nodesObject);
    List<TopoNodes> nodes = JSONArray.parseArray(nodesStr, TopoNodes.class);

    dataManagerDao.clearDataSites();
    dataManagerDao.clearDataBelongUnit();
    dataManagerDao.clearDataLinks();
    dataManagerDao.clearDataNodes();

    boolean a = siteInfoService.saveBatch(sites);
    boolean b = belongUnitService.saveBatch(hosts);
    boolean c = topoLinksService.saveBatch(links);
    boolean d = topoNodesService.saveBatch(nodes);

    if (a & b & c & d) {
      return true;
    } else {
      throw new RuntimeException("导入过程出现异常,尝试回滚操作.");
    }
  }

  public boolean antFlagExecute(TopoNodes topoNode) {
    if (topoNode == null) {
      return false;
    }
    TopoNodes dbNode = topoNodesService.getById(topoNode.getId());
    if (dbNode == null) {
      return false;
    }
    boolean enabled = topoNode.isFlagTrue();
    if (enabled) {
      dbNode.setFlagTrue(true); // 开启使能,真实数据信息
    } else {
      dbNode.setFlagTrue(false);
    }
    return topoNodesService.updateById(dbNode);
  }

  /**
   * 根据hostNumber 和 probeSid 获取node节点
   *
   * @param hostNumber
   * @param probeSid
   * @return
   */
  public TopoNodes getAntFlag(String hostNumber, String probeSid) {
    LambdaQueryWrapper<TopoNodes> lqw = new LambdaQueryWrapper<>();
    lqw.eq(TopoNodes::getHostNum, hostNumber);
    lqw.eq(TopoNodes::getProbeSid, probeSid);
    return topoNodesService.getOne(lqw);
  }

  private String deviceType(int type) {
    DeviceType[] values = DeviceType.values();
    for (DeviceType dt : values) {
      if (dt.getDeviceTypeCode() == type) {
        return dt.getDeviceTypeName();
      }
    }
    return "--";
  }
}
