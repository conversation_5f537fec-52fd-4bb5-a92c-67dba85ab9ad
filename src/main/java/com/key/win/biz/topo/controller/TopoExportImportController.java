package com.key.win.biz.topo.controller;

import cn.hutool.core.date.DateUtil;
import com.key.win.biz.topo.service.TopoService;
import com.key.win.common.web.Result;
import com.key.win.utils.JsonUtil;
import java.io.IOException;
import java.util.Date;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

@Controller
@RequestMapping("/topo/*")
public class TopoExportImportController {

  @Autowired private TopoService topoService;

  /**
   * 导出监控主机的链路Topo图
   *
   * @param pid 监控主机的主键表ID
   */
  @GetMapping("/exportTopo/{pid}")
  public void exportTopo(HttpServletResponse response, @PathVariable String pid) {
    Map<String, Object> topoResult = topoService.exportTopo(pid);
    JsonUtil.exportJson(
        response, topoResult, "TopoTemplate-" + topoResult.get("hostNumber").toString() + ".json");
  }

  @PostMapping("/importTopo/{pid}")
  @ResponseBody
  public Result<String> importTopo(
      @RequestParam("file") MultipartFile file, @PathVariable String pid) throws IOException {
    Map<String, String> map = JsonUtil.readJson(file);
    if (map == null) {
      return Result.failed("文件必须.json文件");
    } else {
      topoService.importTopo(pid, map);
      return Result.succeed("导入成功");
    }
  }

  /** 导出所有的站点、主机、topo链路、node节点 */
  @PostMapping("/export/all")
  public void exportAll(HttpServletResponse response) {
    Map<String, Object> topoResult = topoService.exportTopo();
    String now = DateUtil.now();
    now = now.replace("-", "").replace(":", "").replace(" ", "");
    JsonUtil.exportJson(response, topoResult, "Topo图信息导出-" + now + ".json");
  }

  /** 导入所有的站点、主机、topo链路、node节点 */
  @PostMapping("/import/all")
  @ResponseBody
  public Result<String> importAll(@RequestParam("file") MultipartFile file) throws IOException {
    Map<String, String> map = JsonUtil.readJson(file);
    if (map == null) {
      return Result.failed("ERROR", "文件必须.json文件");
    } else {
      topoService.importTopo(map);
      return Result.succeed("SUCCESS", "导入成功");
    }
  }
}
