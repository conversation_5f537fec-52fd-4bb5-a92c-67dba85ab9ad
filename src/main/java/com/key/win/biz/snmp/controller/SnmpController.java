package com.key.win.biz.snmp.controller;

import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.snmp4j.CommunityTarget;
import org.snmp4j.PDU;
import org.snmp4j.Snmp;
import org.snmp4j.TransportMapping;
import org.snmp4j.event.ResponseEvent;
import org.snmp4j.smi.OID;
import org.snmp4j.smi.UdpAddress;
import org.snmp4j.smi.VariableBinding;
import org.snmp4j.transport.DefaultUdpTransportMapping;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.key.win.config.snmp.SnmpTargetConfig;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/snmp/*")
public class SnmpController {

  @Autowired private PDU pduRequest;

  @PostMapping("/monitor")
  public Object snmpTargetList() throws Exception {
    Map<String, Map<String, Object>> resultMap = new LinkedHashMap<String, Map<String, Object>>();

    Map<String, CommunityTarget> targetMap = SnmpTargetConfig.getTargetMap();
    List<String> oids = SnmpTargetConfig.getOids();

    targetMap.forEach(
        (x, y) -> {
          Map<String, Object> result = new LinkedHashMap<>();
          try {
            TransportMapping<UdpAddress> transport = null;
            transport = new DefaultUdpTransportMapping();
            transport.listen();
            Snmp protocol = new Snmp(transport);
            ResponseEvent responseEvent = protocol.send(pduRequest, y);
            PDU response = responseEvent.getResponse();
            if (response != null) {
              VariableBinding[] array = response.toArray();
              for (VariableBinding vb : array) {
                OID oid = vb.getOid();
                String value = vb.toValueString();
                int val = 0;
                if (value.equals("1")) {
                  val = 1;
                } else if (value.equals("2")) {
                  val = 0;
                } else {
                  val = 0;
                }
                result.put(oid.format(), val);
              }
              transport.close();
            } else {
              for (String oid : oids) {
                result.put(oid, 0);
              }
            }
            resultMap.put(x, result);
          } catch (IOException e) {
            log.error(e.getMessage());
            e.printStackTrace();
          }
        });
    return resultMap;
  }
}
