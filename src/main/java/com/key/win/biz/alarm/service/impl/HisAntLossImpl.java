package com.key.win.biz.alarm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.key.win.biz.alarm.dao.HisAntLossDao;
import com.key.win.biz.alarm.model.AntLossExportDto;
import com.key.win.biz.alarm.model.HisAntLoss;
import com.key.win.biz.alarm.model.HisAntLossChartDto;
import com.key.win.biz.alarm.service.HisAntLossSevice;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.utils.SpringBeanUtilsExt;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class HisAntLossImpl extends ServiceImpl<HisAntLossDao, HisAntLoss> implements HisAntLossSevice {

  @Override
  public HisAntLossChartDto getChartListByHostNum(HisAntLoss param)  {
      HisAntLossChartDto dto = new HisAntLossChartDto();
      LambdaQueryWrapper<HisAntLoss> queryWrapper = new LambdaQueryWrapper<HisAntLoss>();
      queryWrapper.eq(HisAntLoss::getAntName, param.getAntName())
              .eq(HisAntLoss::getHostNumber, param.getHostNumber())
              .eq(HisAntLoss::getIsDeleted, 0)
              .orderByAsc(HisAntLoss::getUpdateTime);
      List<HisAntLoss> antLossList = baseMapper.selectList(queryWrapper);
      if(CollectionUtil.isNotEmpty(antLossList)){
          List<String> lossList = antLossList.stream()
                  .map(HisAntLoss::getLoss)
                  .collect(Collectors.toList());

          List<String> dateList = antLossList.stream()
                  .map(HisAntLoss::getUpdateTime)
                  .map(dateString -> dateString.replaceAll(":\\d{2}$", ""))
                  .collect(Collectors.toList());

          dto.setDate(dateList);
          dto.setLoss(lossList);
      }
      return dto;
  }

  @Override
  public PageResult<HisAntLoss> getPageList(PageRequest<HisAntLoss> pageRequest) {
      PageResult<HisAntLoss> pageResult = new PageResult<HisAntLoss>();
      Integer pageSize = pageRequest.getPageSize();
      Integer offset =  (pageRequest.getPageNo() - 1) * pageSize ;// 查询偏移量
      if(offset < 0){
          offset = 0;
      }
      //分组分页查询记录
      List<HisAntLoss> list = baseMapper.selectLatestHisLossByPage(pageRequest.getT(),offset,pageSize);
      //查询总记录数
      Integer totalCount =  baseMapper.countHisLoss(pageRequest.getT());
      pageResult.setData(list);
      pageResult.setCount(totalCount);
      return pageResult;
  }

  /**
   * 保存天线路损值
   * @param antName
   * @param loss
   * @return
   */
  @Override
  @Transactional
  public int saveHisAntLoss(String hostNumber,String lineId,String lineName,String statioId,String stationName,String antName,String probeId,String probeSid,String loss){
      int result =0;
      LambdaQueryWrapper<HisAntLoss> queryWrapper = new LambdaQueryWrapper<HisAntLoss>();
      queryWrapper.eq(HisAntLoss::getAntName, antName)
                  .eq(HisAntLoss::getHostNumber, hostNumber)
                  .eq(HisAntLoss::getIsDeleted, 0)
                  .orderByAsc(HisAntLoss::getUpdateTime);
      List<HisAntLoss> lossList = baseMapper.selectList(queryWrapper);
      //如果天线历史路损记录超过10条，则更新处理
      if(ObjectUtil.isNotEmpty(lossList) && lossList.size() >10) {
          //获取最旧的记录-更新替换（只保留最近的10条路损信息）
          HisAntLoss bean = lossList.get(0);
          bean.setProbeId(probeId);
          bean.setProbeSid(probeSid);
          bean.setLoss(loss);
          bean.setBelongLineId(lineId);
          bean.setBelongLineName(lineName);
          bean.setBelongStationId(statioId);
          bean.setBelongStationName(stationName);
          bean.setUpdateTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
          result = baseMapper.updateById(bean);
      }else{
          HisAntLoss newBean = new HisAntLoss();
          newBean.setAntName(antName);
          newBean.setProbeId(probeId);
          newBean.setProbeSid(probeSid);
          newBean.setLoss(loss);
          newBean.setBelongLineId(lineId);
          newBean.setBelongLineName(lineName);
          newBean.setBelongStationId(statioId);
          newBean.setBelongStationName(stationName);
          newBean.setEquipmentType("天线");
          newBean.setHostNumber(hostNumber);
          newBean.setCreateBy("system");
          newBean.setIsDeleted(0);
          newBean.setUpdateTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
          result = baseMapper.insert(newBean);
      }
      return result;
  }

    /**
     * 导出历史路损记录
     * @param response
     * @throws IOException
     */
  public void exportHisAntLoss(HttpServletResponse response) throws IOException {
        EasyExcel.write(response.getOutputStream(), AntLossExportDto.class)
                .sheet("历史线损列表")
                .doWrite(hisAntLossDataList());
  }

  private List<AntLossExportDto> hisAntLossDataList() {
        LambdaQueryWrapper<HisAntLoss> lqw = new LambdaQueryWrapper<HisAntLoss>();
        lqw.orderByDesc(HisAntLoss::getUpdateTime);
        lqw.orderByAsc(HisAntLoss::getProbeSid);
        List<AntLossExportDto> excelList = new ArrayList<AntLossExportDto>();
        List<HisAntLoss> list  = baseMapper.selectList(lqw);
        for (HisAntLoss w : list) {
            AntLossExportDto excelDto = new AntLossExportDto();
            SpringBeanUtilsExt.copyProperties(w, excelDto);
            excelList.add(excelDto);
        }
        return excelList;
  }
}
