package com.key.win.biz.alarm.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;


@Data
@ColumnWidth(15)
@HeadRowHeight(30)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@HeadFontStyle(fontHeightInPoints = 11)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AntLossExportDto implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  @ExcelProperty(value = "监控主机编号")
  private String hostNumber;

  @ExcelProperty(value = "线路名称")
  private String belongLineName;

  @ExcelProperty(value = "车站名称")
  private String belongStationName;

  @ExcelProperty(value = "设备类型")
  private String equipmentType;

  @ExcelProperty(value = "网元名称")
  private String antName;

  @ExcelProperty(value = "探针编号")
  private String probeSid;

  @ExcelProperty(value = "探针序号")
  private String probeId;

  @ExcelProperty(value = "路损值")
  private String loss;

  @ExcelProperty(value = "记录时间")
  private String updateTime;

//  @ExcelProperty(value = "操作人")
//  private String createBy;

}
