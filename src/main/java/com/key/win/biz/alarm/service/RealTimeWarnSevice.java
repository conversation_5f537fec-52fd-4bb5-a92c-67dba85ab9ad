package com.key.win.biz.alarm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.key.win.biz.alarm.model.RealTimeWarn;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;

public interface RealTimeWarnSevice extends IService<RealTimeWarn> {

  PageResult<RealTimeWarn> getPageWarnConditions(PageRequest<RealTimeWarn> pageRequest);

  Result makeSuerWarn(String id);

  Result clearWarnById(String id);

  PageResult<RealTimeWarn> pageWarnRealTimeWarnWithStationId(PageRequest<RealTimeWarn> pageRequest);
}
