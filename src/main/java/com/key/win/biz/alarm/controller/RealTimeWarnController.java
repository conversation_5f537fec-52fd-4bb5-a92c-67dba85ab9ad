package com.key.win.biz.alarm.controller;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.key.win.biz.alarm.model.RealTimeWarn;
import com.key.win.biz.alarm.service.RealTimeWarnSevice;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import com.key.win.system.aop.annotation.LogAnnotation;
import com.key.win.system.aop.annotation.LogType;
import com.key.win.system.aop.annotation.SfLog;

@RestController
@RequestMapping("/real/time/warn")
public class RealTimeWarnController {

  @Resource private RealTimeWarnSevice realTimeWarnSevice;

  @PostMapping("/queryPageInfo")
  public PageResult<RealTimeWarn> pageWarnRealTimeWarn(
      @RequestBody PageRequest<RealTimeWarn> pageRequest) {
    return realTimeWarnSevice.getPageWarnConditions(pageRequest);
  }

  @PostMapping("/queryPageInfo/belongStationId")
  public PageResult<RealTimeWarn> pageWarnRealTimeWarnWithStationId(
      @RequestBody PageRequest<RealTimeWarn> pageRequest) {
    return realTimeWarnSevice.pageWarnRealTimeWarnWithStationId(pageRequest);
  }

  @SfLog(module = "实时告警信息", actionName = "告警确认", logType = LogType.Alarm)
  @PostMapping("/makeSuerById/{id}")
  public Result makeSuerWarn(@PathVariable("id") String id) {
    return realTimeWarnSevice.makeSuerWarn(id);
  }

  @SfLog(module = "实时告警信息", actionName = "告警清除", logType = LogType.Alarm)
  @PostMapping("/clearWarnById/{id}")
  public Result clearWarn(@PathVariable("id") String id) {
    return realTimeWarnSevice.clearWarnById(id);
  }
}
