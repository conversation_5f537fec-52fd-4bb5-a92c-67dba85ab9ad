package com.key.win.biz.alarm.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.key.win.biz.alarm.model.HisAntLoss;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface HisAntLossDao extends BaseMapper<HisAntLoss> {

     List<HisAntLoss> selectLatestHisLossByPage(@Param("req") HisAntLoss req,@Param("pageNo") Integer pageNo,@Param("pageSize") Integer pageSize);

     Integer countHisLoss(@Param("req") HisAntLoss req);
}
