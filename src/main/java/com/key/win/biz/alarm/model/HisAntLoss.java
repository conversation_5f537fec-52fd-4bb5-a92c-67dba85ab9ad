package com.key.win.biz.alarm.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.key.win.common.web.MybatisID;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Transient;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(callSuper = true)
@TableName("his_ant_loss")
@Entity(name = "his_ant_loss")
public class HisAntLoss extends MybatisID {

  @TableField(exist = false)
  @Transient
  private String startTime;

  @TableField(exist = false)
  @Transient
  private String endTime;

  private String equipmentType;//'设备类型'

  private String antName;//'天线名称'

  private String probeId;//探针序号

  private String probeSid; //探针编号

  private String belongLineId; //线路Id

  private String belongLineName;//线路名称

  private String belongStationId;//'车站ID编码'

  private String belongStationName;//'车站名称'

  private String hostNumber; //'设备主机编码'

  private String loss; //'路损值'

  private String  remark ;//'备注'

  private String attr1;//'扩展字段1'

  private String attr2; //'扩展字段2'

  private String createBy; //创建人

  private String createTime; //创建时间

  private String updateTime; //更新时间

  private Integer isDeleted; //是否逻辑删除

}
