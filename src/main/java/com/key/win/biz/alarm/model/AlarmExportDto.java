package com.key.win.biz.alarm.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import lombok.Data;
/**
 * EasyExcel 参考贴 https://blog.csdn.net/qq_45678613/article/details/120393722
 *
 * <AUTHOR>
 */
@Data
@ColumnWidth(15)
@HeadRowHeight(30)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@HeadFontStyle(fontHeightInPoints = 11)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlarmExportDto implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  @ExcelProperty(value = "车站名称")
  private String belongStationName;

  @ExcelProperty(value = "告警名称")
  private String alarmName;

  @ExcelProperty(value = "网元名称")
  private String networkName;

  @ExcelProperty(value = "监控主机编号")
  private String hostNumber;

  @ExcelProperty(value = "设备类型")
  private String equipmentType;

  @ExcelIgnore private String alarmGrade;

  @ExcelProperty(value = "告警类型")
  private String alarmType;

  @ExcelProperty(value = "告警次数")
  private long alarmTimes;

  @ExcelProperty(value = "告警时间")
  private String alarmTime;

  @ExcelProperty(value = "操作时间")
  private String operationTime;

  @ExcelProperty(value = "操作人")
  private String operationPeople;

  /** 告警状态 - 0 实时告警，1 确认告警，2 清除告警（历史告警） */
  @ExcelProperty(value = "告警状态")
  private String alarmStatus;
}
