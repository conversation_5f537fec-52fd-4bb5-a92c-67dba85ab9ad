package com.key.win.biz.alarm.controller;

import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/check/host")
public class CheckHostListController {

  @Resource private BelongUnitService belongUnitService;

  @PostMapping("/queryPageInfo")
  public PageResult<BelongUnit> pageCheckHostListPage(
      @RequestBody PageRequest<BelongUnit> pageRequest) {
    return belongUnitService.getPageWarnConditions(pageRequest);
  }
}
