package com.key.win.biz.alarm.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.key.win.biz.alarm.dao.RealTimeWarnDao;
import com.key.win.biz.alarm.model.RealTimeWarn;
import com.key.win.biz.alarm.service.RealTimeWarnSevice;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.common.web.MybatiesPageServiceTemplate;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import com.key.win.utils.DateUtils;
import com.key.win.utils.SpringSecurityUtils;

@Service
public class RealTimeWarnImpl extends ServiceImpl<RealTimeWarnDao, RealTimeWarn>
    implements RealTimeWarnSevice {

  @Autowired private BelongUnitService belongUnitService;

  @Override
  public PageResult<RealTimeWarn> getPageWarnConditions(PageRequest<RealTimeWarn> pageRequest) {
    MybatiesPageServiceTemplate<RealTimeWarn, RealTimeWarn> page =
        new MybatiesPageServiceTemplate<RealTimeWarn, RealTimeWarn>(this.baseMapper) {
          @Override
          protected Wrapper<RealTimeWarn> constructWrapper(RealTimeWarn realTimeWarn) {
            LambdaQueryWrapper<RealTimeWarn> lqw = new LambdaQueryWrapper<RealTimeWarn>();
            lqw.eq(RealTimeWarn::getAlarmStatus, "0");
            if (realTimeWarn == null) {
              return lqw;
            }
            if (StringUtils.isNotBlank(realTimeWarn.getAlarmStatus())) {
              lqw.eq(RealTimeWarn::getAlarmStatus, realTimeWarn.getAlarmStatus());
            }
            if (StringUtils.isNotBlank(realTimeWarn.getAlarmGrade())) {
              lqw.eq(RealTimeWarn::getAlarmGrade, realTimeWarn.getAlarmGrade());
            }
            if (StringUtils.isNotBlank(realTimeWarn.getAlarmName())) {
              lqw.like(RealTimeWarn::getAlarmName, realTimeWarn.getAlarmName());
            }
            if (StringUtils.isNotBlank(realTimeWarn.getBelongStationName())) {
              lqw.and(
                  lq ->
                      lq.like(
                              RealTimeWarn::getBelongStationName,
                              realTimeWarn.getBelongStationName())
                          .or()
                          .like(
                              RealTimeWarn::getBelongStationId,
                              realTimeWarn.getBelongStationName()));
            }
            if (StringUtils.isNotBlank(realTimeWarn.getHostNumber())) {
              lqw.like(RealTimeWarn::getHostNumber, realTimeWarn.getHostNumber());
            }
            if (StringUtils.isNotBlank(realTimeWarn.getAlarmType())) {
              lqw.like(RealTimeWarn::getAlarmType, realTimeWarn.getAlarmType());
            }
            if (StringUtils.isNotBlank(realTimeWarn.getStartTime())) {
              lqw.between(
                  RealTimeWarn::getAlarmTime,
                  realTimeWarn.getStartTime(),
                  realTimeWarn.getEndTime());
            }
            lqw.orderByDesc(RealTimeWarn::getAlarmTime);
            return lqw;
          }
        };
    PageResult<RealTimeWarn> dataSourceTestPageResult = page.doPagingQuery(pageRequest);
    // 定时模拟数据
    // timerRealTimeWarnData();
    return dataSourceTestPageResult;
  }

  @Override
  public Result makeSuerWarn(String id) {
    RealTimeWarn realTimeWarn = null;
    try {
      if (StringUtils.isBlank(id)) {
        return Result.failed("告警id为空");
      }
      boolean update =
          update(
              new LambdaUpdateWrapper<RealTimeWarn>()
                  .eq(RealTimeWarn::getId, id)
                  .set(RealTimeWarn::getAlarmStatus, 1)
                  .set(RealTimeWarn::getOperationPeople, SpringSecurityUtils.getUserName())
                  .set(RealTimeWarn::getOperationTime, DateUtils.dateTimeToStr(new Date())));
      realTimeWarn = getById(id);
      if (update) {
        return Result.succeed(realTimeWarn, "确认告警成功");
      } else {
        return Result.failed(realTimeWarn, "确认告警失败");
      }
    } catch (Exception e) {
      return Result.failed(realTimeWarn, "网络异常");
    }
  }

  @Override
  public Result clearWarnById(String id) {
    RealTimeWarn realTimeWarn = null;
    try {
      if (StringUtils.isBlank(id)) {
        return Result.failed("告警id为空");
      }
      realTimeWarn = getById(id);
      String userName = SpringSecurityUtils.getUserName();
      Date alaramTime = DateUtils.strToTime(realTimeWarn.getAlarmTime());
      Date now = new Date();
      long time = DateUtils.compareDate(alaramTime, now);
      if (time > 0) {
        time = time / 1000 / 60;
      }
      boolean update =
          update(
              new LambdaUpdateWrapper<RealTimeWarn>()
                  .eq(RealTimeWarn::getId, id)
                  .set(RealTimeWarn::getAlarmStatus, 2)
                  .set(RealTimeWarn::getOperationPeople, userName)
                  .set(RealTimeWarn::getAlarmDuration, time)
                  .set(RealTimeWarn::getOperationTime, DateUtils.dateTimeToStr(new Date())));
      if (update) {
        return Result.succeed(realTimeWarn, "确认清除成功");
      } else {
        return Result.failed(realTimeWarn, "确认清除失败");
      }
    } catch (Exception e) {
      return Result.failed(realTimeWarn, "网络异常");
    }
  }

  @Override
  public PageResult<RealTimeWarn> pageWarnRealTimeWarnWithStationId(
      PageRequest<RealTimeWarn> pageRequest) {
    MybatiesPageServiceTemplate<RealTimeWarn, RealTimeWarn> page =
        new MybatiesPageServiceTemplate<RealTimeWarn, RealTimeWarn>(this.baseMapper) {
          @Override
          protected Wrapper<RealTimeWarn> constructWrapper(RealTimeWarn realTimeWarn) {
            LambdaQueryWrapper<RealTimeWarn> lqw = new LambdaQueryWrapper<RealTimeWarn>();
            lqw.eq(RealTimeWarn::getAlarmStatus, "0");
            if (realTimeWarn == null) {
              lqw.eq(RealTimeWarn::getHostNumber, "-1");
              return lqw;
            }
            // 如果车站不存在，则查不出数据
            if (StringUtils.isNotBlank(realTimeWarn.getBelongStationId())) {
              List<BelongUnit> belongUnitList =
                  belongUnitService.list(
                      new LambdaQueryWrapper<BelongUnit>()
                          .eq(BelongUnit::getStationId, realTimeWarn.getBelongStationId()));
              List<String> hostListString = new ArrayList<>();
              for (BelongUnit belongUnit : belongUnitList) {
                hostListString.add(belongUnit.getHostNum());
              }
              if (CollectionUtils.isNotEmpty(hostListString)) {
                lqw.in(RealTimeWarn::getHostNumber, hostListString);
              } else {
                lqw.eq(RealTimeWarn::getHostNumber, "-1");
              }

            } else {
              lqw.eq(RealTimeWarn::getHostNumber, "-1");
            }
            lqw.orderByDesc(RealTimeWarn::getAlarmTime);
            return lqw;
          }
        };
    PageResult<RealTimeWarn> dataSourceTestPageResult = page.doPagingQuery(pageRequest);
    // 定时模拟数据
    // timerRealTimeWarnData();
    return dataSourceTestPageResult;
  }
}
