package com.key.win.biz.alarm.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.key.win.biz.alarm.model.RealTimeWarn;
import com.key.win.biz.alarm.service.HistoryWarnSevice;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import com.key.win.system.aop.annotation.LogType;
import com.key.win.system.aop.annotation.SfLog;

@RestController
@RequestMapping("/history")
public class HistoryWarnController {

  @Resource private HistoryWarnSevice historyWarnSevice;

  /**
   * 分页查询 alaramStatus 传 1 确认告警，2 历史告警 不传，查所有(历史，警告)
   *
   * @param pageRequest
   * @return
   */
  @PostMapping("/queryPageInfo")
  protected PageResult<RealTimeWarn> pageQueryHistory(
      @RequestBody PageRequest<RealTimeWarn> pageRequest) {
    return historyWarnSevice.getPageWarnConditions(pageRequest);
  }

  @SfLog(module = "历史告警信息", actionName = "告警清除", logType = LogType.Alarm)
  @PostMapping("/clearWarnById/{id}")
  public Result clearWarn(@PathVariable("id") String id) {
    return historyWarnSevice.clearWarnById(id);
  }

  @PostMapping("/queryById/{id}")
  public Result<RealTimeWarn> queryById(@PathVariable("id") String id) {
    return historyWarnSevice.queryById(id);
  }

  @SfLog(module = "历史告警信息", actionName = "告警删除", logType = LogType.Alarm)
  @PostMapping("/deleteById/{id}")
  public Result<RealTimeWarn> deleteWarnById(@PathVariable("id") String id) {
    return historyWarnSevice.deleteById(id);
  }
}
