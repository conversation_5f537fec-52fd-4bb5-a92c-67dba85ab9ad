package com.key.win.biz.alarm.service.impl;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.key.win.biz.alarm.dao.RealTimeWarnDao;
import com.key.win.biz.alarm.model.RealTimeWarn;
import com.key.win.biz.alarm.service.HistoryWarnSevice;
import com.key.win.common.web.MybatiesPageServiceTemplate;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import com.key.win.utils.DateUtils;
import com.key.win.utils.SpringSecurityUtils;

@Service
public class HistoryWarnImpl extends ServiceImpl<RealTimeWarnDao, RealTimeWarn>
    implements HistoryWarnSevice {

  @Override
  public PageResult<RealTimeWarn> getPageWarnConditions(PageRequest<RealTimeWarn> pageRequest) {
    MybatiesPageServiceTemplate<RealTimeWarn, RealTimeWarn> page =
        new MybatiesPageServiceTemplate<RealTimeWarn, RealTimeWarn>(this.baseMapper) {
          @Override
          protected Wrapper<RealTimeWarn> constructWrapper(RealTimeWarn realTimeWarn) {
            LambdaQueryWrapper<RealTimeWarn> lqw = new LambdaQueryWrapper<RealTimeWarn>();

            if (realTimeWarn == null) {
              lqw.in(RealTimeWarn::getAlarmStatus, 1, 2);
              return lqw;
            }

            if (StringUtils.isNotBlank(realTimeWarn.getAlarmStatus())) {
              lqw.eq(RealTimeWarn::getAlarmStatus, realTimeWarn.getAlarmStatus());
            } else {
              lqw.in(RealTimeWarn::getAlarmStatus, 1, 2);
            }
            if (StringUtils.isNotBlank(realTimeWarn.getBelongStationName())) {
              lqw.and(
                  lq ->
                      lq.like(
                              RealTimeWarn::getBelongStationName,
                              realTimeWarn.getBelongStationName())
                          .or()
                          .like(
                              RealTimeWarn::getBelongStationId,
                              realTimeWarn.getBelongStationName()));
            }
            if (StringUtils.isNotBlank(realTimeWarn.getHostNumber())) {
              lqw.like(RealTimeWarn::getHostNumber, realTimeWarn.getHostNumber());
            }
            if (StringUtils.isNotBlank(realTimeWarn.getStartTime())) {
              lqw.between(
                  RealTimeWarn::getAlarmTime,
                  realTimeWarn.getStartTime(),
                  realTimeWarn.getEndTime());
            }
            lqw.orderByDesc(RealTimeWarn::getOperationTime);
            lqw.orderByDesc(RealTimeWarn::getAlarmTime);
            String targetSql = lqw.getTargetSql();
            return lqw;
          }
        };
    PageResult<RealTimeWarn> dataSourceTestPageResult = page.doPagingQuery(pageRequest);
    return dataSourceTestPageResult;
  }

  @Override
  public Result clearWarnById(String id) {
    RealTimeWarn realTimeWarn = null;
    try {
      if (StringUtils.isBlank(id)) {
        return Result.failed("告警id为空");
      }
      realTimeWarn = getById(id);

      String alaramTime = realTimeWarn.getAlarmTime();
      String now = DateUtils.dateTimeToStr(new Date());
      long time = DateUtils.compareDate(DateUtils.strToTime(alaramTime), DateUtils.strToTime(now));
      if (time > 0) {
        time = time / 1000 / 60;
      }
      boolean update =
          update(
              new LambdaUpdateWrapper<RealTimeWarn>()
                  .eq(RealTimeWarn::getId, id)
                  .set(RealTimeWarn::getAlarmStatus, 2)
                  .set(RealTimeWarn::getOperationPeople, SpringSecurityUtils.getUserName())
                  .set(RealTimeWarn::getAlarmDuration, time)
                  .set(RealTimeWarn::getOperationTime, DateUtils.dateTimeToStr(new Date())));
      if (update) {
        return Result.succeed(realTimeWarn, "确认清除成功");
      } else {
        return Result.failed(realTimeWarn, "确认清除失败");
      }
    } catch (Exception e) {
      return Result.failed(realTimeWarn, "网络异常");
    }
  }

  @Override
  public Result<RealTimeWarn> queryById(String id) {
    try {
      if (StringUtils.isBlank(id)) {
        return Result.failed("告警id为空");
      }
      RealTimeWarn realTimeWarn = getById(id);
      if (realTimeWarn != null) {
        return Result.succeed(realTimeWarn, "查询成功");
      } else {
        return Result.failed("查询失败");
      }
    } catch (Exception e) {
      return Result.failed("网络异常");
    }
  }

  @Override
  public Result<RealTimeWarn> deleteById(String id) {
    try {
      if (StringUtils.isBlank(id)) {
        return Result.failed("告警id为空");
      }
      RealTimeWarn realTimeWarn = getById(id);
      boolean delete = removeById(id);
      if (delete) {
        return Result.succeed(realTimeWarn, "删除成功");
      } else {
        return Result.failed("删除失败");
      }
    } catch (Exception e) {
      return Result.failed("网络异常");
    }
  }
}
