package com.key.win.biz.alarm.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.key.win.common.web.MybatisID;
import javax.persistence.Entity;
import javax.persistence.Transient;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(callSuper = true)
@TableName("real_time_warn")
@Entity(name = "real_time_warn")
public class RealTimeWarn extends MybatisID {

  @TableField(exist = false)
  @Transient
  private String startTime;

  @TableField(exist = false)
  @Transient
  private String endTime;

  private String alarmId;

  private String belongStationId;

  private String belongStationName;

  private String alarmName;

  private String networkName;

  private String hostNumber;

  private String equipmentType;

  private String alarmGrade;

  private String alarmType;

  /** 告警次数 */
  private long alarmTimes;

  /** 告警时间 */
  private String alarmTime;

  private String alarmDuration;

  private String operationTime;

  private String operationPeople;

  private String remark;

  /** 告警状态 - 0 实时告警，1 确认告警，2 清除告警（历史告警） */
  private String alarmStatus;

  /** 20230618当前字段被占用 用作此条信息是否上报集中报警的标识位，0:未上报 1:上报 */
  private String attr1;

  private String attr2;

  private String attr3;
}
