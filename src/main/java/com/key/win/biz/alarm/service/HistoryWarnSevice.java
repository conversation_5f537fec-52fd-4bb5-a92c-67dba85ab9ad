package com.key.win.biz.alarm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.key.win.biz.alarm.model.RealTimeWarn;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;

public interface HistoryWarnSevice extends IService<RealTimeWarn> {

  PageResult<RealTimeWarn> getPageWarnConditions(PageRequest<RealTimeWarn> pageRequest);

  Result clearWarnById(String id);

  Result<RealTimeWarn> queryById(String id);

  Result<RealTimeWarn> deleteById(String id);
}
