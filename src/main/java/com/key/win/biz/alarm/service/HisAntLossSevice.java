package com.key.win.biz.alarm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.key.win.biz.alarm.model.HisAntLoss;
import com.key.win.biz.alarm.model.HisAntLossChartDto;
import com.key.win.biz.alarm.model.RealTimeWarn;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;

public interface HisAntLossSevice extends IService<HisAntLoss> {

  /**
   * 查询历史天线路损记录
   * @param param
   * @return
   */
  HisAntLossChartDto getChartListByHostNum(HisAntLoss param);
  /**
   * 查询分组分页的历史天线路损值
   * @param pageRequest
   * @return
   */
  PageResult<HisAntLoss> getPageList(PageRequest<HisAntLoss> pageRequest);

  /**
   * 保存天线路损值
   * @param antName
   * @param loss
   * @return
   */
  int saveHisAntLoss(String hostNumber,String lineId,String lineName,String statioId,String stationName,String antName,String probeId,String probeSid,String loss);

  /**
   * 导出历史天线路损
   * @param response
   * @throws IOException
   */
  void exportHisAntLoss(HttpServletResponse response) throws IOException;
}
