package com.key.win.biz.alarm.service;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.alarm.model.AlarmExportDto;
import com.key.win.biz.alarm.model.RealTimeWarn;
import com.key.win.utils.SpringBeanUtilsExt;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AlarmExportService {

  @Autowired private RealTimeWarnSevice realTimeWarnSevice;

  @Autowired private HistoryWarnSevice historyWarnSevice;

  public void alarmExport(HttpServletResponse response, String alarmStatus) throws IOException {
    EasyExcel.write(response.getOutputStream(), AlarmExportDto.class)
        .sheet("告警列表")
        .doWrite(alarmDataList(alarmStatus));
  }

  private List<AlarmExportDto> alarmDataList(String alarmStatus) {
    LambdaQueryWrapper<RealTimeWarn> lqw = new LambdaQueryWrapper<RealTimeWarn>();
    lqw.orderByAsc(RealTimeWarn::getBelongStationId);
    lqw.orderByAsc(RealTimeWarn::getEquipmentType);
    lqw.orderByDesc(RealTimeWarn::getAlarmTimes);
    lqw.orderByDesc(RealTimeWarn::getOperationTime);
    lqw.orderByDesc(RealTimeWarn::getAlarmStatus);
    List<AlarmExportDto> excelList = new ArrayList<AlarmExportDto>();

    List<RealTimeWarn> list = new ArrayList<RealTimeWarn>();
    String statusName = "";
    boolean flag = false;
    // 0 实时告警，1 确认告警，2 清除告警（历史告警）
    if (StringUtils.equals(alarmStatus, "0")) {
      statusName = "实时告警";
      lqw.eq(RealTimeWarn::getAlarmStatus, "0");
      list = realTimeWarnSevice.list(lqw);
    } else if (StringUtils.equals(alarmStatus, "1")) {
      statusName = "历史告警（确认）";
      lqw.eq(RealTimeWarn::getAlarmStatus, "1");
      list = historyWarnSevice.list(lqw);
    } else if (StringUtils.equals(alarmStatus, "2")) {
      statusName = "历史告警（清除）";
      lqw.eq(RealTimeWarn::getAlarmStatus, "2");
      list = historyWarnSevice.list(lqw);
    } else if (StringUtils.equals(alarmStatus, "3")) {
      statusName = "历史告警";
      flag = true;
      lqw.and(
          lq ->
              lq.eq(RealTimeWarn::getAlarmStatus, "2").or().eq(RealTimeWarn::getAlarmStatus, "1"));
      list = historyWarnSevice.list(lqw);
    } else {
      return excelList;
    }
    for (RealTimeWarn w : list) {
      AlarmExportDto excelDto = new AlarmExportDto();
      SpringBeanUtilsExt.copyProperties(w, excelDto);
      if (flag) {
        boolean cf = excelDto.getAlarmStatus().equals("1");
        boolean cls = excelDto.getAlarmStatus().equals("2");
        if (cf) {
          statusName = "历史告警（确认）";
        }
        if (cls) {
          statusName = "历史告警（清除）";
        }
      }
      String operationPeople = excelDto.getOperationPeople();
      if (!StringUtils.isBlank(operationPeople) && operationPeople.equals("SYS_CHECK")) {
        operationPeople = "系统自动恢复";
        excelDto.setOperationPeople(operationPeople);
      }
      excelDto.setAlarmStatus(statusName);
      excelList.add(excelDto);
    }
    return excelList;
  }
}
