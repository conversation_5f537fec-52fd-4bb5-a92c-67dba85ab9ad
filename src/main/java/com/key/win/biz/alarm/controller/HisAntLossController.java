package com.key.win.biz.alarm.controller;

import com.alibaba.fastjson.JSONObject;
import com.key.win.biz.alarm.model.HisAntLoss;
import com.key.win.biz.alarm.model.HisAntLossChartDto;
import com.key.win.biz.alarm.model.RealTimeWarn;
import com.key.win.biz.alarm.service.HisAntLossSevice;
import com.key.win.biz.alarm.service.HistoryWarnSevice;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import com.key.win.system.aop.annotation.LogType;
import com.key.win.system.aop.annotation.SfLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/history/loss")
public class HisAntLossController {

  @Resource private HisAntLossSevice hisAntLossSevice;

  /**
   *  分页分组（天线纬度）查询历史路损值列表
   * @param pageRequest
   * @return
   */
  @PostMapping("/queryPageInfo")
  protected PageResult<HisAntLoss> pageQueryHisLossList(@RequestBody PageRequest<HisAntLoss> pageRequest) {
    return hisAntLossSevice.getPageList(pageRequest);
  }

  /**
   * 根据主机编码&天线名称 查询天线历史路损值列表
   * @param param
   * @return
   */
  @PostMapping("/queryListByHostNum")
  protected Result<HisAntLossChartDto> queryChartListByHostNum(@RequestBody HisAntLoss param) {
    if(StringUtils.isBlank(param.getAntName()) || StringUtils.isBlank(param.getHostNumber())){
      return Result.failed("参数不能为空");
    }
    return Result.succeed(hisAntLossSevice.getChartListByHostNum(param),"查询成功");
  }

  @GetMapping("/export/all")
  public void exportHisLossAll(HttpServletResponse response) throws IOException {
    hisAntLossSevice.exportHisAntLoss(response);
  }
}
