package com.key.win.biz.statistic.service;

import com.key.win.biz.statistic.dao.TopoStatisticDao;
import com.key.win.biz.statistic.vo.AllStationStatisticVO;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class StatisticService {

  @Autowired private TopoStatisticDao topoStatisticDao;

  public Map<String, List<AllStationStatisticVO>> allStationStatistic() {
    List<AllStationStatisticVO> allData = topoStatisticDao.allStationStatistic();
    //		Map<String, Map<String, List<AllStationStatisticVO>>> collect =
    // allData.stream().collect(Collectors.groupingBy(
    //				AllStationStatisticVO::getLineId, LinkedHashMap::new,
    //				Collectors.groupingBy(AllStationStatisticVO::getStationId, LinkedHashMap::new,
    // Collectors.toList())));
    Map<String, List<AllStationStatisticVO>> collect =
        allData.stream()
            .collect(
                Collectors.groupingBy(
                    AllStationStatisticVO::getStationId, LinkedHashMap::new, Collectors.toList()));
    return collect;
  }
}
