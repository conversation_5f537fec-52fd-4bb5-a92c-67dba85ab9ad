package com.key.win.biz.statistic.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.key.win.biz.statistic.service.StatisticService;
import com.key.win.biz.statistic.vo.AllStationStatisticVO;
import com.key.win.common.web.Result;

@RestController
@RequestMapping("/topo/statistic/*")
public class StatisticController {

  @Autowired private StatisticService statisticService;

  @PostMapping("/all")
  public Result<Map<String, List<AllStationStatisticVO>>> allStationStatistic() {
    Map<String, List<AllStationStatisticVO>> data = statisticService.allStationStatistic();
    return Result.succeed(data, "统计数据获取完毕");
  }
}
