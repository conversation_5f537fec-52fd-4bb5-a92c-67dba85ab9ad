package com.key.win.biz.statistic.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.key.win.biz.statistic.vo.AllStationStatisticVO;
import com.key.win.biz.statistic.vo.StatisticVO;

@Mapper
public interface TopoStatisticDao {

  /**
   * 按车站统计器件个数
   *
   * @param stationId
   * @return
   */
  public List<StatisticVO> topoStatistic(@Param("stationId") String stationId);

  /**
   * 资产统计资源信息
   *
   * @return
   */
  public List<AllStationStatisticVO> allStationStatistic();
}
