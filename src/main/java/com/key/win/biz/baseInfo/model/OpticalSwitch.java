package com.key.win.biz.baseInfo.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Date;

@Data
@TableName("optical_switch")
public class OpticalSwitch   {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("port_1")
    private String port1;

    @TableField("port_2")
    private String port2;

    @TableField("port_3")
    private String port3;

    @TableField("port_4")
    private String port4;

    @TableField("port_5")
    private String port5;

    @TableField("port_6")
    private String port6;

    @TableField("port_7")
    private String port7;

    @TableField("port_8")
    private String port8;

    @TableField("receive_power_1")
    private String receivePower1;
    @TableField("output_power_1")
    private String outputPower1;

    @TableField("receive_power_2")
    private String receivePower2;
    @TableField("output_power_2")
    private String outputPower2;

    @TableField("receive_power_3")
    private String receivePower3;
    @TableField("output_power_3")
    private String outputPower3;

    @TableField("receive_power_4")
    private String receivePower4;
    @TableField("output_power_4")
    private String outputPower4;

    @TableField("receive_power_5")
    private String receivePower5;
    @TableField("output_power_5")
    private String outputPower5;

    @TableField("receive_power_6")
    private String receivePower6;
    @TableField("output_power_6")
    private String outputPower6;

    @TableField("receive_power_7")
    private String receivePower7;
    @TableField("output_power_7")
    private String outputPower7;

    @TableField("receive_power_8")
    private String receivePower8;
    @TableField("output_power_8")
    private String outputPower8;

    @TableField("belong_line_id")
    private String belongLineId; //线路Id

    @TableField("belong_line_name")
    private String belongLineName;//线路名称

    @TableField("belong_station_id")
    private String belongStationId;//'车站ID编码'

    @TableField("belong_station_name")
    private String belongStationName;//'车站名称'

    @TableField("host_number")
    private String hostNumber; //'设备主机编码'

    @TableField("create_by")
    private String createBy;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("is_deleted")
    private Integer isDeleted;


    @TableField(exist = false)
    private String startTime;

    @TableField(exist = false)
    private String endTime;

}
