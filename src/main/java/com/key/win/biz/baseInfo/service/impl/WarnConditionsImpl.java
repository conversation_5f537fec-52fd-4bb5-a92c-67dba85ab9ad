package com.key.win.biz.baseInfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.key.win.biz.baseInfo.dao.WarnConditionsDao;
import com.key.win.biz.baseInfo.model.WarnConditions;
import com.key.win.biz.baseInfo.service.WarnConditionsService;
import com.key.win.common.web.MybatiesPageServiceTemplate;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class WarnConditionsImpl extends ServiceImpl<WarnConditionsDao, WarnConditions>
    implements WarnConditionsService {

  @Override
  public PageResult<WarnConditions> getPageWarnConditions(PageRequest<WarnConditions> pageRequest) {
    MybatiesPageServiceTemplate<WarnConditions, WarnConditions> page =
        new MybatiesPageServiceTemplate<WarnConditions, WarnConditions>(this.baseMapper) {
          @Override
          protected Wrapper<WarnConditions> constructWrapper(WarnConditions warnConditions) {
            LambdaQueryWrapper<WarnConditions> lqw = new LambdaQueryWrapper<WarnConditions>();
            if (warnConditions == null) {
              return lqw;
            }
            if (StringUtils.isNotBlank(warnConditions.getWarnParameter())) {
              lqw.like(WarnConditions::getWarnParameter, warnConditions.getWarnParameter());
            }

            if (StringUtils.isNotBlank(warnConditions.getRuleType())) {
              lqw.eq(WarnConditions::getRuleType, warnConditions.getRuleType());
            }
            if (StringUtils.isNotBlank(warnConditions.getRule())) {
              lqw.like(WarnConditions::getRule, warnConditions.getRule());
            }
            return lqw;
          }
        };
    PageResult<WarnConditions> dataSourceTestPageResult = page.doPagingQuery(pageRequest);
    return dataSourceTestPageResult;
  }

  @Override
  public Result saveOrUpdateWarnConditions(WarnConditions warnConditions) {
    try {
      if (warnConditions == null) {
        return Result.failed("保存参数为空");
      }
      boolean saveOrUpdate = saveOrUpdate(warnConditions);
      if (saveOrUpdate) {
        return Result.succeed("保存或更新成功");
      } else {
        return Result.failed("保存或更新失败");
      }
    } catch (Exception e) {
      return Result.failed("网络异常");
    }
  }

  @Override
  public Result deleteById(String id) {
    try {
      if (StringUtils.isBlank(id)) {
        return Result.failed("id参数为空 ");
      }
      boolean deleteById = removeById(id);
      if (deleteById) {
        return Result.succeed("删除成功");
      } else {
        return Result.failed("删除失败");
      }
    } catch (Exception e) {
      return Result.failed("网络异常");
    }
  }

  @Override
  public Result<WarnConditions> queryById(String id) {
    try {
      if (StringUtils.isBlank(id)) {
        return Result.failed("id参数为空");
      }
      WarnConditions warnConditions = getById(id);
      if (warnConditions != null) {
        return Result.succeed(warnConditions, "查询成功");
      } else {
        return Result.failed("查询失败");
      }
    } catch (Exception e) {
      return Result.failed("网络异常");
    }
  }
}
