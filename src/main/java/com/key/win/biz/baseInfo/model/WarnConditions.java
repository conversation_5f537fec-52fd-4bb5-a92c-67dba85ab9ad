package com.key.win.biz.baseInfo.model;

import javax.persistence.Entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.key.win.common.web.MybatisID;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("warn_conditions")
@Entity(name = "warn_conditions")
public class WarnConditions extends MybatisID {

  private String rule;

  private String ruleType;

  private String netNumber;

  private String warnParameter;

  private String status;

  private String createPeople;

  private String createTime;

  private String attr1;

  private String attr2;

  private String attr3;
}
