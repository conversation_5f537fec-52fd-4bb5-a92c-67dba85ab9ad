package com.key.win.biz.baseInfo.model;

import javax.persistence.Entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.key.win.common.web.MybatisID;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("warn_type")
@Entity(name = "warn_type")
public class WarnType extends MybatisID {

  private String typeNumber;

  private String typeName;

  private String remark;

  private String attr1;

  private String attr2;

  private String attr3;
}
