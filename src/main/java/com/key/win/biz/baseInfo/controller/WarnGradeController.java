package com.key.win.biz.baseInfo.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.key.win.biz.baseInfo.model.WarnGrade;
import com.key.win.biz.baseInfo.service.WarnGradeService;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;

@RestController
@RequestMapping("/warn/grade")
public class WarnGradeController {

  @Resource private WarnGradeService warnGradeService;

  @PostMapping("/queryPageInfo")
  public PageResult<WarnGrade> pageWarnGrade(@RequestBody PageRequest<WarnGrade> pageRequest) {
    return warnGradeService.getPageWarnGrade(pageRequest);
  }

  @PostMapping("/saveOrUpdate")
  public Result saveOrUpdateWarnType(@RequestBody WarnGrade warnGrade) {
    return warnGradeService.saveOrUpdateWarnGrade(warnGrade);
  }

  @PostMapping("/deleteById/{id}")
  public Result deleteWarnType(@PathVariable("id") String id) {
    return warnGradeService.deleteById(id);
  }

  @PostMapping("/queryById/{id}")
  public Result<WarnGrade> queryById(@PathVariable("id") String id) {
    return warnGradeService.queryById(id);
  }
}
