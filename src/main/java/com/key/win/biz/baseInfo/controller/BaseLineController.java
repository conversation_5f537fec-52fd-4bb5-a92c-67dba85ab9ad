package com.key.win.biz.baseInfo.controller;

import com.key.win.biz.baseInfo.model.BaseLine;
import com.key.win.biz.baseInfo.service.BaseLineService;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/line")
public class BaseLineController {

  @Resource private BaseLineService baseLineService;

  @PostMapping("/queryPageInfo")
  public PageResult<BaseLine> pageSiteInfo(@RequestBody PageRequest<BaseLine> pageRequest) {
    return baseLineService.getPageBaseLine(pageRequest);
  }

  @PostMapping("/queryList")
  public Result<List<BaseLine>> pageSiteInfo() {
    return baseLineService.getBaseLineList();
  }
}
