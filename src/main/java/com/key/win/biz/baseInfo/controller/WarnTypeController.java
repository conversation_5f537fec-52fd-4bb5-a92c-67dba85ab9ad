package com.key.win.biz.baseInfo.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.key.win.biz.baseInfo.model.WarnType;
import com.key.win.biz.baseInfo.service.WarnTypeService;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;

@RestController
@RequestMapping("/warn/type")
public class WarnTypeController {

  @Resource private WarnTypeService warnTypeService;

  @PostMapping("/queryPageInfo")
  public PageResult<WarnType> pageWarnType(@RequestBody PageRequest<WarnType> pageRequest) {
    return warnTypeService.getPageWarnType(pageRequest);
  }

  @PostMapping("/saveOrUpdate")
  public Result saveOrUpdateWarnType(@RequestBody WarnType WarnType) {
    return warnTypeService.saveOrUpdateWarnType(WarnType);
  }

  @PostMapping("/deleteById/{id}")
  public Result deleteWarnType(@PathVariable("id") String id) {
    return warnTypeService.deleteById(id);
  }

  @PostMapping("/queryById/{id}")
  public Result<WarnType> queryById(@PathVariable("id") String id) {
    return warnTypeService.queryById(id);
  }
}
