package com.key.win.biz.baseInfo.model;

import java.util.Date;
import java.util.List;

import javax.persistence.Entity;
import javax.persistence.Transient;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.key.win.common.web.MybatisID;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("belong_unit")
@Entity(name = "belong_unit")
public class BelongUnit extends MybatisID {

  /** */
  private static final long serialVersionUID = 1L;

  private String hostNum;

  private String unitName;

  private String lineId;

  private String lineName;

  private String stationId;

  private String stationName;

  private String startPower;

  private String endPower;

  private String startFrequency;

  private String endFrequency;

  private String hostType;

  private String witeTime;

  private String readTime;

  /**
   * 精确读取修正值
   */
  private String sourcePowerCorrection;

  private String installPosition;

  private Boolean accurateReading;

  private Boolean troubleshooting;

  private String ipAddress;

  private String port;

  /**
   * 发射功率
   */
  private String transmitPower;

  /**
   * 频率步进
   */
  private String frequencyStep;

  /**
   * 故障诊断周期
   */
  private String faultDiagnosisCycle;

  /**
   * 故障诊断时间
   */
  private String faultDiagnosisTime;

  /**
   * 故障诊断次数
   */
  private String faultDiagnosisNumber;

  private String xx;

  private String yy;

  private String zz;

  private String pid;

  @TableField(exist = false)
  @Transient
  private List<Probe> probeList;

  @Transient
  @TableField(exist = false)
  private Integer timer;

  private String softwareVersion;

  private String deviceVersion;

  private String productSerVersion;

  @JsonFormat(pattern = "yyyyMMddHHmmss", timezone = "GMT+8")
  private Date hostCurrentTime;

  private String readStatus;

  private String readDate;

  private String readingProgress;

  @Transient
  @TableField(exist = false)
  private String diagnosisStatus;

  private String deviceStatus;

  private String attr1;

  private String attr2;

  private String attr3;

  private String attr4;

  private String attr5;

  private String attr6;

  private String attr7;

  private String attr8;

  private String attr9;

  private String attr10;

  /**
   * 设备安装类型（室内，室外）
   */
  private String installWay;

  /**
   * 设备代数（I代， II代）
   */
  private String hardwareVersion;

  /**
   * 告警判断次数
   */
  private String alarmJudgeCount;

  /**
   * 功率步进
   */
  private String powerStep;
}
