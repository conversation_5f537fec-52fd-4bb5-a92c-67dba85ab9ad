package com.key.win.biz.baseInfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.key.win.biz.baseInfo.model.OpticalSwitch;
import com.key.win.biz.baseInfo.vo.OpticalSwitchVo;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;

public interface OpticalSwitchService extends IService<OpticalSwitch> {

      Integer saveOrUpdateOptical(OpticalSwitchVo bean);

      PageResult<OpticalSwitch> getPageList(PageRequest<OpticalSwitch> pageRequest);
}
