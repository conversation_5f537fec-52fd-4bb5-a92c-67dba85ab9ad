package com.key.win.biz.baseInfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.key.win.biz.baseInfo.model.BaseStation;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import java.util.List;

public interface BaseStationService extends IService<BaseStation> {

  PageResult<BaseStation> getPageBaseStation(PageRequest<BaseStation> pageRequest);

  Result<List<BaseStation>> queryById(String lineId);

  List<BaseStation> list();
}
