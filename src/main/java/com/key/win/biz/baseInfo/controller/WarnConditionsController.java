package com.key.win.biz.baseInfo.controller;

import com.key.win.biz.baseInfo.model.WarnConditions;
import com.key.win.biz.baseInfo.service.WarnConditionsService;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/warn/conditions")
public class WarnConditionsController {

  @Resource private WarnConditionsService warnConditionsService;

  @PostMapping("/queryPageInfo")
  public PageResult<WarnConditions> pageWarnConditions(
      @RequestBody PageRequest<WarnConditions> pageRequest) {
    return warnConditionsService.getPageWarnConditions(pageRequest);
  }

  @PostMapping("/saveOrUpdate")
  public Result saveOrUpdateWarnConditions(@RequestBody WarnConditions warnConditions) {
    return warnConditionsService.saveOrUpdateWarnConditions(warnConditions);
  }

  @PostMapping("/deleteById/{id}")
  public Result deleteWarnConditions(@PathVariable("id") String id) {
    return warnConditionsService.deleteById(id);
  }

  @PostMapping("/queryById/{id}")
  public Result<WarnConditions> queryById(@PathVariable("id") String id) {
    return warnConditionsService.queryById(id);
  }
}
