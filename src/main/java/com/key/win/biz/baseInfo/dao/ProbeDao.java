package com.key.win.biz.baseInfo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.key.win.biz.baseInfo.model.Probe;
import com.key.win.biz.baseInfo.vo.AntennaVO;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/10/29 11:25
 */
@Mapper
public interface ProbeDao extends BaseMapper<Probe> {

  @Delete("delete from sf_probe where host_number = #{hostNumber}")
  public void removeAllProbeByHostNumber(@Param(value = "hostNumber") String hostNumber);

  @Select(
      "select ANT.number AS antName , ant.probe_status  from sf_probe as ant WHERE ant.enable_flag = 1 and (ant.probe_status is not null and ant.probe_status <> '') and ant.host_number = #{hostNumber} ")
  public List<AntennaVO> antListByHostNumber(@Param(value = "hostNumber") String hostNumber);
}
