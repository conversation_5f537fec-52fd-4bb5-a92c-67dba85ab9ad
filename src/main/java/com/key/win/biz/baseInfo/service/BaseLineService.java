package com.key.win.biz.baseInfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.key.win.biz.baseInfo.model.BaseLine;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import java.util.List;

public interface BaseLineService extends IService<BaseLine> {

  PageResult<BaseLine> getPageBaseLine(PageRequest<BaseLine> pageRequest);

  Result<List<BaseLine>> getBaseLineList();

  List<BaseLine> list();

  /**
   * 获取当前数据库中启用的线路
   *
   * @return
   */
  List<String> operationLineList();
}
