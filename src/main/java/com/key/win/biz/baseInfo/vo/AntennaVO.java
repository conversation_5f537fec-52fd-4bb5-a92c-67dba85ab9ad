package com.key.win.biz.baseInfo.vo;

import java.io.Serializable;
import lombok.Data;

@Data
public class AntennaVO implements Serializable {

  private String antName;

  private String coding;

  /**
   * 原始探针编号,对于未满24位编号需要用0填充的编号,此时的编号存储还暂未填充0时的原始手动录入探针编号 eg: 21072814 原始编号 000000000000000021072814
   * 填充后的 那么此oCoding 代表的则是 原始的21072814. 对于000000000000000021072814 这个编号则是保存在 coding 属性中
   */
  private String oCoding;

  private String probeStatus;
}
