package com.key.win.biz.baseInfo.model;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

@Data
@TableName("base_line")
public class BaseLine {

  private String lineId;

  private String lineName;

  private String lineShortName;

  private String lineType;

  private String lineLength;

  private String lineManagerId;

  private String lineCharacter;

  private String lineColor;

  /** 运营状态 1：开通 0：未开通" */
  private String lineOperationState;
}
