package com.key.win.biz.baseInfo.model;

import javax.persistence.Entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.key.win.common.web.MybatisID;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("site_info")
@Entity(name = "site_info")
public class SiteInfo extends MybatisID {

  private String lineName;

  private String lineCode;

  private String stationName;

  private String stationCode;

  private String phoneNumber;

  private String wareType;

  private String stationStatus;

  private String address;

  private String finishDate;

  private String vipType;

  private String coverageType;

  private String remarks;

  private String finishPeople;

  private String majorPhone;

  private String attr1;

  private String attr2;

  private String attr3;

  private String attr4;

  private String attr5;

  private String attr6;

  private String attr7;

  private String attr8;

  private String attr9;

  private String attr10;
}
