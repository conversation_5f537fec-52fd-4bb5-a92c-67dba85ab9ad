package com.key.win.biz.baseInfo.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.model.Probe;
import com.key.win.biz.baseInfo.model.SiteInfo;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.biz.baseInfo.service.ProbeService;
import com.key.win.biz.baseInfo.service.SiteInfoService;
import com.key.win.biz.baseInfo.service.impl.ProbeServiceImpl;
import com.key.win.biz.topo.enums.DeviceStatus;
import com.key.win.biz.topo.enums.DeviceType;
import com.key.win.biz.topo.model.TopoNodes;
import com.key.win.biz.topo.service.TopoService;
import com.key.win.common.result.ResultCode;
import com.key.win.common.web.Result;
import com.key.win.config.RedisUtil;
import com.key.win.mqtt.mq.cmd.EmqRequestService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

import com.key.win.utils.SpringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/3d/*")
@SuppressWarnings("rawtypes")
public class D3ModelController {

  @Autowired private EmqRequestService emqRequestService;

  @Autowired private SiteInfoService siteInfoService;

  @Resource private BelongUnitService belongUnitService;

  @Autowired private ProbeService probeService;

  @Autowired private TopoService topoService;
  @Autowired private RedisUtil redisUtil;
  @Autowired private ProbeServiceImpl probeInfoService;

  /**
   * 车站3D监测统计
   *
   * @param stationId
   */
  @PostMapping("/station/{stationId}")
  public Result<Map> station3Dcheck(@PathVariable("stationId") String stationId) {
    int hostCount = 0;
    int hostError = 0;

    int antennaCount = 0; // 天线个数
    int antennaErrorCount = 0; // 天线异常个数
    int antennaOnlineCount = 0; // 天线在线个数

    stationId = StringUtils.defaultIfBlank(stationId, "");
    boolean stationIdIsBlank = StringUtils.isBlank(stationId);
    if (stationIdIsBlank) {
      return Result.failed("传入的车站ID为空");
    }

    // 获取车站下的监控主机列表
    LambdaQueryWrapper<BelongUnit> lqw = new LambdaQueryWrapper<BelongUnit>();
    lqw.eq(BelongUnit::getStationId, stationId);
    List<BelongUnit> unitList = belongUnitService.list(lqw);

    int size = unitList.size();
    if (size == 0) {
      return Result.failed("当前车站下暂无监控主机信息");
    }

    List<Map<String, Object>> hostInfoList = new ArrayList<Map<String, Object>>();
    List<Map<String, Object>> antMapList = new ArrayList<Map<String, Object>>();
    hostCount += hostInfoList.size();

    for (BelongUnit unit : unitList) {
      boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + unit.getHostNum());
      //			clientIsOnline = true;
      Map<String, Object> HostInfoMap = new LinkedHashMap<String, Object>();
      HostInfoMap.put("hostNumber", unit.getHostNum()); // 主机编号
      HostInfoMap.put("HostType", unit.getHostType()); // 主机类型
      HostInfoMap.put(
          "startFrequency", StringUtils.defaultIfBlank(unit.getStartFrequency(), "-1")); // 开始频率
      HostInfoMap.put(
          "endFrequency", StringUtils.defaultIfBlank(unit.getEndFrequency(), "-1")); // 结束频率
      HostInfoMap.put("startPower", StringUtils.defaultIfBlank(unit.getStartPower(), "-1")); // 开始功率
      HostInfoMap.put("endPower", StringUtils.defaultIfBlank(unit.getEndPower(), "-1")); // 结束功率
      HostInfoMap.put(
          "transmitPower", StringUtils.defaultIfBlank(unit.getTransmitPower(), "-1")); // 发射功率
      HostInfoMap.put("deviceStatus", clientIsOnline ? "Online" : "Offline"); // 设备状态

      List<TopoNodes> antNodes = topoService.getAntNodesByHostNum(unit.getHostNum(), false);
      antennaCount += antNodes.size();

      for (TopoNodes node : antNodes) {
        Map<String, Object> antInfoMap = new LinkedHashMap<String, Object>();
        antInfoMap.put("probeId", StringUtils.defaultIfBlank(node.getRfidlabel(), "-")); // 探针编号
        antInfoMap.put("antNumber", StringUtils.defaultIfBlank(node.getProbeSid(), "")); // 序号绑定天线用
        antInfoMap.put("antTypeName", deviceType(node.getType())); // 天线类型名称
        antInfoMap.put("antName", StringUtils.defaultIfBlank(node.getName(), ""));
        if (clientIsOnline) {
          if (node.getState() == DeviceStatus.green.getState()) {
            antennaOnlineCount++;
          } else if (node.getState() == DeviceStatus.red.getState()
              || node.getState() == DeviceStatus.gray.getState()) {
            antennaErrorCount++;
          } else {
            antennaErrorCount++;
          }
          antInfoMap.put("antStatus", node.getState()); // 天线状态
          antInfoMap.put("pathLoss", (long) node.getLoss()); // 路损
        } else {
          antennaErrorCount++;
          antInfoMap.put("antStatus", DeviceStatus.gray.getState()); // 天线状态
          antInfoMap.put("pathLoss", 120); // 路损
        }
        antMapList.add(antInfoMap);
      }

      HostInfoMap.put("antennas", antMapList);
      hostInfoList.add(HostInfoMap);
    }

    Map<String, Object> res = new LinkedHashMap<>();

    res.put("hostCount", hostCount); // 监控主机总数
    res.put("hostError", hostError); // 监控主机异常总数
    int hostNormal = hostCount - hostError < 0 ? 0 : (hostCount - hostError);
    res.put("hostNormal", hostNormal); // 监控主机正常总数

    res.put("antennaCount", antennaCount); // 天线总数
    res.put("antennaOnlineCount", antennaOnlineCount); // 探针总数
    res.put("antennaErrorCount", antennaErrorCount); // 探针总数

    res.put("sfInfo", hostInfoList);
    return Result.succeed(res, "3D室分数据获取完毕");
  }

  /**
   * 线网首页室分信息统计
   *
   * @return
   */
  @PostMapping("/networkInfo")
  public Result<Map> networkDeviceStatistics() {

    int siteCount = 0; // 室分站点总数
    int hostCount = 0; // 监控主机总数
    int antennaCount = 0; // 天线总数
    int probeCount = 0; // 探针总数
    int hostErrorCount = 0; // 主机异常总数
    int antErrorCount = 0;
    int antUnknownCount = 0;
    int antSuccessCount = 0;

    List<Map<String, String>> networkInfos = new ArrayList<Map<String, String>>();

    List<SiteInfo> hostErrSiteList = new ArrayList<>(); // 存储监控主机异常车站

    // 监控站点列表

    List<SiteInfo> siteList = siteInfoService.list();
    if (siteList.size() == 0) {
      Map<String, Object> res = new HashMap<>();
      res.put("siteCount", siteCount); // 室分车站总数
      res.put("hostCount", hostCount); // 监控主机总数
      res.put("antennaCount", antennaCount); // 天线总数
      res.put("probeCount", probeCount); // 探针总数
      res.put("hostErrorCount", hostErrorCount); // 主机异常总数
      res.put("hostNormalCount", 0); // 主机正常
      res.put("antErrorCount", antErrorCount); // 天线异常
      res.put("antOnlineCount", antSuccessCount); // 天线正常
      res.put("antUnknownCount", antUnknownCount); // 天线正常
      res.put("siteErrorList", hostErrSiteList); // 异常主机车站信息
      res.put("network", networkInfos);
      return Result.failedWith(res, ResultCode.SF_STATION_NOT_EXIST.getCode(), "当前线路暂未查询到室分站点信息");
    } else {
      siteCount += siteList.size();
    }

    for (SiteInfo sinfo : siteList) {
      boolean hostErrorFlag = false; // 监控主机是否异常标识

      LambdaQueryWrapper<BelongUnit> lqw = new LambdaQueryWrapper<>();
      lqw.eq(BelongUnit::getPid, sinfo.getId());
      List<BelongUnit> hostList = belongUnitService.list(lqw); // 获取室分车站的ID（主机的PID）获取车站下的监控主机列表

      // 监控主机个数
      int size = hostList.size();
      if (size == 0) {
        continue;
      } else {
        hostCount += hostList.size();
      }

      Map<String, String> stationInfo = new HashMap<>();
      String sid = sinfo.getStationCode();
      stationInfo.put("stationId", sid);
      stationInfo.put("hostCount", String.valueOf(size));
      networkInfos.add(stationInfo);

      for (BelongUnit unit : hostList) {
        LambdaQueryWrapper<Probe> lqwAnt = new LambdaQueryWrapper<Probe>();
        lqwAnt.eq(Probe::getHostNumber, unit.getHostNum());
        List<Probe> probeList = probeService.list(lqwAnt); // 获取监控主机下的所有探针列表
        probeCount += probeList.size();

        // 当前监控主机是否在线
        boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + unit.getHostNum());
        // 从拓扑图中获取 所有的天线节点
        List<TopoNodes> antList = topoService.getAntNodesByHostNum(unit.getHostNum(), false);
        antennaCount += antList.size(); // 天线个数累加
        if (!clientIsOnline) { // 如果设备不在线,标记为主机异常 & 天线故障数量=天线数量 &
          hostErrorCount++;
          hostErrorFlag = true;
          antErrorCount += antList.size();
          continue;
        }

        //    添加探针数据至缓存
        List<Probe> list = probeInfoService.list();
        for (int i = 0; i < list.size(); i++) {
          Probe probe = list.get(i);
          redisUtil.set(
              probe.getHostNumber() + probe.getNumber(),
              probe.getProbeStatus() + ":" + probe.getLost());
        }

        String hostNumber = unit.getHostNum();
        for (TopoNodes n : antList) {
          String probeSid = n.getProbeSid();
          if (StringUtils.isBlank(probeSid)) {
            antUnknownCount++;
          } else {
            //            LambdaQueryWrapper<Probe> lqwAnt1 = new LambdaQueryWrapper<Probe>();
            //            lqwAnt1.eq(Probe::getNumber, probeSid);
            //            lqwAnt1.eq(Probe::getHostNumber, hostNumber);
            //            Probe an = probeService.getOne(lqwAnt1);
            //            if (an == null) {
            //              antUnknownCount++;
            //            } else {
            //              if (StringUtils.isNoneBlank(an.getProbeStatus())
            //                  && an.getProbeStatus().equals("01")) {
            //                antSuccessCount++;
            //              } else if (StringUtils.isNoneBlank(an.getProbeStatus())
            //                  && an.getProbeStatus().equals("00")) {
            //                hostErrorFlag = true;
            //                antErrorCount++;
            //              } else {
            //                antUnknownCount++;
            //              }
            //            }
            String antKey = n.getHostNum() + probeSid;
            boolean hasKey = redisUtil.hasKey(antKey);
            if (!hasKey) {
              antUnknownCount++;
            } else {
              String result = (String) redisUtil.get(antKey);
              String[] split = result.split(":");
              String probeStatus = split[0];
              if ("00".equals(probeStatus)) {
                hostErrorFlag = true;
                antErrorCount++;
              } else if ("01".equals(probeStatus)) {
                antSuccessCount++;
              } else {
                antUnknownCount++;
              }
            }
          }
        }
      }

      if (hostErrorFlag) {
        hostErrSiteList.add(sinfo);
      }
    }

    Map<String, Object> res = new HashMap<>();

    res.put("siteCount", siteCount); // 室分车站总数
    res.put("hostCount", hostCount); // 监控主机总数
    res.put("antennaCount", antennaCount); // 天线总数
    res.put("probeCount", probeCount); // 探针总数
    res.put("hostErrorCount", hostErrorCount); // 主机异常总数
    res.put(
        "hostNormalCount",
        hostCount - hostErrorCount <= 0 ? 0 : hostCount - hostErrorCount); // 主机正常
    res.put("antErrorCount", antErrorCount); // 天线异常
    res.put("antOnlineCount", antSuccessCount); // 天线正常
    res.put("antUnknownCount", antUnknownCount); // 天线正常
    res.put("siteErrorList", hostErrSiteList); // 异常主机车站信息
    res.put("network", networkInfos);

    return Result.succeed(res, "信息获取完毕");
  }

  /** 根据车站的code查询出当前车站下的所有监控主机列表 */
  @PostMapping("/hostList/{stationId}")
  public Map<String, Object> getHostListByPid(@PathVariable("stationId") String stationId) {
    LambdaQueryWrapper<BelongUnit> lqw = new LambdaQueryWrapper<BelongUnit>();
    lqw.eq(BelongUnit::getStationId, stationId);
    // 获取车站下的监控主机列表
    List<BelongUnit> hostList = belongUnitService.list(lqw);

    Map<String, Object> result = new HashMap<>();
    result.put("hosts", hostList);
    return result;
  }

  private String deviceType(int type) {
    DeviceType[] values = DeviceType.values();
    for (DeviceType dt : values) {
      if (dt.getDeviceTypeCode() == type) {
        return dt.getDeviceTypeName();
      }
    }
    return "--";
  }
}
