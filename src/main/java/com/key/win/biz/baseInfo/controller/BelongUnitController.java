package com.key.win.biz.baseInfo.controller;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.model.Probe;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.biz.baseInfo.service.ProbeService;
import com.key.win.biz.topo.enums.DeviceStatus;
import com.key.win.biz.topo.enums.DeviceType;
import com.key.win.biz.topo.model.TopoNodes;
import com.key.win.biz.topo.service.TopoNodesService;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import com.key.win.config.RedisUtil;
import com.key.win.mqtt.Scheduling.config.runnable.DeviceCmdCallbackRunnable;
import com.key.win.mqtt.Scheduling.config.runnable.service.DeviceArgumentsCollectService;
import com.key.win.mqtt.Scheduling.config.runnable.service.ProbeFaultDiagnosisService;
import com.key.win.mqtt.Scheduling.config.runnable.service.ProbeReadingService;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.mqtt.mq.cmd.EmqRequestService;
import com.key.win.mqtt.mq.cmd.SettingCommandService;
import com.key.win.system.aop.annotation.LogType;
import com.key.win.system.aop.annotation.MqIsOnlineCheck;
import com.key.win.system.aop.annotation.ProbePreQuery;
import com.key.win.system.aop.annotation.SfLog;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/belongUnit")
public class BelongUnitController {

  @Resource private BelongUnitService belongUnitService;
  @Resource private EmqRequestService emqRequestService;
  @Resource private DeviceArgumentsCollectService deviceBaseInfoCheckService;
  @Resource private ProbeFaultDiagnosisService probeFaultDiagnosisService;
  @Resource private ProbeReadingService probeReadingService;
  @Resource private ProbeService probeService;
  @Resource private SettingCommandService SettingCommandService;
  @Resource private TopoNodesService topoNodesService;
  @Resource private RedisUtil redisUtil;
  @Resource private Environment environment;

  /**
   * @param pageRequest 分页请求参数
   * @return 监控主机分页查询列表
   */
  @PostMapping("/queryPageInfo")
  public PageResult<BelongUnit> pageBelongUnit(@RequestBody PageRequest<BelongUnit> pageRequest) {
    PageResult<BelongUnit> belongUnit = belongUnitService.getPageBelongUnit(pageRequest);

    List<BelongUnit> unitList = belongUnit.getData();
    for (int i = 0; i < unitList.size(); i++) {
      BelongUnit unit = unitList.get(i);
      // 监控主机编号
      String hostNum = unit.getHostNum();

      // 1.获取监控主机里面的所有天线信息
      LambdaQueryWrapper<Probe> lqw = new LambdaQueryWrapper<>();
      lqw.eq(Probe::getHostNumber, hostNum);
      List<Probe> probeList = probeService.list(lqw);

      // 2.获取画在TOPO图上得天线节点信息
      LambdaQueryWrapper<TopoNodes> lqwNodes = new LambdaQueryWrapper<>();
      lqwNodes.eq(TopoNodes::getPid, unit.getId());
      List<TopoNodes> nodeList = topoNodesService.list(lqwNodes);

      for (int j = 0; j < probeList.size(); j++) {
        Probe ant = probeList.get(j);
        for (int k = 0; k < nodeList.size(); k++) {
          if (StringUtils.isNotBlank(nodeList.get(k).getProbeSid())
              && nodeList.get(k).getProbeSid().equalsIgnoreCase(ant.getNumber())) {
            if (StringUtils.isNotBlank(ant.getProbeStatus()) && ant.getProbeStatus().equals("00")) {
              unit.setDiagnosisStatus("ERR");
              break;
            }
          }
        }
      }
    }
    return belongUnit;
  }

  /**
   * 保存或者更新监控主机信息
   *
   * @param belongUnit
   * @return
   */
  @SuppressWarnings("unchecked")
  @PostMapping("/saveOrUpdate")
  public Result<BelongUnit> saveOrUpdateBelongUnit(@RequestBody BelongUnit belongUnit) {
    // 主键 ID
    String id = belongUnit.getId();
    // 设备编号
    String hostNum = belongUnit.getHostNum();

    // 新增监控主机
    if (StringUtils.isBlank(id)) {
      LambdaQueryWrapper<BelongUnit> lqw = new LambdaQueryWrapper<BelongUnit>();
      lqw.eq(BelongUnit::getHostNum, hostNum);
      List<BelongUnit> list = belongUnitService.list(lqw);
      if (list != null && list.size() > 0) {
        // 存在编号相同的监控主机
        return Result.failed(new BelongUnit(), "监控主机编号重复");
      }
    }
    return belongUnitService.saveOrUpdateBelongUnit(belongUnit);
  }

  /**
   * 删除监控主机根据主键ID
   *
   * @param id
   * @return
   */
  @PostMapping("/deleteById/{id}")
  public Result deleteBelongUnit(@PathVariable("id") String id) {
    return belongUnitService.deleteMonitorHostCascadeByRowId(id);
  }

  @SfLog(actionName = "设备复位", module = "数据录入", logType = LogType.BizLog)
  @MqIsOnlineCheck(
      needCallback = false,
      cmdName = "设备复位",
      cmdType = GConfig.CMD_TYPE_DEVICE_REBOOT,
      validateCacheKey = GConfig.SERVICE_BASE_KEY)
  @PostMapping("/reboot/{deviceId}")
  public Result<String> systemRestartByDeviceId(@PathVariable("deviceId") String deviceId) {
    SettingCommandService.rebootSystem(deviceId);
    return Result.succeed("指令下发完毕,等待设备重启!");
  }

  /**
   * 设备参数信息同步
   *
   * @param deviceId
   * @return
   */
  @SfLog(actionName = "设备信息同步", module = "数据录入", logType = LogType.BizLog)
  @MqIsOnlineCheck(
      needCallback = true,
      callback = DeviceCmdCallbackRunnable.class,
      cmdName = "设备信息同步",
      cmdType = GConfig.CMD_TYPE_DEVICE_ARGUMENTINFO,
      validateCacheKey = GConfig.SERVICE_BASE_KEY)
  @PostMapping("/async/{deviceId}")
  public Result<String> deviceInfoAsync(@PathVariable("deviceId") String deviceId) {
    deviceBaseInfoCheckService.AutoCheck(deviceId);
    return Result.succeed("指令发送完毕,稍后请刷新本页面进行查看");
  }

  /**
   * 精确读取
   *
   * @param deviceId
   * @return
   */
  @SfLog(actionName = "路损查询", module = "数据录入", logType = LogType.BizLog)
  @ProbePreQuery(
      cmdName = "路损查询",
      cmdType = GConfig.CMD_TYPE_PATHLOSS,
      validateCacheKey = GConfig.SERVICE_BASE_KEY)
  @PostMapping("/reading/{deviceId}")
  public Result<String> reading(@PathVariable("deviceId") String deviceId) {
    probeReadingService.AutoCheck(deviceId);
    return Result.succeed("精确读取指令已下发!");
  }

  /**
   * 故障诊断
   *
   * @param deviceId
   * @return
   */
  @SfLog(actionName = "故障诊断", module = "数据录入", logType = LogType.BizLog)
  @ProbePreQuery(
      cmdName = "故障诊断",
      cmdType = GConfig.CMD_TYPE_FAULT,
      validateCacheKey = GConfig.SERVICE_BASE_KEY)
  @PostMapping("/faulting/{deviceId}")
  public Result<String> faulting(@PathVariable("deviceId") String deviceId) {
    log.info("开始执行故障诊断指令======>开启本次执行缓存,将缓存记录本次故诊记录:{}", DateUtil.now());

    // 是否开启探针的真实读取
    String probeRealRead = environment.getProperty("sf.cmd.probeRealRead.enable");
    boolean enable = Boolean.parseBoolean(probeRealRead);
    if (!enable) {
      LinkedHashMap<String, Object> probeStatusMap = new LinkedHashMap<>();
      probeStatusMap.put("init", "");
      redisUtil.hmset(
          GConfig.PROBE_CHECK_KEY + deviceId, probeStatusMap, GConfig.PROBE_CHECK_KEY_EXPIRED_TIME);
    }
    probeFaultDiagnosisService.AutoCheck(deviceId);
    return Result.succeed("故障诊断已下发!");
  }

  /**
   * 天线状态小眼睛图标的API请求
   *
   * @param deviceId 监控主机编号
   * @return 天线状态列表
   */
  @PostMapping("/antNodes/{deviceId}")
  public Result<List<TopoNodes>> antennaListByDeviceId(@PathVariable String deviceId) {

    boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + deviceId);

    LambdaQueryWrapper<TopoNodes> lqwNodes = new LambdaQueryWrapper<>();
    lqwNodes.eq(TopoNodes::getHostNum, deviceId);
    lqwNodes.orderByAsc(TopoNodes::getProbeSid);
    lqwNodes.and(
        lq ->
            lq.eq(TopoNodes::getType, DeviceType.TX_BG.getDeviceTypeCode())
                .or()
                .eq(TopoNodes::getType, DeviceType.TX_BZ.getDeviceTypeCode())
                .or()
                .eq(TopoNodes::getType, DeviceType.TX_DSZQ.getDeviceTypeCode())
                .or()
                .eq(TopoNodes::getType, DeviceType.TX_DXXD.getDeviceTypeCode())
                .or()
                .eq(TopoNodes::getType, DeviceType.TX_QT.getDeviceTypeCode())
                .or()
                .eq(TopoNodes::getType, DeviceType.TX_QXXD.getDeviceTypeCode()));
    List<TopoNodes> nodeList = topoNodesService.list(lqwNodes);

    LambdaQueryWrapper<Probe> lqwAnt = new LambdaQueryWrapper<Probe>();
    lqwAnt.eq(Probe::getHostNumber, deviceId);
    lqwAnt.orderByAsc(Probe::getNumber);
    List<Probe> ants = probeService.list(lqwAnt);
    LinkedHashMap<String, Probe> antsMap =
        ants.stream()
            .collect(
                Collectors.toMap(
                    Probe::getNumber, node -> node, (k1, k2) -> k2, LinkedHashMap::new));
    for (TopoNodes node : nodeList) {
      if (StringUtils.isBlank(node.getRfidlabel())) {
        node.setRfidlabel("--");
      }

      String name = node.getName();
      String probeSid = node.getProbeSid();
      if (StringUtils.isBlank(probeSid)) {
        node.setTypeName(deviceType(node.getType()));
        node.setState(DeviceStatus.gray.getState());
        node.setLoss(Double.POSITIVE_INFINITY);
        continue;
      }
      Probe probe = antsMap.get(probeSid);
      if (probe == null) {
        node.setTypeName(deviceType(node.getType()));
        node.setState(DeviceStatus.gray.getState());
        node.setLoss(Double.POSITIVE_INFINITY);
      } else {
        String probeStatus = probe.getProbeStatus();
        if (!clientIsOnline) {
          node.setState(DeviceStatus.gray.getState());
        } else {
          if (StringUtils.isBlank(probeStatus)) {
            node.setState(DeviceStatus.gray.getState());
          } else if (probeStatus.equals("00")) {
            node.setState(DeviceStatus.red.getState());
          } else if (probeStatus.equals("01")) {
            node.setState(DeviceStatus.green.getState());
          } else {
            node.setState(DeviceStatus.gray.getState());
          }
        }
        node.setTypeName(deviceType(node.getType()));
        node.setRfidlabel(probe.getCoding());
        node.setLoss(probe.getLost());
      }
    }
    return Result.succeed(nodeList, "获取天线信息完毕");
  }

  private String deviceType(int type) {
    DeviceType[] values = DeviceType.values();
    for (DeviceType dt : values) {
      if (dt.getDeviceTypeCode() == type) {
        return dt.getDeviceTypeName();
      }
    }
    return "--";
  }
}
