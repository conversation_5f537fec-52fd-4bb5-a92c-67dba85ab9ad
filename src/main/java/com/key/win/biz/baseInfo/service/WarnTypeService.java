package com.key.win.biz.baseInfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.key.win.biz.baseInfo.model.WarnType;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;

public interface WarnTypeService extends IService<WarnType> {

  PageResult<WarnType> getPageWarnType(PageRequest<WarnType> pageRequest);

  Result saveOrUpdateWarnType(WarnType warnType);

  Result deleteById(String id);

  Result queryById(String id);
}
