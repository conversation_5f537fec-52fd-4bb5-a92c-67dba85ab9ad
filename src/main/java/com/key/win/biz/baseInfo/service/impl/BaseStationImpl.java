package com.key.win.biz.baseInfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.key.win.biz.baseInfo.dao.BaseStationDao;
import com.key.win.biz.baseInfo.model.BaseStation;
import com.key.win.biz.baseInfo.service.BaseLineService;
import com.key.win.biz.baseInfo.service.BaseStationService;
import com.key.win.common.web.MybatiesPageServiceTemplate;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BaseStationImpl extends ServiceImpl<BaseStationDao, BaseStation>
    implements BaseStationService {

  @Resource private BaseLineService baseLineService;

  @Override
  public PageResult<BaseStation> getPageBaseStation(PageRequest<BaseStation> pageRequest) {
    MybatiesPageServiceTemplate<BaseStation, BaseStation> page =
        new MybatiesPageServiceTemplate<BaseStation, BaseStation>(this.baseMapper) {
          @Override
          protected Wrapper<BaseStation> constructWrapper(BaseStation baseStation) {
            LambdaQueryWrapper<BaseStation> lqw = new LambdaQueryWrapper<BaseStation>();
            List<String> lineCodeList = baseLineService.operationLineList();

            if (lineCodeList.size() == 0) {
              log.error("!!!!!!!!!数据库中没有启用的地铁线路，请检查数据库数据是否正确");
              lqw.eq(BaseStation::getStationName, "随便一个查询不出来的值");
              return lqw;
            } else {
              lqw.orderByAsc(BaseStation::getStationId);
              lqw.eq(BaseStation::getStationType, "1");
              lqw.in(BaseStation::getLineId, lineCodeList);
              if (baseStation == null) {
                return lqw;
              }

              if (StringUtils.isNotBlank(baseStation.getLineId())) {
                lqw.eq(BaseStation::getLineId, baseStation.getLineId());
              }
              if (StringUtils.isNotBlank(baseStation.getStationName())) {
                lqw.like(BaseStation::getStationName, baseStation.getStationName())
                    .or()
                    .eq(BaseStation::getStationId, baseStation.getStationName());
              }
              return lqw;
            }
          }
        };
    PageResult<BaseStation> dataSourceTestPageResult = page.doPagingQuery(pageRequest);
    return dataSourceTestPageResult;
  }

  @Override
  public Result<List<BaseStation>> queryById(String lineId) {
    try {
      if (StringUtils.isBlank(lineId)) {
        return Result.failed("lineId参数为空");
      }
      List<BaseStation> baseStationList =
          list(
              new LambdaQueryWrapper<BaseStation>()
                  .eq(BaseStation::getLineId, lineId)
                  .eq(BaseStation::getStationType, "1"));
      return Result.succeed(baseStationList, "查询成功");
    } catch (Exception e) {
      return Result.failed("网络异常");
    }
  }

  @Override
  public List<BaseStation> list() {
    LambdaQueryWrapper<BaseStation> lqw = new LambdaQueryWrapper<>();
    lqw.eq(BaseStation::getStationType, "1");
    lqw.orderByAsc(BaseStation::getStationId);
    return this.list(lqw);
  }
}
