package com.key.win.biz.baseInfo.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.key.win.biz.baseInfo.dao.WarnTypeDao;
import com.key.win.biz.baseInfo.model.WarnType;
import com.key.win.biz.baseInfo.service.WarnTypeService;
import com.key.win.common.web.MybatiesPageServiceTemplate;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;

@Service
public class WarnTypeImpl extends ServiceImpl<WarnTypeDao, WarnType> implements WarnTypeService {

  @Override
  public PageResult<WarnType> getPageWarnType(PageRequest<WarnType> pageRequest) {
    MybatiesPageServiceTemplate<WarnType, WarnType> page =
        new MybatiesPageServiceTemplate<WarnType, WarnType>(this.baseMapper) {
          @Override
          protected Wrapper<WarnType> constructWrapper(WarnType warnType) {
            LambdaQueryWrapper<WarnType> lqw = new LambdaQueryWrapper<WarnType>();
            if (warnType == null) {
              return lqw;
            }
            if (StringUtils.isNotBlank(warnType.getTypeName())) {
              lqw.like(WarnType::getTypeName, warnType.getTypeName());
            }
            return lqw;
          }
        };
    PageResult<WarnType> dataSourceTestPageResult = page.doPagingQuery(pageRequest);
    return dataSourceTestPageResult;
  }

  @Override
  public Result saveOrUpdateWarnType(WarnType warnType) {
    try {
      if (warnType == null) {
        return Result.failed("保存参数为空");
      }
      boolean saveOrUpdate = saveOrUpdate(warnType);
      if (saveOrUpdate) {
        return Result.succeed("保存或更新成功");
      } else {
        return Result.failed("保存或更新失败");
      }
    } catch (Exception e) {
      return Result.failed("网络异常");
    }
  }

  @Override
  public Result deleteById(String id) {
    try {
      if (StringUtils.isBlank(id)) {
        return Result.failed("id参数为空");
      }
      boolean deleteById = removeById(id);
      if (deleteById) {
        return Result.succeed("删除成功");
      } else {
        return Result.failed("删除失败");
      }
    } catch (Exception e) {
      return Result.failed("网络异常");
    }
  }

  @Override
  public Result<WarnType> queryById(String id) {
    try {
      if (StringUtils.isBlank(id)) {
        return Result.failed("id参数为空");
      }
      WarnType warnType = getById(id);
      if (warnType != null) {
        return Result.succeed(warnType, "查询成功");
      } else {
        return Result.failed("查询失败");
      }
    } catch (Exception e) {
      return Result.failed("网络异常");
    }
  }
}
