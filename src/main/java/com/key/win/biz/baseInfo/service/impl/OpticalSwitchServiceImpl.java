package com.key.win.biz.baseInfo.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.key.win.biz.alarm.model.HisAntLoss;
import com.key.win.biz.baseInfo.dao.OpticalSwitchDao;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.model.OpticalSwitch;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.biz.baseInfo.service.OpticalSwitchService;
import com.key.win.biz.baseInfo.vo.OpticalSwitchVo;
import com.key.win.common.web.MybatiesPageServiceTemplate;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.utils.BeanConvertUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;

/**
 * 光交换机 端口及收/发功率服务
 */
@Slf4j
@Service
public class OpticalSwitchServiceImpl extends ServiceImpl<OpticalSwitchDao, OpticalSwitch>
        implements OpticalSwitchService {

    @Resource
    private BelongUnitService belongUnitService;

    @Transactional
    @Override
    public Integer saveOrUpdateOptical(OpticalSwitchVo param){
        String deviceId = param.getHostNumber();
        if(StringUtils.isNotBlank(deviceId)){
            //查询主机相关信息
            LambdaQueryWrapper<BelongUnit> lqw = new LambdaQueryWrapper<>();
            lqw.eq(BelongUnit::getEnableFlag, true);
            lqw.eq(BelongUnit::getHostNum, deviceId);
            List<BelongUnit> list = belongUnitService.list(lqw);
            if(ObjectUtil.isNotEmpty(list) && list.size() >0){
                BelongUnit belongUnit = list.get(0);
                param.setBelongLineId(belongUnit.getLineId());
                param.setBelongLineName(belongUnit.getLineName());
                param.setBelongStationId(belongUnit.getStationId());
                param.setBelongStationName(belongUnit.getStationName());
            }else{
                log.error("设备主机信息查询不到数据～");
            }

            //查询设备关联的光功率信息是否存在
            LambdaQueryWrapper<OpticalSwitch> queryWrapper = new LambdaQueryWrapper<OpticalSwitch>();
            queryWrapper.eq(OpticalSwitch::getHostNumber, deviceId)
                    .eq(OpticalSwitch::getIsDeleted, 0);
            OpticalSwitch dbBean =  baseMapper.selectOne(queryWrapper);
            if(dbBean !=null ){
                BeanConvertUtils.copyProperties(param,dbBean);
                dbBean.setUpdateTime(new Date());
                log.info("update Optical data:{}",dbBean.toString());
                return baseMapper.updateById(dbBean);
            }else{
                OpticalSwitch newBean =new OpticalSwitch();
                BeanConvertUtils.copyProperties(param,newBean);
                return baseMapper.insert(newBean);
            }
        }else{
            log.error("hostNumber is null");
            return 0;
        }
    }

    /**
     * 查询光交换机光端口，光收发功率列表
     * @param pageRequest
     * @return
     */
    @Override
    public PageResult<OpticalSwitch> getPageList(PageRequest<OpticalSwitch> pageRequest){
        MybatiesPageServiceTemplate<OpticalSwitch, OpticalSwitch> page =
                new MybatiesPageServiceTemplate<OpticalSwitch, OpticalSwitch>(this.baseMapper) {
                    @Override
                    protected Wrapper<OpticalSwitch> constructWrapper(OpticalSwitch optical) {
                        LambdaQueryWrapper<OpticalSwitch> lqw = new LambdaQueryWrapper<OpticalSwitch>();
                        if (StringUtils.isNotBlank(optical.getBelongStationName())) {
                            lqw.and(
                                    lq ->
                                            lq.like(
                                                    OpticalSwitch::getBelongStationName,
                                                    optical.getBelongStationName())
                                                    .or()
                                                    .like(
                                                            OpticalSwitch::getBelongStationId,
                                                            optical.getBelongStationName()));
                        }
                        if (StringUtils.isNotBlank(optical.getHostNumber())) {
                            lqw.like(OpticalSwitch::getHostNumber, optical.getHostNumber());
                        }

                        if (StringUtils.isNotBlank(optical.getStartTime())
                                && StringUtils.isNotBlank(optical.getEndTime())) {
                            lqw.between(
                                    OpticalSwitch::getUpdateTime,
                                    optical.getStartTime()+" 00:00:00",
                                    optical.getEndTime()+" 23:59:59");
                        }
                        lqw.orderByDesc(OpticalSwitch::getUpdateTime);
                        return lqw;
                    }
                };
        PageResult<OpticalSwitch> dataSourceTestPageResult = page.doPagingQuery(pageRequest);
        return dataSourceTestPageResult;
    }
}
