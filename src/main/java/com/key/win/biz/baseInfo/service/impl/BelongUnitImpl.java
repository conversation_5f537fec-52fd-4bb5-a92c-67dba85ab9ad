package com.key.win.biz.baseInfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.key.win.biz.baseInfo.dao.BelongUnitDao;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.model.SiteInfo;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.biz.baseInfo.service.ProbeService;
import com.key.win.biz.baseInfo.service.SiteInfoService;
import com.key.win.biz.topo.dao.TopoLinksDao;
import com.key.win.biz.topo.dao.TopoNodesDao;
import com.key.win.biz.topo.model.TopoLinks;
import com.key.win.biz.topo.model.TopoNodes;
import com.key.win.common.web.MybatiesPageServiceTemplate;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import com.key.win.config.mqtt.MqttSubClient;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.mqtt.mq.cmd.EmqRequestService;
import com.key.win.mqtt.mq.cmd.SettingCommandService;
import java.util.List;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 所属单元 监测主机
 *
 * <AUTHOR>
 */
@Service
@SuppressWarnings("rawtypes")
public class BelongUnitImpl extends ServiceImpl<BelongUnitDao, BelongUnit>
    implements BelongUnitService {

  @Resource private EmqRequestService emqRequestService;
  @Resource private ProbeService probeService;
  @Resource private SettingCommandService settingCommandService;

  @Autowired private TopoLinksDao topoLinksDao;
  @Autowired private TopoNodesDao topoNodesDao;
  @Autowired private SiteInfoService siteInfoService;

  @Value("${sf.cmd.setting.faultDiagnosisCycle}")
  private boolean faultDiagnosisCycle = true;

  @Value("${sf.cmd.setting.faultDiagnosisTime}")
  private boolean faultDiagnosisTime = true;

  @Value("${sf.cmd.setting.faultDiagnosisNumber}")
  private boolean faultDiagnosisNumber = true;

  @Override
  public PageResult<BelongUnit> getPageBelongUnit(PageRequest<BelongUnit> pageRequest) {
    MybatiesPageServiceTemplate<BelongUnit, BelongUnit> page =
        new MybatiesPageServiceTemplate<BelongUnit, BelongUnit>(this.baseMapper) {
          @Override
          protected Wrapper<BelongUnit> constructWrapper(BelongUnit belongUnit) {
            LambdaQueryWrapper<BelongUnit> lqw = new LambdaQueryWrapper<BelongUnit>();
            if (belongUnit == null || StringUtils.isBlank(belongUnit.getPid())) {
              return lqw;
            }
            lqw.eq(BelongUnit::getPid, belongUnit.getPid());

            if (StringUtils.isNotBlank(belongUnit.getUnitName())) {
              lqw.like(BelongUnit::getUnitName, belongUnit.getUnitName());
            }

            if (StringUtils.isNotBlank(belongUnit.getHostNum())) {
              lqw.like(BelongUnit::getHostNum, belongUnit.getHostNum());
            }
            lqw.orderByAsc(BelongUnit::getId);
            return lqw;
          }
        };
    PageResult<BelongUnit> dataSourceTestPageResult = page.doPagingQuery(pageRequest);
    return dataSourceTestPageResult;
  }

  @Override
  public Result saveOrUpdateBelongUnit(BelongUnit belongUnit) {
    try {
      if (belongUnit == null) {
        return Result.failed("保存参数为空");
      }

      // 所属线路车站信息的主键id
      String pid = belongUnit.getPid();
      SiteInfo siteInfo = siteInfoService.getById(pid);
      if (siteInfo != null) {
        belongUnit.setLineId(siteInfo.getLineCode());
        belongUnit.setLineName(siteInfo.getLineName());
        belongUnit.setStationId(siteInfo.getStationCode());
        belongUnit.setStationName(siteInfo.getStationName());
      } else {
        return Result.failed("关联的室分站点信息已被移除,请刷新页面重试");
      }

      int waitTimeSecond = 2 * 1000;
      String topic = "reader_" + belongUnit.getHostNum();
      boolean clientIsOnline = emqRequestService.clientIsOnline(topic);
      if (clientIsOnline) {
        belongUnit.setTimer(waitTimeSecond);

        // 设置IP
        if (StringUtils.isNotBlank(belongUnit.getIpAddress())) {
          settingCommandService.setUpTheServer(belongUnit);
        }
        // 设置精准读取开关
        Boolean accurateReading = belongUnit.getAccurateReading();
        if (accurateReading != null && accurateReading) {
          settingCommandService.setAccurateReadingSwitch(belongUnit);
        }

        Boolean troubleshooting = belongUnit.getTroubleshooting();
        if (belongUnit.getTroubleshooting() != null && troubleshooting) {
          // 设置故诊开关
          settingCommandService.setFaultSwitch(belongUnit);
        }

        // 发射功率
        if (StringUtils.isNotBlank(belongUnit.getTransmitPower())) {
          settingCommandService.setTransmitPower(belongUnit);
        }

        // 频率步进
        if (StringUtils.isNotBlank(belongUnit.getFrequencyStep())) {
          settingCommandService.setFrequencyStep(belongUnit);
        }

        // 设置起始功率,结束功率
        if (StringUtils.isNotBlank(belongUnit.getStartPower())) {
          settingCommandService.setStartPower(belongUnit);
        }
        if (StringUtils.isNotBlank(belongUnit.getEndPower())) {
          settingCommandService.setEndPower(belongUnit);
        }
        //设置功率步进
        if (StringUtils.isNotBlank(belongUnit.getPowerStep())) {
          settingCommandService.setPowerStep(belongUnit);
        }
        // 设置开始结束频率
        if (StringUtils.isNotBlank(belongUnit.getStartFrequency())) {
          settingCommandService.setUpStartFrequency(belongUnit);
        }
        if (StringUtils.isNotBlank(belongUnit.getEndFrequency())) {
          settingCommandService.setUpEndFrequency(belongUnit);
        }

        // 精确读取修正值
        if (StringUtils.isNotBlank(belongUnit.getSourcePowerCorrection())) {
          settingCommandService.setAccuratelyReadTheCorrectionValue(belongUnit);
        }

        // 故障诊断周期与故障诊断时间 具有一样的功能（比如：诊断时间是1分钟，周期就是一分钟一次）
//        if (faultDiagnosisCycle) {
//          if (StringUtils.isNotBlank(belongUnit.getFaultDiagnosisCycle())) {
//            settingCommandService.setTheFaultDiagnosisCycle(belongUnit);
//          }
//        }
        // 故障诊断时间
        if (faultDiagnosisTime) {
          if (StringUtils.isNotBlank(belongUnit.getFaultDiagnosisTime())) {
            settingCommandService.setFaultDiagnosisTime(belongUnit);
          }
        }
        // 故障诊断次数
        if (faultDiagnosisNumber) {
          if (StringUtils.isNotBlank(belongUnit.getFaultDiagnosisNumber())) {
            settingCommandService.setFaultDiagnosisNumber(belongUnit);
          }
        }
        belongUnit.setDeviceStatus(GConfig.device_status_online);
      } else {
        belongUnit.setDeviceStatus(GConfig.device_status_offline);
      }
      boolean saveOrUpdate = saveOrUpdate(belongUnit);
      if (saveOrUpdate) {
        // 订阅新增的设备主机
        MqttSubClient.getInstance().subscribe(topic);
        if (clientIsOnline) {
          return Result.succeed(belongUnit, "数据库存储成功!");
        } else {
          return Result.succeed(belongUnit, "数据库存储成功!但当前设备[未在线]");
        }
      } else {
        return Result.failed("保存失败");
      }
    } catch (Exception e) {
      e.printStackTrace();
      return Result.failed("保存时异常:" + e.getMessage());
    }
  }

  /** 监控主机主键ID */
  @Override
  public Result deleteMonitorHostCascadeByRowId(String id) {
    try {
      if (StringUtils.isBlank(id)) {
        return Result.failed("传入监控主机主键ID为空");
      }

      BelongUnit dbUnit = getById(id);
      if (null == dbUnit) {
        return Result.failed("监控主机不存在,请刷新重试.");
      }

      String hostNum = dbUnit.getHostNum();

      // 删除监控主机
      boolean deleteFlag = removeById(id);
      if (deleteFlag) {
        // 1.删除成功后取消该监控主机的订阅
        MqttSubClient.getInstance().unSubscribe("reader_" + hostNum);
        // 2.移除所有的天线信息
        probeService.removeAllProbeByHostNumber(hostNum);
        // 3.移除topo图信息
        // 移除topoLinks信息
        LambdaQueryWrapper<TopoLinks> linksWrapper = new LambdaQueryWrapper<>();
        linksWrapper.in(TopoLinks::getPid, id);
        topoLinksDao.delete(linksWrapper);
        // 移除topoNode信息
        LambdaQueryWrapper<TopoNodes> nodeWrapper = new LambdaQueryWrapper<>();
        nodeWrapper.in(TopoNodes::getPid, id);
        topoNodesDao.delete(nodeWrapper);

        return Result.succeed("设备关联的信息已经完全移除成功");
      } else {
        return Result.failed("删除失败");
      }
    } catch (Exception e) {
      return Result.failed("监控主机删除失败");
    }
  }

  /** 根据室分站点ID查询站点下的所有监控主机 */
  @Override
  public Result<List<BelongUnit>> queryById(String pid) {
    try {
      if (StringUtils.isBlank(pid)) {
        return Result.failed("室分站点id不存在");
      }
      List<BelongUnit> belongUnitList =
          list(new LambdaQueryWrapper<BelongUnit>().eq(BelongUnit::getPid, pid));
      return Result.succeed(belongUnitList, "查询成功");
    } catch (Exception e) {
      return Result.failed("查询时异常:" + e.getMessage());
    }
  }

  @Override
  public PageResult<BelongUnit> getPageWarnConditions(PageRequest<BelongUnit> pageRequest) {
    MybatiesPageServiceTemplate<BelongUnit, BelongUnit> page =
        new MybatiesPageServiceTemplate<BelongUnit, BelongUnit>(this.baseMapper) {
          @Override
          protected Wrapper<BelongUnit> constructWrapper(BelongUnit belongUnit) {
            LambdaQueryWrapper<BelongUnit> lqw = new LambdaQueryWrapper<BelongUnit>();

            lqw.eq(BelongUnit::getHostType, "固定类型");
            if (belongUnit == null || StringUtils.isBlank(belongUnit.getPid())) {
              return lqw;
            }
            lqw.eq(BelongUnit::getPid, belongUnit.getPid());

            if (StringUtils.isNotBlank(belongUnit.getUnitName())) {
              lqw.like(BelongUnit::getUnitName, belongUnit.getUnitName());
            }

            if (StringUtils.isNotBlank(belongUnit.getHostNum())) {
              lqw.like(BelongUnit::getHostNum, belongUnit.getHostNum());
            }
            return lqw;
          }
        };
    PageResult<BelongUnit> dataSourceTestPageResult = page.doPagingQuery(pageRequest);
    return dataSourceTestPageResult;
  }

  @Override
  public Result online(String deviceId) {
    LambdaQueryWrapper<BelongUnit> lqw = new LambdaQueryWrapper<BelongUnit>();
    lqw.eq(BelongUnit::getHostNum, deviceId);

    BelongUnit dbUnit = getOne(lqw, true);
    if (null != dbUnit) {
      dbUnit.setDeviceStatus(GConfig.device_status_online);
      updateById(dbUnit);
      return Result.succeed("设备状态已更新");
    } else {
      return Result.succeed("设备状态更新失败");
    }
  }

  @Override
  public Result offline(String deviceId) {
    LambdaQueryWrapper<BelongUnit> lqw = new LambdaQueryWrapper<BelongUnit>();
    lqw.eq(BelongUnit::getHostNum, deviceId);

    BelongUnit dbUnit = getOne(lqw, true);
    if (null != dbUnit) {
      dbUnit.setDeviceStatus(GConfig.alarmType_deviceOffline);
      updateById(dbUnit);
      return Result.succeed("设备状态已更新");
    } else {
      return Result.succeed("设备暂不存在");
    }
  }

  @Override
  public boolean isBanByLineOrStation(String deviceId) {
    int deviceCount = baseMapper.isBanByLineIdOrStationId(deviceId);
    if (deviceCount > 0) {
      return false;
    }
    return true;
  }
}
