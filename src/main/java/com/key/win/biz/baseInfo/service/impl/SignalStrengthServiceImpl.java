package com.key.win.biz.baseInfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.baseInfo.model.BaseLine;
import com.key.win.biz.baseInfo.model.BaseStation;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.service.BaseLineService;
import com.key.win.biz.baseInfo.service.BaseStationService;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.biz.baseInfo.service.ISignalStrengthService;
import com.key.win.biz.topo.model.TopoNodes;
import com.key.win.biz.topo.service.TopoService;
import com.key.win.config.RedisUtil;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.utils.SpringUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SignalStrengthServiceImpl implements ISignalStrengthService {

  @Autowired private BaseLineService baseLineService;
  @Autowired private BaseStationService baseStationService;
  @Autowired private BelongUnitService belongUnitService;
  @Autowired private TopoService topoService;

  @Override
  public Map<String, Object> getDeviceTree() {

    Map<String, Object> deviceTreeMap = new LinkedHashMap<>();
    deviceTreeMap.put("label", "室分系统");
    List<Map<String, Object>> rootChildren = new ArrayList<>();

    LambdaQueryWrapper<BaseLine> lqwLine = new LambdaQueryWrapper<>();
    lqwLine.orderByAsc(BaseLine::getLineId);
    lqwLine.eq(BaseLine::getLineOperationState, "1");
    List<BaseLine> baseLineList = baseLineService.list(lqwLine);

    for (BaseLine line : baseLineList) {
      Map<String, Object> lineMap = new LinkedHashMap<>();
      String lineName = line.getLineName();
      String lineId = line.getLineId();
      lineMap.put("label", lineName);
      lineMap.put("lineId", lineId);

      LambdaQueryWrapper<BaseStation> lqwStation = new LambdaQueryWrapper<>();
      lqwStation.eq(BaseStation::getLineId, lineId);
      lqwStation.orderByAsc(BaseStation::getStationId);
      List<BaseStation> stationList = baseStationService.list(lqwStation);

      List<Map<String, Object>> lineChildren = new ArrayList<>();
      for (BaseStation station : stationList) {
        Map<String, Object> stationMap = new LinkedHashMap<>();
        String stationId = station.getStationId();
        String stationName = station.getStationName();
        stationMap.put("label", stationName);
        stationMap.put("stationId", stationId);

        LambdaQueryWrapper<BelongUnit> lqwUnit = new LambdaQueryWrapper<>();
        lqwUnit.eq(BelongUnit::getStationId, stationId);
        lqwUnit.eq(BelongUnit::getLineId, lineId);
        lqwUnit.orderByAsc(BelongUnit::getAddTime);
        List<BelongUnit> unitList = belongUnitService.list(lqwUnit);
        List<Map<String, Object>> unitChildren = new ArrayList<>();
        for (BelongUnit unit : unitList) {
          String hostNumber = unit.getHostNum();
          String pid = unit.getPid();
          Map<String, Object> unitMap = new LinkedHashMap<>();
          unitMap.put("label", hostNumber);
          unitMap.put("pid", pid);
          unitMap.put("lineId", lineId);
          unitMap.put("stationId", stationId);
          unitChildren.add(unitMap);
        }
        stationMap.put("children", unitChildren);
        lineChildren.add(stationMap);
      }
      lineMap.put("children", lineChildren);
      rootChildren.add(lineMap);
    }
    deviceTreeMap.put("children", rootChildren);
    return deviceTreeMap;
  }

  @Override
  public List<Map> antennaListByHostNumber(String hostNumber) {

    RedisUtil redisUtil = SpringUtils.getBean(RedisUtil.class);
    RedisTemplate<String, Object> redisTemplate = redisUtil.getRedisTemplate();

    Map<String, Object> signalStrengthMap = new HashMap<>();
    Set<String> keys = redisTemplate.keys(GConfig.probeKeyPathLoss + hostNumber + ":*");
    for (String key : keys) {
      String[] split = key.split(":");

      String sid = split[5];

      Set<Object> scores = redisTemplate.opsForZSet().rangeByScore(key, -1, 199);
      if (scores.size() > 0) {
        // 首先获取member为99的用户手动指定的值,如果没有获取到则获取最小那个值
        Double customScore = redisTemplate.opsForZSet().score(key, 99);
        if (customScore == null) {
          // 没有发现用户设置的值,则使用3次采集中最小的那个值
          customScore = redisTemplate.opsForZSet().score(key, scores.toArray()[0]);
        }
        signalStrengthMap.put(sid, customScore);
      }
    }

    List<Map> result = new ArrayList<Map>();
    List<TopoNodes> antTopoNodes = topoService.getAntNodesByHostNum(hostNumber, false);
    for (TopoNodes node : antTopoNodes) {
      Map<String, Object> nodeMap = new LinkedHashMap();
      nodeMap.put("antName", node.getName());
      nodeMap.put("sid", node.getProbeSid());
      nodeMap.put("rfidLabel", node.getRfidlabel());
      nodeMap.put("signalStrength", signalStrengthMap.get(node.getProbeSid()));
      result.add(nodeMap);
    }
    return result;
  }
}
