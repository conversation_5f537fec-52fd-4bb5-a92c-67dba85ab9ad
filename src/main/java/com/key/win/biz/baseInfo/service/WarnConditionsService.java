package com.key.win.biz.baseInfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.key.win.biz.baseInfo.model.WarnConditions;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;

public interface WarnConditionsService extends IService<WarnConditions> {

  PageResult<WarnConditions> getPageWarnConditions(PageRequest<WarnConditions> pageRequest);

  Result saveOrUpdateWarnConditions(WarnConditions warnConditions);

  Result deleteById(String id);

  Result<WarnConditions> queryById(String id);
}
