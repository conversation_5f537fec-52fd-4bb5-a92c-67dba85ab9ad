package com.key.win.biz.baseInfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.key.win.biz.baseInfo.dao.BelongUnitDao;
import com.key.win.biz.baseInfo.dao.SiteInfoDao;
import com.key.win.biz.baseInfo.model.BaseLine;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.model.SiteInfo;
import com.key.win.biz.baseInfo.service.BaseLineService;
import com.key.win.biz.baseInfo.service.SiteInfoService;
import com.key.win.common.result.ResultCode;
import com.key.win.common.web.MybatiesPageServiceTemplate;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description: 站点信息
 * @return:
 * @date: 2021/8/11 16:14
 */
@Service
public class SiteInfoImpl extends ServiceImpl<SiteInfoDao, SiteInfo> implements SiteInfoService {
  @Autowired private SiteInfoDao siteInfoDao;

  @Autowired private BelongUnitDao belongUnitDao;

  @Resource private BaseLineService baseLineService;

  @Override
  public PageResult<SiteInfo> getPageSiteInfo(PageRequest<SiteInfo> pageRequest) {
    MybatiesPageServiceTemplate<SiteInfo, SiteInfo> page =
        new MybatiesPageServiceTemplate<SiteInfo, SiteInfo>(this.baseMapper) {
          @Override
          protected Wrapper<SiteInfo> constructWrapper(SiteInfo siteInfo) {
            LambdaQueryWrapper<SiteInfo> lqw = new LambdaQueryWrapper<SiteInfo>();

            List<String> lineCodeList = baseLineService.operationLineList();
            if (lineCodeList.size() != 0) {
              lqw.in(SiteInfo::getLineCode, lineCodeList);
            } else {
              lqw.eq(SiteInfo::getLineCode, "随便一个值");
              return lqw;
            }
            if (siteInfo == null) {
              return lqw;
            }
            if (StringUtils.isNotBlank(siteInfo.getStationName())) {
              lqw.like(SiteInfo::getStationName, siteInfo.getStationName());
            }
            lqw.orderByAsc(SiteInfo::getStationCode);
            return lqw;
          }
        };
    PageResult<SiteInfo> dataSourceTestPageResult = page.doPagingQuery(pageRequest);
    return dataSourceTestPageResult;
  }

  @Override
  public Result saveOrUpdateSiteInfo(SiteInfo siteInfo) {
    try {
      if (siteInfo == null) {
        return Result.failed("保存参数为空");
      }
      LambdaQueryWrapper<SiteInfo> lqw = new LambdaQueryWrapper<>();
      if (StringUtils.isBlank(siteInfo.getStationCode())) {
        return Result.failed("站点编号不存在");
      }
      lqw.eq(SiteInfo::getStationCode, siteInfo.getStationCode());
      SiteInfo dbSiteInfo = siteInfoDao.selectOne(lqw);
      if (dbSiteInfo != null) {
        return Result.failed("车站已存在");
      }
      boolean saveOrUpdate = saveOrUpdate(siteInfo);
      if (saveOrUpdate) {
        return Result.succeed("保存或更新成功");
      } else {
        return Result.failed("保存或更新失败");
      }
    } catch (Exception e) {
      return Result.failed("网络异常");
    }
  }

  @Override
  public Result deleteById(String id) {
    try {
      if (StringUtils.isBlank(id)) {
        return Result.failed("id参数为空");
      }
      // 站点下存在主机需要先删除主机才可以删除站点
      LambdaQueryWrapper<BelongUnit> wrapper = new LambdaQueryWrapper<>();
      wrapper.eq(BelongUnit::getPid, id);
      List<BelongUnit> belongUnits = belongUnitDao.selectList(wrapper);
      if (CollectionUtils.isEmpty(belongUnits)) {
        boolean deleteById = removeById(id);
        if (deleteById) {
          return Result.succeed("删除成功");
        } else {
          return Result.failed("删除失败");
        }
      } else {
        return Result.failed(
            ResultCode.SF_BELONG_UNIT_DELETE_FILE.getCode(),
            ResultCode.SF_BELONG_UNIT_DELETE_FILE.getMessage());
      }
    } catch (Exception e) {
      return Result.failed("网络异常");
    }
  }

  @Override
  public Result<SiteInfo> querySiteInfoById(String id) {
    try {
      if (StringUtils.isBlank(id)) {
        return Result.failed("id参数为空");
      }
      SiteInfo siteInfo = getById(id);
      if (siteInfo == null) {
        return Result.succeed(siteInfo, "查询成功");
      } else {
        return Result.failed("查询失败");
      }
    } catch (Exception e) {
      return Result.failed("网络异常");
    }
  }

  @Override
  public List<SiteInfo> list(Wrapper<SiteInfo> queryWrapper) {
    return super.list(queryWrapper);
  }

  @Override
  public List<SiteInfo> list() {
    List<String> lineCodeList = new ArrayList<>();
    List<BaseLine> lineList = baseLineService.list();
    for (BaseLine line : lineList) {
      lineCodeList.add(line.getLineId());
    }

    if (lineCodeList.size() == 0) {
      return new ArrayList<>();
    }
    LambdaQueryWrapper<SiteInfo> lqwSiteInfo = new LambdaQueryWrapper<>();
    lqwSiteInfo.in(SiteInfo::getLineCode, lineCodeList);
    return list(lqwSiteInfo);
  }
}
