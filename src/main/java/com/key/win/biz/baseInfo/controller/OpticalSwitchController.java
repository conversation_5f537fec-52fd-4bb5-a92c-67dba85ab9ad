package com.key.win.biz.baseInfo.controller;

import com.key.win.biz.baseInfo.model.OpticalSwitch;
import com.key.win.biz.baseInfo.service.OpticalSwitchService;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/optical")
public class OpticalSwitchController {

    @Autowired
    private OpticalSwitchService opticalSwitchService;

    /**
     * 列表分页查询-光交换机光端口，光收发功率列表
     * @param pageRequest
     * @return
     */
    @PostMapping("/queryPageInfo")
    protected PageResult<OpticalSwitch> pageQuerySpticalList(
            @RequestBody PageRequest<OpticalSwitch> pageRequest) {
        return opticalSwitchService.getPageList(pageRequest);
    }

}
