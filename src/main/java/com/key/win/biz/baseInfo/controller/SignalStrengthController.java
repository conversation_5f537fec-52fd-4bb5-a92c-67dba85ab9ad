package com.key.win.biz.baseInfo.controller;

import cn.hutool.core.map.MapUtil;
import com.key.win.biz.baseInfo.service.ISignalStrengthService;
import com.key.win.common.web.Result;
import com.key.win.config.RedisUtil;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/** 信源强度手动设置 */
@RestController
@RequestMapping("/signalStrength/controller/**")
public class SignalStrengthController {

  @Autowired private ISignalStrengthService signalService;

  @Autowired private RedisUtil redisUtil;

  /**
   * 构建线路-车站-监控主机树结构
   *
   * @return 结构树Tree数据
   */
  @PostMapping("/deviceTree")
  public List<Map<String, Object>> deviceTree() {
    List<Map<String, Object>> data = new ArrayList<>();
    Map<String, Object> deviceTree = signalService.getDeviceTree();
    data.add(deviceTree);
    return data;
  }

  /**
   * 根据监控主机的编号查询监控主机下的所有天线节点信源强度的配置信息
   *
   * @param hostNumber 监控主机编号
   * @return 监控主机下的所有天线节点信源强度
   */
  @PostMapping("/antennaSignalStrengthList/{hostNumber}")
  public List<Map> antennaSignalStrengthList(@PathVariable String hostNumber) {
    List<Map> antSignalStrength = signalService.antennaListByHostNumber(hostNumber);
    return antSignalStrength;
  }

  @PostMapping("/update/signalStrength")
  public Result updateSignalStrength(@RequestBody Map<String, Object> param) {

    String hostNumber = MapUtil.getStr(param, "hostNumber");
    String sid = MapUtil.getStr(param, "sid");
    double signalStrength = MapUtil.getDouble(param, "signalStrength");

    String key = GConfig.probeKeyPathLoss + hostNumber + ":" + sid;
    Boolean flag = redisUtil.getRedisTemplate().opsForZSet().add(key, 99, signalStrength);
    if (flag) {
      return Result.succeed("修改成功");
    } else {
      return Result.failed("修改失败");
    }
  }
}
