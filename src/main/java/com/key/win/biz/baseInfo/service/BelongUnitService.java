package com.key.win.biz.baseInfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import java.util.List;

public interface BelongUnitService extends IService<BelongUnit> {

  PageResult<BelongUnit> getPageBelongUnit(PageRequest<BelongUnit> pageRequest);

  Result saveOrUpdateBelongUnit(BelongUnit belongUnit);

  /**
   * 根据监控主机自身的主键ID级联删除所有的信息，包含探针、拓扑图
   *
   * @param id 监控主机自身id
   * @return 删除返回
   */
  Result deleteMonitorHostCascadeByRowId(String id);

  Result<List<BelongUnit>> queryById(String pid);

  PageResult<BelongUnit> getPageWarnConditions(PageRequest<BelongUnit> pageRequest);

  Result online(String deviceId);

  Result offline(String deviceId);

  /**
   * 当前传入的设备编号是否所属线路或者车站被禁用
   *
   * @param deviceId 设备编号
   * @return
   */
  boolean isBanByLineOrStation(String deviceId);
}
