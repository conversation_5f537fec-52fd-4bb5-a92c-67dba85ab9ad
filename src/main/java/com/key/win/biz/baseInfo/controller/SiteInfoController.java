package com.key.win.biz.baseInfo.controller;

import com.key.win.biz.baseInfo.model.SiteInfo;
import com.key.win.biz.baseInfo.service.SiteInfoService;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description:
 * @date: 2021/8/26 14:10
 */
@RestController
@RequestMapping("/siteInfo")
public class SiteInfoController {

  @Resource private SiteInfoService siteInfoService;

  @PostMapping("/queryPageInfo")
  public PageResult<SiteInfo> pageSiteInfo(@RequestBody PageRequest<SiteInfo> pageRequest) {
    return siteInfoService.getPageSiteInfo(pageRequest);
  }

  @PostMapping("/saveOrUpdate")
  public Result saveOrUpdateSiteInfo(@RequestBody SiteInfo siteInfo) {
    return siteInfoService.saveOrUpdateSiteInfo(siteInfo);
  }

  @PostMapping("/deleteById/{id}")
  public Result deleteSiteInfo(@PathVariable("id") String id) {
    return siteInfoService.deleteById(id);
  }

  @PostMapping("/queryById/{id}")
  public Result<SiteInfo> queryById(@PathVariable("id") String id) {
    return siteInfoService.querySiteInfoById(id);
  }
}
