package com.key.win.biz.baseInfo.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.key.win.common.web.MybatisID;
import javax.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("warn_grade")
@Entity(name = "warn_grade")
public class WarnGrade extends MybatisID {

  private String gradeNumber;

  private String gradeName;

  private String remark;

  private String attr1;

  private String attr2;

  private String attr3;
}
