package com.key.win.biz.baseInfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.key.win.biz.baseInfo.model.WarnGrade;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;

public interface WarnGradeService extends IService<WarnGrade> {

  PageResult<WarnGrade> getPageWarnGrade(PageRequest<WarnGrade> pageRequest);

  Result saveOrUpdateWarnGrade(WarnGrade warnGrade);

  Result deleteById(String id);

  Result<WarnGrade> queryById(String id);
}
