package com.key.win.biz.baseInfo.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.key.win.biz.baseInfo.model.BaseStation;
import com.key.win.biz.baseInfo.service.BaseStationService;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;

@RestController
@RequestMapping("/station")
public class BaseStationController {

  @Resource private BaseStationService baseStationService;

  @PostMapping("/queryPageInfo")
  public PageResult<BaseStation> pageBaseStation(
      @RequestBody PageRequest<BaseStation> pageRequest) {
    return baseStationService.getPageBaseStation(pageRequest);
  }

  @PostMapping("/getById/{lineId}")
  public Result<List<BaseStation>> queryById(@PathVariable("lineId") String lineId) {
    return baseStationService.queryById(lineId);
  }
}
