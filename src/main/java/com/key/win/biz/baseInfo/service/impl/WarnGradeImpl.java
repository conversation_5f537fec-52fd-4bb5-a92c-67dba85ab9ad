package com.key.win.biz.baseInfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.key.win.biz.baseInfo.dao.WarnGradeDao;
import com.key.win.biz.baseInfo.model.WarnGrade;
import com.key.win.biz.baseInfo.service.WarnGradeService;
import com.key.win.common.web.MybatiesPageServiceTemplate;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class WarnGradeImpl extends ServiceImpl<WarnGradeDao, WarnGrade>
    implements WarnGradeService {

  @Override
  public PageResult<WarnGrade> getPageWarnGrade(PageRequest<WarnGrade> pageRequest) {
    MybatiesPageServiceTemplate<WarnGrade, WarnGrade> page =
        new MybatiesPageServiceTemplate<WarnGrade, WarnGrade>(this.baseMapper) {
          @Override
          protected Wrapper<WarnGrade> constructWrapper(WarnGrade warnGrade) {
            LambdaQueryWrapper<WarnGrade> lqw = new LambdaQueryWrapper<WarnGrade>();
            if (warnGrade == null) {
              return lqw;
            }
            if (StringUtils.isNotBlank(warnGrade.getGradeName())) {
              lqw.like(WarnGrade::getGradeName, warnGrade.getGradeName());
            }
            return lqw;
          }
        };
    PageResult<WarnGrade> dataSourceTestPageResult = page.doPagingQuery(pageRequest);
    return dataSourceTestPageResult;
  }

  @Override
  public Result saveOrUpdateWarnGrade(WarnGrade warnGrade) {
    try {
      if (warnGrade == null) {
        return Result.failed("保存参数为空");
      }
      boolean saveOrUpdate = saveOrUpdate(warnGrade);
      if (saveOrUpdate) {
        return Result.succeed("保存或更新成功 ");
      } else {
        return Result.failed("保存或更新失败");
      }
    } catch (Exception e) {
      return Result.failed("网络异常");
    }
  }

  @Override
  public Result deleteById(String id) {
    try {
      if (StringUtils.isBlank(id)) {
        return Result.failed("id参数为空");
      }
      boolean deleteById = removeById(id);
      if (deleteById) {
        return Result.succeed("删除成功");
      } else {
        return Result.failed("删除失败");
      }
    } catch (Exception e) {
      return Result.failed("网络异常");
    }
  }

  @Override
  public Result<WarnGrade> queryById(String id) {
    try {
      if (StringUtils.isBlank(id)) {
        return Result.failed("id参数为空");
      }
      WarnGrade warnGrade = getById(id);
      if (warnGrade != null) {
        return Result.succeed(warnGrade, "查询成功");
      } else {
        return Result.failed("查询失败");
      }
    } catch (Exception e) {
      return Result.failed("网络异常");
    }
  }
}
