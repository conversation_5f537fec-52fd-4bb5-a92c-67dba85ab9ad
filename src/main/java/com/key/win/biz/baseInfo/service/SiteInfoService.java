package com.key.win.biz.baseInfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.key.win.biz.baseInfo.model.SiteInfo;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import java.util.List;

public interface SiteInfoService extends IService<SiteInfo> {

  public List<SiteInfo> list();

  PageResult<SiteInfo> getPageSiteInfo(PageRequest<SiteInfo> pageRequest);

  Result saveOrUpdateSiteInfo(SiteInfo siteInfo);

  Result deleteById(String id);

  Result<SiteInfo> querySiteInfoById(String id);
}
