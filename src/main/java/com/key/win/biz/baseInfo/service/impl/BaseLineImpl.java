package com.key.win.biz.baseInfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.key.win.biz.baseInfo.dao.BaseLineDao;
import com.key.win.biz.baseInfo.model.BaseLine;
import com.key.win.biz.baseInfo.service.BaseLineService;
import com.key.win.common.web.MybatiesPageServiceTemplate;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class BaseLineImpl extends ServiceImpl<BaseLineDao, BaseLine> implements BaseLineService {

  @Override
  public PageResult<BaseLine> getPageBaseLine(PageRequest<BaseLine> pageRequest) {
    MybatiesPageServiceTemplate<BaseLine, BaseLine> page =
        new MybatiesPageServiceTemplate<BaseLine, BaseLine>(this.baseMapper) {
          @Override
          protected Wrapper<BaseLine> constructWrapper(BaseLine baseLine) {
            LambdaQueryWrapper<BaseLine> lqw = new LambdaQueryWrapper<BaseLine>();
            lqw.orderByAsc(BaseLine::getLineId);
            if (baseLine == null) {
              lqw = new LambdaQueryWrapper<BaseLine>();
            }
            if (StringUtils.isNotBlank(baseLine.getLineName())) {
              lqw.like(BaseLine::getLineName, baseLine.getLineName());
            }
            lqw.eq(BaseLine::getLineOperationState, 1);
            return lqw;
          }
        };
    PageResult<BaseLine> dataSourceTestPageResult = page.doPagingQuery(pageRequest);
    return dataSourceTestPageResult;
  }

  @Override
  public Result<List<BaseLine>> getBaseLineList() {
    LambdaQueryWrapper<BaseLine> lqw = new LambdaQueryWrapper<BaseLine>();
    lqw.orderByAsc(BaseLine::getLineId);
    lqw.eq(BaseLine::getLineOperationState, 1);
    List<BaseLine> baseLineList = this.list(lqw);
    return Result.succeed(baseLineList, "查询成功");
  }

  public List<BaseLine> list() {
    LambdaQueryWrapper<BaseLine> lqw = new LambdaQueryWrapper<>();
    lqw.eq(BaseLine::getLineOperationState, "1");
    return this.list(lqw);
  }

  @Override
  public List<String> operationLineList() {
    List<String> lineCodeList = new ArrayList<>();
    List<BaseLine> lineList = this.list();
    for (BaseLine line : lineList) {
      lineCodeList.add(line.getLineId());
    }
    return lineCodeList;
  }
}
