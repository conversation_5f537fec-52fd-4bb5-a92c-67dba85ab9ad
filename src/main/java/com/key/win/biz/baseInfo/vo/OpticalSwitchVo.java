package com.key.win.biz.baseInfo.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class OpticalSwitchVo implements Serializable {

    private String port1;

    private String port2;

    private String port3;

    private String port4;

    private String port5;

    private String port6;

    private String port7;

    private String port8;

    private String receivePower1;
    private String outputPower1;

    private String receivePower2;
    private String outputPower2;

    private String receivePower3;
    private String outputPower3;

    private String receivePower4;
    private String outputPower4;

    private String receivePower5;
    private String outputPower5;

    private String receivePower6;
    private String outputPower6;

    private String receivePower7;
    private String outputPower7;

    private String receivePower8;
    private String outputPower8;

    private String belongLineId; //线路Id

    private String belongLineName;//线路名称

    private String belongStationId;//'车站ID编码'

    private String belongStationName;//'车站名称'

    private String hostNumber; //'设备主机编码'

}
