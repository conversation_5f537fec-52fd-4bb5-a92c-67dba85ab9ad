package com.key.win.biz.baseInfo.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.key.win.biz.baseInfo.dao.ProbeDao;
import com.key.win.biz.baseInfo.model.Probe;
import com.key.win.biz.baseInfo.service.ProbeService;
import com.key.win.biz.baseInfo.vo.AntennaVO;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/10/29 11:28
 */
@Service
public class ProbeServiceImpl extends ServiceImpl<ProbeDao, Probe> implements ProbeService {

  @Resource private ProbeDao probeDao;

  @Override
  public void removeAllProbeByHostNumber(String hostNumber) {
    probeDao.removeAllProbeByHostNumber(hostNumber);
  }

  @Override
  public List<AntennaVO> antListByHostNumber(String hostNumber) {
    return probeDao.antListByHostNumber(hostNumber);
  }
}
