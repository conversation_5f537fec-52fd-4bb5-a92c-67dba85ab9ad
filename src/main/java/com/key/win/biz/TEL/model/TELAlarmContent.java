package com.key.win.biz.TEL.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.Id;
import lombok.Data;

@Data
@TableName("TEL_ALARM_CONTENT")
@Entity(name = "TEL_ALARM_CONTENT")
public class TELAlarmContent implements Serializable {

  @Id @TableId private String id;
  private String content;
  private String alarmGrade;
  /** 影响范围 */
  private String reach;

  private String alarmReason;
  /** 处理建议 */
  private String HandlingSuggestions;
}
