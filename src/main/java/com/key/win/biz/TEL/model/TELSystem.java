package com.key.win.biz.TEL.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.Id;
import lombok.Data;

@Data
@TableName("TEL_SYSTEM")
@Entity(name = "TEL_SYSTEM")
public class TELSystem implements Serializable {

  @Id @TableId private String id;
  private String subSystemName;
  private String ip;
  private String port;
  private String remark;
}
