package com.key.win.biz.TEL.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.Id;
import lombok.Data;

@Data
@TableName("TEL_STATION")
@Entity(name = "TEL_STATION")
public class TELStation implements Serializable {

  @Id @TableId private Integer id;
  private String stationName;

  private String stationCode;

  private String remark;
}
