package com.key.win.biz.TEL.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.Id;
import lombok.Data;

@Data
@Entity(name = "TEL_DEVICE")
@TableName("TEL_DEVICE")
public class TELDevice implements Serializable {

  @Id @TableId private String id;

  private String deviceName;
}
