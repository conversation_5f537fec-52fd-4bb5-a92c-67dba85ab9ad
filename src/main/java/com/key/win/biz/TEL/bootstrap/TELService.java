package com.key.win.biz.TEL.bootstrap;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.TEL.model.*;
import com.key.win.biz.TEL.service.*;
import com.key.win.biz.alarm.model.RealTimeWarn;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.biz.topo.service.TopoNodesService;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.utils.SpringUtils;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TELService implements InitializingBean {

  public static final String reportMsgFF = "FF";
  public static final String reportMsg00 = "00";
  public static String subSystemCode = "99";
  private static final Map<String, String> telAlarmContentMap = new HashMap<>();
  private static final Map<String, String> telAlarmTypeMap = new HashMap<>();
  private static final Map<String, String> telDeviceMap = new HashMap<>();
  private static final Map<String, Integer> telStationMap = new HashMap<>();
  private static Map<String,Map<String, String>> antNodeMap = new HashMap<>();

  @Autowired private TELDeviceService telDeviceService;
  @Autowired private TELStationService telStationService;
  @Autowired private SpringUtils springUtils;

  public static Map<String, String> getTelAlarmContentMap() {
    return telAlarmContentMap;
  }

  public static Map<String, String> getTelAlarmTypeMap() {
    return telAlarmTypeMap;
  }

  public static Map<String, String> getTelDeviceMap() {
    return telDeviceMap;
  }

  public static Map<String, Integer> getTelStationMap() {
    return telStationMap;
  }

  public static Map<String,Map<String, String>> getAntNodeMap() {
    return antNodeMap;
  }

  public static String getAntSidByHostNumAndAntName(String hostNum,String antName) {
    Map<String, String> antMap = antNodeMap.get(hostNum);
    if (antMap != null) {
      String sid = antMap.get(antName);
      if (StringUtils.isNotBlank(sid)) {
        return sid;
      }
    }
    return "";
  }


  public static String packageMsgAsync(
      BelongUnitService belongUnitService, RealTimeWarn warn, int size) {
    String belongStationId = warn.getBelongStationId();
    int telStationID = TELService.getTelStationMap().get(belongStationId);


    String deviceType = warn.getEquipmentType();
    String alarmDeviceType = TELService.getTelAlarmTypeMap().get(deviceType);
    if (StringUtils.isBlank(alarmDeviceType)) {
      return null;
    }
    int alarmDeviceTypeInt = Integer.parseInt(alarmDeviceType);

    String hostNumber = warn.getHostNumber();
    String networkName = warn.getNetworkName();
    String belongStationName = warn.getBelongStationName();
    List<BelongUnit> hostList =
        belongUnitService.list(
            new LambdaQueryWrapper<BelongUnit>().eq(BelongUnit::getStationId, belongStationId));
    Map<String, Integer> hostListMap = new HashMap<>();
    for (int j = 0; j < hostList.size(); j++) {
      BelongUnit belongUnit = hostList.get(j);
      hostListMap.put(belongUnit.getHostNum(), (j + 1));
    }
    Integer order = hostListMap.get(hostNumber);
    if (order == null) {
      return null;
    }
    int telDeviceCode = order;
    if (deviceType.equals(GConfig.equipmentType_Ant)) {
      String sid = TELService.getAntSidByHostNumAndAntName(hostNumber, networkName); // 根据天线名称获取探针的序号
      String antNumber = "";
        if (StringUtils.isNotBlank(sid)) {
            antNumber = sid;
            log.info("TEL: 车站:{}-{}-{},主机编号:{}-{},天线名称:{},集中告警天线id:{}",belongStationName, belongStationId,telStationID, hostNumber, telDeviceCode ,networkName, sid);
        } else {
            log.info("TEL: 车站:{}-{}-{},主机编号:{}-{},天线名称:{} 对应的天线Topo配置中的探针序号不存在,跳过报文构建!!!!",belongStationName, belongStationId,telStationID, hostNumber, telDeviceCode ,networkName);
            return "";
        }
      telDeviceCode = order * 100 + Integer.parseInt(antNumber);
    }
    String alarmContentType = warn.getAlarmType();
    String alarmContentCode = TELService.getTelAlarmContentMap().get(alarmContentType); // 告警编码
    int alarmCodeInt = Integer.parseInt(alarmContentCode); // 告警等级

    String alarmTime = warn.getAlarmTime();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    LocalDateTime dateTime = LocalDateTime.parse(alarmTime, formatter);
    int year = dateTime.getYear();
    String year1 = String.valueOf(year).substring(0, 2);
    String year2 = String.valueOf(year).substring(2, 4);
    String hexAlarmTime =
        String.format(
            "%02X%02X%02X%02X%02X%02X%02X",
            Integer.parseInt(year1),
            Integer.parseInt(year2),
            dateTime.getMonthValue(),
            dateTime.getDayOfMonth(),
            dateTime.getHour(),
            dateTime.getMinute(),
            dateTime.getSecond());

    String msg =
        "7E7E7E"
            + TELService.subSystemCode // 子系统code
            + String.format("%02X", telStationID) // 车站code
            + "00000000" // 机架号,没有用0填充
            + String.format("%04X", telDeviceCode) // 设备编号
            + String.format("%02X", alarmDeviceTypeInt) // 告警设备类型
            + TELService.reportMsgFF // 故常产生上报
            + String.format("%04X", alarmCodeInt) // 告警编号|告警内容编号
            + String.format("%04X", (size)) // 告警序号
            + hexAlarmTime // 告警时间
            + "0000"; // 填充
    msg = msg.replace(" ", "");

    List<String> hexList = new ArrayList<>();
    for (int k = 0; k < msg.length(); k += 2) {
      hexList.add(msg.substring(k, k + 2));
    }
    int sum = 0;
    for (String hex : hexList) {
      int num = Integer.parseInt(hex, 16);
      sum += num;
    }
    int lowByte = sum & 0xFF;
    String validateCode = String.format("%02X", lowByte);

    msg = msg + validateCode;
    log.info("完整的上报报文:{}", msg);
    return msg;
  }

  /**
   * 主动上报打包报文
   *
   * @param belongUnitService
   * @param warn
   * @param size
   * @return
   */
  public static String packageMsgProactiveReporting(
      BelongUnitService belongUnitService, RealTimeWarn warn, int size, String reportType) {
    if (warn == null) {
      return "";
    }

    String belongStationId = warn.getBelongStationId();
    Map<String, Integer> stationMap = TELService.getTelStationMap();
    Integer telStationID = MapUtil.getInt(stationMap, belongStationId);
    if (telStationID == null) {
      log.info("未匹配到TEL点表所归属信息!!点表所属车站ID:{},请检查数据库中:TEL_为前缀的数据表!!!", belongStationId);
      return null;
    }

    String deviceType = warn.getEquipmentType();
    String alarmDeviceType = TELService.getTelAlarmTypeMap().get(deviceType);
    if (StringUtils.isBlank(alarmDeviceType)) {
      return null;
    }
    int alarmDeviceTypeInt = Integer.parseInt(alarmDeviceType);

    String hostNumber = warn.getHostNumber();
    String networkName = warn.getNetworkName();
    String belongStationName = warn.getBelongStationName();
    List<BelongUnit> hostList =
        belongUnitService.list(
            new LambdaQueryWrapper<BelongUnit>().eq(BelongUnit::getStationId, belongStationId));
    Map<String, Integer> hostListMap = new HashMap<>();
    for (int j = 0; j < hostList.size(); j++) {
      BelongUnit belongUnit = hostList.get(j);
      hostListMap.put(belongUnit.getHostNum(), (j + 1));
    }
    Integer order = hostListMap.get(hostNumber);
    if (order == null) {
      return null;
    }
    int telDeviceCode = order;
    if (deviceType.equals(GConfig.equipmentType_Ant)) {
        String sid = TELService.getAntSidByHostNumAndAntName(hostNumber, networkName); // 根据天线名称获取探针的序号
        String antNumber = "";
        if (StringUtils.isNotBlank(sid)) {
            antNumber = sid;
            log.info("TEL: 车站:{}-{}-{},主机编号:{}-{},天线名称:{},集中告警天线id:{}",belongStationName, belongStationId,telStationID, hostNumber, telDeviceCode ,networkName, sid);
        } else {
            log.info("TEL: 车站:{}-{}-{},主机编号:{}-{},天线名称:{} 对应的天线Topo配置中的探针序号不存在,跳过报文构建!!!!",belongStationName, belongStationId,telStationID, hostNumber, telDeviceCode ,networkName);
            return "";
        }
        telDeviceCode = order * 100 + Integer.parseInt(antNumber);
    }

    String alarmContentType = warn.getAlarmType();
    String alarmContentCode = TELService.getTelAlarmContentMap().get(alarmContentType); // 告警编码
    int alarmCodeInt = Integer.parseInt(alarmContentCode); // 告警等级

    String alarmTime = warn.getAlarmTime();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    LocalDateTime dateTime = LocalDateTime.parse(alarmTime, formatter);
    int year = dateTime.getYear();
    String year1 = String.valueOf(year).substring(0, 2);
    String year2 = String.valueOf(year).substring(2, 4);
    String hexAlarmTime =
        String.format(
            "%02X%02X%02X%02X%02X%02X%02X",
            Integer.parseInt(year1),
            Integer.parseInt(year2),
            dateTime.getMonthValue(),
            dateTime.getDayOfMonth(),
            dateTime.getHour(),
            dateTime.getMinute(),
            dateTime.getSecond());

    String msg =
        "7E7E7E"
            + TELService.subSystemCode // 子系统code
            + String.format("%02X", telStationID) // 车站code
            + "00000000" // 机架号,没有用0填充
            + String.format("%04X", telDeviceCode) // 设备编号
            + String.format("%02X", alarmDeviceTypeInt) // 告警设备类型
            + reportType // 故常产生类型 00恢复  FF产生
            + String.format("%04X", alarmCodeInt) // 告警编号|告警内容编号
            + String.format("%04X", (size)) // 告警序号
            + hexAlarmTime // 告警时间
            + "0000"; // 填充
    msg = msg.replace(" ", "");

    List<String> hexList = new ArrayList<>();
    for (int k = 0; k < msg.length(); k += 2) {
      hexList.add(msg.substring(k, k + 2));
    }
    int sum = 0;
    for (String hex : hexList) {
      int num = Integer.parseInt(hex, 16);
      sum += num;
    }
    int lowByte = sum & 0xFF;
    String validateCode = String.format("%02X", lowByte);

    msg = msg + validateCode;
    log.info("完整的上报报文:{}", msg);
    return msg;
  }

  @Override
  public void afterPropertiesSet() {
    Environment evn = springUtils.getBean(Environment.class);
    subSystemCode = evn.getProperty("sf.tel.systemCode");
    log.info("TELService SystemCode为:{}", subSystemCode);

    telAlarmContentMap.put("设备离线", "1");
    telAlarmContentMap.put("天线异常", "2");
    telAlarmContentMap.put("链路异常", "3");
    telAlarmContentMap.put("信源偏弱", "4");

    telAlarmTypeMap.put("固定主机", "1");
    telAlarmTypeMap.put("天线", "2");

    List<TELDevice> devicesList = telDeviceService.list();
    devicesList.forEach(
        (x) -> {
          telDeviceMap.put(x.getId(), x.getId());
        });

    List<TELStation> stationList = telStationService.list();
    stationList.forEach(
        (x) -> {
          if (!StringUtils.isBlank(x.getStationCode())) {
            telStationMap.put(x.getStationCode(), x.getId());
          }
        });
  }
}
