package com.key.win.biz.dashboard.statistic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.key.win.biz.baseInfo.model.BaseLine;
import com.key.win.biz.dashboard.statistic.vo.DeviceInfoVo;

import java.util.List;

public interface DeviceStateService extends IService<DeviceInfoVo> {
  List<DeviceInfoVo> getDeviceListSort();

  List<DeviceInfoVo> queryDeviceListSortByStationId(String stationId);

  Boolean queryDeviceListById(String deviceId);
}
