package com.key.win.biz.dashboard.statistic.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.key.win.biz.dashboard.statistic.dao.DeviceStateDao;
import com.key.win.biz.dashboard.statistic.service.DeviceStateService;
import com.key.win.biz.dashboard.statistic.vo.DeviceInfoVo;
import com.sun.org.apache.xpath.internal.operations.Bool;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class DeviceStateServiceImpl extends ServiceImpl<DeviceStateDao, DeviceInfoVo>
    implements DeviceStateService {
  @Resource private DeviceStateDao deviceStateDao;

  @Override
  public List<DeviceInfoVo> getDeviceListSort() {
    List<DeviceInfoVo> deviceInfoVos = deviceStateDao.queryDeviceListSort();
    return deviceInfoVos;
  }

  public List<DeviceInfoVo> queryDeviceListSortByStationId(String stationId) {
    List<DeviceInfoVo> deviceInfoVos = deviceStateDao.queryDeviceListSortByStationId(stationId);
    return deviceInfoVos;
  }

  @Override
  public Boolean queryDeviceListById(String deviceId) {
    List<DeviceInfoVo> deviceInfoVos = deviceStateDao.queryDeviceListById(deviceId);
    return deviceInfoVos.size() > 0 ? true : false;
  }
}
