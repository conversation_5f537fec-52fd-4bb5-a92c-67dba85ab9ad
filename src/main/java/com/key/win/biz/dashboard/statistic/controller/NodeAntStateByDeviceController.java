package com.key.win.biz.dashboard.statistic.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.model.Probe;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.biz.baseInfo.service.impl.ProbeServiceImpl;
import com.key.win.biz.dashboard.statistic.service.DeviceStateService;
import com.key.win.biz.dashboard.statistic.service.NodeAntStateDeviceService;
import com.key.win.biz.dashboard.statistic.vo.NodeAntInfoVo;
import com.key.win.biz.topo.enums.DeviceStatus;
import com.key.win.common.web.Result;
import com.key.win.config.RedisUtil;
import com.key.win.mqtt.mq.cmd.EmqRequestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/dashboard/nodeAnt/*")
public class NodeAntStateByDeviceController {
  @Autowired private NodeAntStateDeviceService nodeAntStateDeviceService;
  @Autowired private ProbeServiceImpl probeInfoService;
  @Autowired private DeviceStateService deviceStateService;
  @Autowired private RedisUtil redisUtil;
  @Autowired private EmqRequestService emqRequestService;

  @PostMapping("/nodeAntState/{deviceId}")
  public Object AntStateByDeviceId(@PathVariable("deviceId") String deviceId) {
    Boolean hasDevice = deviceStateService.queryDeviceListById(deviceId);
    if (!hasDevice) {
      return Result.failed("查无此设备");
    }
    List<NodeAntInfoVo> nodeAntInfoVos = nodeAntStateDeviceService.queryAntByDeviceId(deviceId);
    if (nodeAntInfoVos.size() <= 0) {
      return Result.succeed("此设备暂无天线");
    }
    boolean flag = emqRequestService.clientIsOnline("reader_" + deviceId);
    if (!flag) {
      for (int i = 0; i < nodeAntInfoVos.size(); i++) {
        nodeAntInfoVos.get(i).setProbeStatus(DeviceStatus.gray.getState());
      }
      return Result.succeed(nodeAntInfoVos, "设备离线");
    }

    List<Probe> list = probeInfoService.list();

    //    添加探针数据至缓存
    for (int i = 0; i < list.size(); i++) {
      Probe probe = list.get(i);
      redisUtil.set(
          probe.getHostNumber() + probe.getNumber(),
          probe.getProbeStatus() + ":" + probe.getLost());
    }
    String antKey = null;
    //    获取天线状态
    for (int i = 0; i < nodeAntInfoVos.size(); i++) {
      String probeSid = nodeAntInfoVos.get(i).getProbeSid();
      if (probeSid == null || "".equals(probeSid.trim())) {
        nodeAntInfoVos.get(i).setProbeStatus(DeviceStatus.gray.getState());
        continue;
      }
      antKey = nodeAntInfoVos.get(i).getHostNum() + probeSid;
      boolean hasKey = redisUtil.hasKey(antKey);
      if (hasKey) {
        String result = (String) redisUtil.get(antKey);
        String[] split = result.split(":");
        String probeStatus = split[0];
        Integer lost = Integer.valueOf(split[1]);
        if ("00".equals(probeStatus)) {
          nodeAntInfoVos.get(i).setProbeStatus(DeviceStatus.red.getState());
        } else if ("01".equals(probeStatus)){
          nodeAntInfoVos.get(i).setProbeStatus(DeviceStatus.green.getState());
        }else {
          nodeAntInfoVos.get(i).setProbeStatus(DeviceStatus.gray.getState());
        }
        nodeAntInfoVos.get(i).setLost(Integer.valueOf(lost));
      } else {
        nodeAntInfoVos.get(i).setProbeStatus(DeviceStatus.gray.getState());
      }
    }
    return Result.succeed(nodeAntInfoVos, "设备在线");
  }
}
