package com.key.win.biz.dashboard.statistic.vo;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 设备数量统计
 *
 * <AUTHOR>
 */
@Data
@TableName("SF_STATISTIC_DEVICE_COUNT_VO")
public class DeviceCountStatisticVO implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  private String lineId;

  private String lineName;

  private String stationId;

  private String stationName;

  private String deviceCount;
}
