package com.key.win.biz.dashboard.statistic.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.key.win.biz.dashboard.statistic.dao.DeviceCountStatisticDao;
import com.key.win.biz.dashboard.statistic.vo.DeviceCountStatisticVO;
import com.key.win.biz.dashboard.statistic.vo.LineStationVO;

import cn.hutool.core.map.MapUtil;

@Service
@SuppressWarnings("rawtypes")
public class DeviceCountStatisticService {

  @Resource private DeviceCountStatisticDao deviceCountStatisticDao;

  public List<DeviceCountStatisticVO> deviceCountStatistic() {
    return deviceCountStatisticDao.deviceCountStatistic();
  }

  public List<Map> currentDayAlarmCount() {
    return deviceCountStatisticDao.currentDayAlarmCount();
  }

  public Map statisticGeneralSituation() {
    List<Map> resultMap = deviceCountStatisticDao.statisticGeneralSituation();
    Map<String, Object> returnResultMap = new HashMap<String, Object>();
    for (Map m : resultMap) {
      String type = MapUtil.getStr(m, "type");
      Integer count = MapUtil.getInt(m, "count", 0);
      returnResultMap.put(type, count);
    }
    return returnResultMap;
  }

  /**
   * 获取每个站的设备个数统计 监控主机、天线、探针个数
   *
   * @return
   */
  public List<Map<String, Object>> deviceStatistic(String lineId) {
    List<Map<String, Object>> resultList = new ArrayList<Map<String, Object>>();

    List<LineStationVO> listListAndStation = deviceCountStatisticDao.listLineAndStation(lineId);
    for (LineStationVO vo : listListAndStation) {

      Map<String, Object> result = new HashMap<String, Object>();

      String _lineId = vo.getLineId();
      String stationId = vo.getStationId();
      Map<String, String> param = new HashMap<String, String>();
      param.put("lineId", _lineId);
      param.put("stationId", stationId);
      // 监控主机个数
      int deviceCount = deviceCountStatisticDao.deviceCountByLineIdAndStationId(param);
      // 天线个数
      int antCount = deviceCountStatisticDao.antCountByStationId(stationId);
      // 探针个数
      int probeCount = deviceCountStatisticDao.probeCountByStationId(stationId);

      result.put("lineId", _lineId);
      result.put("lineName", vo.getLineName());
      result.put("stationId", vo.getStationId());
      result.put("stationName", vo.getStationName());
      result.put("deviceCount", deviceCount);
      result.put("antCount", antCount);
      result.put("probeCount", probeCount);

      resultList.add(result);
    }
    return resultList;
  }
}
