package com.key.win.biz.dashboard.statistic.controller;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.baseInfo.model.Probe;
import com.key.win.biz.baseInfo.model.BaseStation;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.model.SiteInfo;
import com.key.win.biz.baseInfo.service.ProbeService;
import com.key.win.biz.baseInfo.service.BaseLineService;
import com.key.win.biz.baseInfo.service.BaseStationService;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.biz.baseInfo.service.SiteInfoService;
import com.key.win.biz.baseInfo.service.impl.ProbeServiceImpl;
import com.key.win.biz.dashboard.statistic.service.impl.DeviceCountStatisticService;
import com.key.win.biz.topo.model.TopoNodes;
import com.key.win.biz.topo.service.TopoService;
import com.key.win.config.RedisUtil;
import com.key.win.mqtt.mq.cmd.EmqRequestService;
import com.key.win.system.server.po.Cpu;
import com.key.win.system.server.po.Server;
import com.key.win.system.server.po.SysFile;
import com.key.win.system.server.service.GetOSInfoService;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@SuppressWarnings("rawtypes")
@RequestMapping("/dashboard/statistic/*")
public class DashboardStatisticController {

  @Autowired private SiteInfoService siteInfoService;
  @Resource private BelongUnitService belongUnitService;

  @Autowired private ProbeService probeService;

  @Autowired private TopoService topoService;

  @Autowired private EmqRequestService emqRequestService;

  @Autowired private DeviceCountStatisticService deviceCountStatisticService;

  @Autowired private BaseLineService baseLineService;

  @Autowired private BaseStationService baseStationService;

  @Autowired private GetOSInfoService getOSInfoService;
  @Autowired private RedisUtil redisUtil;
  @Autowired private ProbeServiceImpl probeInfoService;

  /**
   * @return 根据线路和站点进行统计室分设备数量
   */
  @PostMapping("/deviceCount")
  public Object deviceCountBylineAndStation() {
    return deviceCountStatisticService.deviceCountStatistic();
  }

  /**
   * @return 今日车站告警数量 清除告警数量、确认告警数量
   */
  @PostMapping("/realTimeAlarmCount")
  public Object realTimeAlarmCount() {
    List<Map> listResult = deviceCountStatisticService.currentDayAlarmCount();
    Map<String, Long> result = new LinkedHashMap<>();
    for (Map m : listResult) {
      result.put(MapUtil.getStr(m, "timeDesc"), MapUtil.getLong(m, "alarmNum"));
    }

    Long clearAlarm = MapUtil.getLong(result, "clearAlarm");
    Long confirmAlarm = MapUtil.getLong(result, "confirmAlarm");

    if (clearAlarm > confirmAlarm) {
      result.put("max", clearAlarm + 10);
    } else {
      result.put("max", confirmAlarm + 10);
    }
    return result;
  }

  /**
   * 室分概况统计
   *
   * @return 室分概况数据 siteCount：室分车站个数 hostCount：主机个数 antennaCount：天线个数 probeCount：探针个数
   *     hostError：主机离线个数 antErrorCount：天线异常个数 antSuccessCount：天线正常个数
   */
  @PostMapping("/networkInfo")
  public Object networkDeviceStatistics() {
    // 室分站点总数
    int siteCount = 0;
    // 监控主机总数
    int hostCount = 0;
    // 天线总数
    int antennaCount = 0;
    // 探针总数
    int probeCount = 0;
    // 主机异常总数
    int hostError = 0;
    int antUnknownCount = 0;
    int antErrorCount = 0;
    int antSuccessCount = 0;

    // 监控站点列表
    List<SiteInfo> siteList = siteInfoService.list();
    siteCount = siteList.size();

    for (SiteInfo siteInfo : siteList) {
      LambdaQueryWrapper<BelongUnit> lqw = new LambdaQueryWrapper<BelongUnit>();
      lqw.eq(BelongUnit::getPid, siteInfo.getId());
      // 获取车站下的监控主机列表
      List<BelongUnit> hostList = belongUnitService.list(lqw);
      // 监控主机个数
      int size = hostList.size();
      if (size == 0) {
        continue;
      }
      hostCount += size;

      for (BelongUnit unit : hostList) {
        String hostNum = unit.getHostNum();
        String clientId = "reader_" + hostNum;
        boolean isOnline = emqRequestService.clientIsOnline(clientId);
        if (isOnline) {
          belongUnitService.online(hostNum);
        } else {
          belongUnitService.offline(hostNum);
          hostError++;
        }
        // 从拓扑图中获取 所有的天线节点
        List<TopoNodes> antList = topoService.getAntNodesByHostNum(unit.getHostNum(), false);
        // 天线个数累加
        antennaCount += antList.size();

        if (!isOnline) {
          antErrorCount += antList.size();
        } else {
          //    添加探针数据至缓存
          List<Probe> list = probeInfoService.list();
          for (int i = 0; i < list.size(); i++) {
            Probe probe = list.get(i);
            redisUtil.set(
                    probe.getHostNumber() + probe.getNumber(),
                    probe.getProbeStatus() + ":" + probe.getLost());
          }

          for (TopoNodes n : antList) {
            String probeSid = n.getProbeSid();
            if (StringUtils.isBlank(probeSid)) {
              antErrorCount++;
            } else {
//              LambdaQueryWrapper<Probe> lqwAnt = new LambdaQueryWrapper<Probe>();
//              lqwAnt.eq(Probe::getNumber, probeSid);
//              lqwAnt.eq(Probe::getHostNumber, hostNum);
//              Probe an = probeService.getOne(lqwAnt);
//              if (an == null) {
//                antUnknownCount++;
//              } else {
//                if (StringUtils.isNoneBlank(an.getProbeStatus())
//                    && an.getProbeStatus().equals("01")) {
//                  antSuccessCount++;
//                } else if (StringUtils.isNoneBlank(an.getProbeStatus())
//                    && an.getProbeStatus().equals("00")) {
//                  antErrorCount++;
//                } else {
//                  antUnknownCount++;
//                }
//              }
              String antKey = n.getHostNum() + probeSid;
              boolean hasKey = redisUtil.hasKey(antKey);
              if (!hasKey) {
                antUnknownCount++;
              }else {
                String result = (String) redisUtil.get(antKey);
                String[] split = result.split(":");
                String probeStatus = split[0];
                if ("00".equals(probeStatus)) {
                  antErrorCount++;
                }  else if("01".equals(probeStatus)){
                  antSuccessCount++;
                }else {
                  antUnknownCount++;
                }
              }
            }
          }
        }
        LambdaQueryWrapper<Probe> lqwAnt = new LambdaQueryWrapper<Probe>();
        lqwAnt.eq(Probe::getHostNumber, unit.getHostNum());
        List<Probe> probeList = probeService.list(lqwAnt);
        probeCount += probeList.size();
      }
    }
    Map<String, Object> res = new LinkedHashMap<String, Object>();
    res.put("siteCount", siteCount); // 室分车站总数
    res.put("hostCount", hostCount); // 监控主机总数
    res.put("antennaCount", antennaCount); // 天线总数
    res.put("probeCount", probeCount); // 探针总数
    res.put("hostError", hostError); // 主机异常总数
    res.put("hostNormal", hostCount - hostError <= 0 ? 0 : hostCount - hostError); // 主机正常
    res.put("antUnknownCount", antUnknownCount); // 天线未知状态
    res.put("antErrorCount", antErrorCount + antUnknownCount); // 天线异常
    res.put("antOnlineCount", antSuccessCount); // 天线正常
    return res;
  }

  /**
   * 室分车站所占比
   *
   * @return 百分比值
   */
  @PostMapping("/siteProportion")
  public Object siteProportion() {
    double stationCount = 0;
    double siteCount = 0;
    List<SiteInfo> siteList = siteInfoService.list();
    siteCount += siteList.size();

    List<BaseStation> stationList = baseStationService.list();
    stationCount += stationList.size();

    if (stationCount == 0) {
      return 0;
    }
    double proportion = siteCount / stationCount;
    long round = Math.round(proportion * 100);

    List<Map> returnList = new ArrayList<>();

    Map<String, Object> sfSiteMap = new LinkedHashMap<String, Object>();
    sfSiteMap.put("value", round); // 监控主机占比
    sfSiteMap.put("name", "室分车站"); // 监控主机占比

    Map<String, Object> stationMap = new LinkedHashMap<String, Object>();
    stationMap.put("value", 100 - round); // 天线总数
    stationMap.put("name", "普通车站"); // 天线总数

    returnList.add(sfSiteMap);
    returnList.add(stationMap);
    return returnList;
  }

  /**
   * 所有设备总数
   *
   * @return 主机、天线、探针所有站点总和
   */
  @PostMapping("/allDeviceCount")
  public Object allDeviceCount() {
    // 监控主机总数
    int hostCount = 0;
    // 天线总数
    int antennaCount = 0;
    // 探针总数
    int probeCount = 0;

    // 监控站点列表
    List<SiteInfo> siteList = siteInfoService.list();
    for (SiteInfo siteInfo : siteList) {
      String id = siteInfo.getId();
      LambdaQueryWrapper<BelongUnit> lqw = new LambdaQueryWrapper<>();
      lqw.eq(BelongUnit::getPid, id);
      // 获取车站下的监控主机列表
      List<BelongUnit> hostList = belongUnitService.list(lqw);
      // 监控主机个数
      int size = hostList.size();
      if (size == 0) {
        continue;
      }
      hostCount += size;

      for (BelongUnit unit : hostList) {

        // 从拓扑图中获取 所有的天线节点
        List<TopoNodes> antList = topoService.getAntNodesByHostNum(unit.getHostNum(), false);
        // 天线个数累加
        antennaCount += antList.size();

        LambdaQueryWrapper<Probe> lqwAnt = new LambdaQueryWrapper<Probe>();
        lqwAnt.eq(Probe::getHostNumber, unit.getHostNum());
        List<Probe> probeList = probeService.list(lqwAnt);
        probeCount += probeList.size();
      }
    }

    List<Map> returnList = new ArrayList<>();

    Map<String, Object> hostMap = new LinkedHashMap<String, Object>();
    hostMap.put("name", "监控主机"); // 监控主机总数
    hostMap.put("value", hostCount); // 监控主机总数

    Map<String, Object> antennaMap = new LinkedHashMap<String, Object>();
    antennaMap.put("name", "天线"); // 天线总数
    antennaMap.put("value", antennaCount); // 天线总数

    Map<String, Object> probeMap = new LinkedHashMap<String, Object>();
    probeMap.put("name", "探针"); // 探针总数
    probeMap.put("value", probeCount); // 探针总数

    returnList.add(hostMap);
    returnList.add(antennaMap);
    returnList.add(probeMap);
    return returnList;
  }

  /**
   * 线路列表（统计分析使用）
   *
   * @return 所有的线路
   */
  @PostMapping("/lines")
  public Object lineList() {
    return baseLineService.list();
  }
  /**
   * 统计按站获取每个站监控主机、探针、天线个数
   *
   * @return 当前线路的所有车站各个设备占比数据信息
   */
  @PostMapping("/statisticDeviceCount/{lineId}")
  public Object statisticDeviceCount(@PathVariable String lineId) {
    List<Map<String, Object>> resultList = deviceCountStatisticService.deviceStatistic(lineId);

    List<String> legendData = new ArrayList<>();
    legendData.add("监控主机");
    legendData.add("天线");
    legendData.add("探针");

    List<String> xAxisData = new ArrayList<>();
    List<Long> deviceSeriesData = new ArrayList<>();
    List<Long> antSeriesData = new ArrayList<>();
    List<Long> probeSeriesData = new ArrayList<>();

    for (Map m : resultList) {
      long probeCount = MapUtil.getLong(m, "probeCount");
      long deviceCount = MapUtil.getLong(m, "deviceCount");
      long antCount = MapUtil.getLong(m, "antCount");
      String stationName = MapUtil.getStr(m, "stationName");

      xAxisData.add(stationName);
      deviceSeriesData.add(deviceCount);
      antSeriesData.add(antCount);
      probeSeriesData.add(probeCount);
    }

    Map<String, Object> retMap = new LinkedHashMap<>();

    retMap.put("legendData", legendData);
    retMap.put("xAxisData", xAxisData);

    retMap.put("deviceSeriesData", deviceSeriesData);
    retMap.put("antSeriesData", antSeriesData);
    retMap.put("probeSeriesData", probeSeriesData);

    return retMap;
  }

  @PostMapping("/serverInfo")
  public Object serverMonitorInfo() {
    Server server = getOSInfoService.getOSInfo();
    Cpu cpu = server.getCpu();
    // CPU利用率
    Map<String, Object> cpuMap = new LinkedHashMap<>();
    double cpuUsage = Math.round(100 - cpu.getFree());
    cpuMap.put("name", "CPU使用率");
    cpuMap.put("value", cpuUsage);

    // 内存使用率
    Map<String, Object> memMap = new LinkedHashMap<>();
    memMap.put("name", "内存使用率");
    memMap.put("value", Math.round(server.getMem().getUsage()));
    memMap.put("totalGB", Math.ceil(server.getMem().getTotal()));
    memMap.put("useGB", Math.round(server.getMem().getUsed()));

    // 磁盘空间
    List<String> yAxisData = new ArrayList<>(); // 盘符名称
    List<Double> seriesData = new ArrayList<>(); // 磁盘使用率
    List<String> diskTotalData = new ArrayList<>(); // 磁盘总量
    List<String> diskUseData = new ArrayList<>(); // 磁盘使用量
    List<String> diskFreeData = new ArrayList<>(); // 磁盘使用量

    List<SysFile> sysFiles = server.getSysFiles();
    for (SysFile file : sysFiles) {
      String diskTotal = file.getTotal();
      double diskUsage = file.getUsage();
      String diskUse = file.getUsed();
      String diskFree = file.getFree();
      String dirName = file.getDirName();

      yAxisData.add(dirName);
      seriesData.add(diskUsage);
      diskUseData.add(diskUse);
      diskTotalData.add(diskTotal);
      diskFreeData.add(diskFree);
    }

    Map<String, Object> result = new LinkedHashMap<>();
    result.put("cpuInfo", cpuMap);
    result.put("memInfo", memMap);

    result.put("diskInfoYAxisData", yAxisData); // 磁盘名称
    result.put("diskInfoSeriesData", seriesData); // 磁盘使用率
    result.put("diskInfoTotalData", diskTotalData); // 盘符总量
    result.put("diskInfoUseData", diskUseData); // 盘符使用量
    result.put("diskInfoFreeData", diskFreeData); // 盘符剩余空间

    return result;
  }
}
