package com.key.win.biz.dashboard.statistic.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.key.win.biz.dashboard.statistic.vo.DeviceCountStatisticVO;
import com.key.win.biz.dashboard.statistic.vo.DeviceInfoVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DeviceStateDao extends BaseMapper<DeviceInfoVo> {
  List<DeviceInfoVo> queryDeviceListSort();

  List<DeviceInfoVo> queryDeviceListSortByStationId(String stationId);

  List<DeviceInfoVo> queryDeviceListById(String deviceId);
}
