package com.key.win.biz.dashboard.statistic.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.persistence.Transient;

@Data
@TableName("sf_topo_node")
public class NodeAntInfoVo {
  private String Id;
  private String hostNum;
  private String lineId;
  private String lineName;
  private String stationId;
  private String stationName;
  private String name;
  private String probeSid;
  private String type;
  @Transient private String typeName;
  private String flagTrue;
  @Transient private Integer probeStatus;
  @Transient private Integer lost;
}
