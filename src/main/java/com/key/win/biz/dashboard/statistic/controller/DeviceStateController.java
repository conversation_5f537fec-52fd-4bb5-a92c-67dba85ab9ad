package com.key.win.biz.dashboard.statistic.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.model.SiteInfo;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.biz.baseInfo.service.ProbeService;
import com.key.win.biz.baseInfo.service.SiteInfoService;
import com.key.win.biz.dashboard.statistic.service.DeviceStateService;
import com.key.win.biz.dashboard.statistic.vo.DeviceInfoVo;
import com.key.win.biz.topo.service.TopoExtService;
import com.key.win.biz.topo.service.TopoService;
import com.key.win.common.web.Result;
import com.key.win.config.RedisUtil;
import com.key.win.mqtt.mq.cmd.EmqRequestService;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/dashboard/device/*")
public class DeviceStateController {

  @Autowired private EmqRequestService emqRequestService;
  @Autowired private SiteInfoService siteInfoService;
  @Autowired private DeviceStateService deviceStateService;
  @Autowired private BelongUnitService belongUnitService;
  @Autowired private TopoService topoService;
  @Autowired private ProbeService probeInfoService;
  @Autowired private RedisUtil redisUtil;
  @Autowired private TopoExtService topoExtService;

  private String redisKeyPrefix = "panel";

  @PostMapping("/onAndOffList/{stationId}")
  private Object onAndOffList(@PathVariable(required = false) String stationId) {
    List<DeviceInfoVo> deviceList = new ArrayList<>();
    if (StringUtils.isBlank(stationId) || "-1".equals(stationId)) {
      deviceList = deviceStateService.getDeviceListSort();
    } else {
      LambdaQueryWrapper<SiteInfo> lqw = new LambdaQueryWrapper<>();
      lqw.eq(SiteInfo::getStationCode, stationId);
      SiteInfo siteInfo = siteInfoService.getOne(lqw);
      if (siteInfo == null) {
        return Result.failed("查无此车站");
      }
      deviceList = deviceStateService.queryDeviceListSortByStationId(stationId);
    }

    if (deviceList.size() == 0) {
      return Result.succeed("当前车站下没有发现监测主机");
    }

    List<DeviceInfoVo> inLine = new ArrayList<>();
    List<DeviceInfoVo> offLine = new ArrayList<>();
    for (int i = 0; i < deviceList.size(); i++) {
      String clientId = "reader_" + deviceList.get(i).getHostNum();
      boolean flag = emqRequestService.clientIsOnline(clientId);
      if (flag) {
        deviceList.get(i).setDeviceStatus("在线");
        inLine.add(deviceList.get(i));
      } else {
        deviceList.get(i).setDeviceStatus("设备离线");
        offLine.add(deviceList.get(i));
      }
    }
    Map<String, List<DeviceInfoVo>> result = new LinkedHashMap<>();
    result.put("deviceOnlineList", inLine);
    result.put("deviceOfflineList", offLine);
    return Result.succeed(result, "主机在线离线列表");
  }

  /**
   * 室分车站信息
   *
   * @return
   */
  @PostMapping("/sfStation/info")
  public Result sfStationInfo() {
    List<SiteInfo> siteListInfo = siteInfoService.list();
    Map<String, List<SiteInfo>> result = new LinkedHashMap<>();
    result.put("siteList", siteListInfo);
    return Result.succeed(result, "室分站点列表");
  }

  /**
   * 室分车站下的 监控主机信息
   *
   * @param stationCode 车站编码
   * @return
   */
  @PostMapping("/loadHostByStationCode/{stationCode}")
  public Result loadHostBySiteId(@PathVariable(name = "stationCode") String stationCode) {
    List<DeviceInfoVo> deviceInfoVos =
            deviceStateService.queryDeviceListSortByStationId(stationCode);
    deviceInfoVos.forEach(
            (x) -> {
              boolean isOnline = emqRequestService.clientIsOnline(x.getHostNum());
              if (isOnline) {
                x.setDeviceStatus("online");
              } else {
                x.setDeviceStatus("offline");
              }
            });
    Map<String, List<DeviceInfoVo>> result = new LinkedHashMap<>();
    result.put("deviceList", deviceInfoVos);
    return Result.succeed(result, "室分主机列表");
  }

  /**
   * 在线天线、离线天线、未知天线 接口
   * @param status online offline unknown
   * @return
   */
  @PostMapping("/antInfoList/{status}")
  private Object antOnlineList(@PathVariable(name = "status") String status) {
    if (!status.equals("online") && !status.equals("offline") && !status.equals("unknown")) {
      return Result.failed(new HashMap<>(),"未获取到天线状态!");
    }
    Map<String, Object> data = topoExtService.loadAllAntNodeFromDBByAntStatus(status);
    return Result.succeed(data, "绑定天线信息");
  }



  @PostMapping("/sfHostInfo/{status}")
  private Object sfHostInfo(@PathVariable(name = "status") String status){
    if (!status.equals("online") && !status.equals("offline")) {
      return Result.failed(new HashMap<>(),"未获取到主机状态参数!");
    }

    LambdaQueryWrapper<BelongUnit> lqwUnit = new LambdaQueryWrapper<>();
    lqwUnit.eq(BelongUnit::getEnableFlag,true);
    lqwUnit.orderByAsc(BelongUnit::getStationId);
    List<BelongUnit> unitList = belongUnitService.list(lqwUnit);

    List<BelongUnit> onlineUnit = new ArrayList<>();
    List<BelongUnit> offlineUnit = new ArrayList<>();

    unitList.forEach( x -> {
      String deviceStatus = x.getDeviceStatus();
      if(StringUtils.equals(deviceStatus,"在线")){
        x.setDeviceStatus("online");
        onlineUnit.add(x);
      }else {
        x.setDeviceStatus("offline");
        offlineUnit.add(x);
      }
    });

    List<BelongUnit> units = null;
    if (status.equals("online")) {
      units = onlineUnit;
    }else if(status.equals("offline")){
      units = offlineUnit;
    }

    LinkedHashMap<String, List<BelongUnit>> unitMap = units.stream().collect(Collectors.groupingBy(BelongUnit::getStationName, LinkedHashMap::new, Collectors.toList()));
    List<String> stationNameList = new ArrayList<>();
    unitMap.keySet().forEach(x -> {
      stationNameList.add(x);
    });

    Map<String,Object> data = new HashMap<>();
    data.put("stations",stationNameList);
    data.put("hosts",unitMap);
    return Result.succeed(data, "室分监控主机信息");
  }


}
