package com.key.win.biz.dashboard.statistic.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.key.win.biz.dashboard.statistic.vo.DeviceCountStatisticVO;
import com.key.win.biz.dashboard.statistic.vo.LineStationVO;

@Mapper
public interface DeviceCountStatisticDao extends BaseMapper<DeviceCountStatisticVO> {

  /**
   * 按照 线路车站统计室分设备数量
   *
   * @return
   */
  public List<DeviceCountStatisticVO> deviceCountStatistic();

  /**
   * 今日告警数量
   *
   * @return
   */
  public List<Map> currentDayAlarmCount();

  /**
   * 概况统计
   *
   * @return
   */
  public List<Map> statisticGeneralSituation();

  /**
   * 线路和车站基础数据
   *
   * @return
   */
  public List<LineStationVO> listLineAndStation(String lineId);

  /**
   * 室分车站个数
   *
   * @return
   */
  public int siteCount();
  /**
   * 根据线路id和车站ID统计监控主机个数
   *
   * @param lineId
   * @param stationId
   * @return
   */
  public int deviceCountByLineIdAndStationId(@Param("param") Map<String, String> param);

  /**
   * 根据车站编号统计天线个数
   *
   * @param stationId 车站编号
   * @return
   */
  public int antCountByStationId(@Param(value = "stationId") String stationId);

  /**
   * 根据车站编号统计探针个数
   *
   * @param stationId 车站编号
   * @return
   */
  public int probeCountByStationId(@Param(value = "stationId") String stationId);
}
