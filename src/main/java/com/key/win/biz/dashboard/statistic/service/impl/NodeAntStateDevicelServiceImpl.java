package com.key.win.biz.dashboard.statistic.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.key.win.biz.dashboard.statistic.dao.NodeAntStateByDeviceDao;
import com.key.win.biz.dashboard.statistic.service.NodeAntStateDeviceService;
import com.key.win.biz.dashboard.statistic.vo.NodeAntInfoVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class NodeAntStateDevicelServiceImpl
    extends ServiceImpl<NodeAntStateByDeviceDao, NodeAntInfoVo>
    implements NodeAntStateDeviceService {
  @Autowired private NodeAntStateByDeviceDao nodeAntStateByDeviceDao;

  @Override
  public List<NodeAntInfoVo> queryAntByDeviceId(String deviceId) {
    return nodeAntStateByDeviceDao.queryAntByDeviceId(deviceId);
  }
}
