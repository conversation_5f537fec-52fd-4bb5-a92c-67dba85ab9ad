package com.key.win.common.web;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.util.Date;

@Data
@MappedSuperclass
@EqualsAndHashCode(callSuper = false)
public class MybatisID {

  /** */
  private static final long serialVersionUID = 1L;

  @TableId(type = IdType.ID_WORKER_STR)
  @Id
  @Column(length = 50)
  private String id;

  @TableLogic(value = "1", delval = "0")
  private Boolean enableFlag = Boolean.TRUE;

  private Date addTime = new Date();

  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public Boolean getEnableFlag() {
    return enableFlag;
  }

  public void setEnableFlag(Boolean enableFlag) {
    this.enableFlag = enableFlag;
  }

  public Date getAddTime() {
    return addTime;
  }

  public void setAddTime(Date addTime) {
    this.addTime = addTime;
  }

  public static long getSerialversionuid() {
    return serialVersionUID;
  }
}
