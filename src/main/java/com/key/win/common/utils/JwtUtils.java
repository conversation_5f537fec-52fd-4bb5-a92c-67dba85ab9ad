package com.key.win.common.utils;

import java.util.Date;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

/**
 * <AUTHOR>
 * @since 2021/10/16
 */
public class JwtUtils {

  public static final long EXPIRE = 1000 * 60 * 60 * 24 * 7 * 8; // token过期时间 7*24小时
  public static final String APP_SECRET = "ukc8BDbRigUDaY6pZFfWus2jZWLPHO"; // 密钥

  public static String getJwtToken(String id, String account) {

    String JwtToken =
        Jwts.builder()
            .setHeaderParam("typ", "JWT")
            .setHeaderParam("alg", "HS256")
            .setSubject("jacob-user")
            .setIssuedAt(new Date())
            .setExpiration(new Date(System.currentTimeMillis() + EXPIRE))
            .claim("id", id)
            .claim("account", account)
            .signWith(SignatureAlgorithm.HS256, APP_SECRET)
            .compact();

    return JwtToken;
  }

  /**
   * 根据token，判断token是否存在与有效
   *
   * @param jwtToken
   * @return
   */
  public static boolean checkToken(String jwtToken) {
    if (StringUtils.isEmpty(jwtToken)) return false;
    try {
      Jwts.parser().setSigningKey(APP_SECRET).parseClaimsJws(jwtToken);
      return true;
    } catch (Exception e) {
      String errorMsg = e.getMessage();
      e.printStackTrace();
      return false;
    }
  }

  /**
   * 根据request判断token是否存在与有效（也就是把token取出来罢了）
   *
   * @param request
   * @return
   */
  public static boolean checkToken(HttpServletRequest request) {
    String jwtToken = request.getHeader(HttpHeaders.AUTHORIZATION);
    if (StringUtils.isEmpty(jwtToken)) {
      return false;
    }
    Jws<Claims> parseClaimsJws = Jwts.parser().setSigningKey(APP_SECRET).parseClaimsJws(jwtToken);
    return parseClaimsJws.getBody().getExpiration().before(new Date());
  }

  /**
   * 根据token获取会员id
   *
   * @param request
   * @return
   */
  public static String getMemberIdByJwtToken(HttpServletRequest request) {
    String jwtToken = request.getHeader(HttpHeaders.AUTHORIZATION);
    if (StringUtils.isEmpty(jwtToken)) return "";
    try {
      // 这里解析可能会抛异常，所以try catch来捕捉
      Jws<Claims> claimsJws = Jwts.parser().setSigningKey(APP_SECRET).parseClaimsJws(jwtToken);
      Claims claims = claimsJws.getBody();
      return (String) claims.get("id");
    } catch (Exception e) {
      e.printStackTrace();
      return null;
    }
  }

  /**
   * 根据token获取用户的account
   *
   * @param request
   * @return
   */
  public static String getMemberAccountByJwtToken(HttpServletRequest request) {
    String jwtToken = request.getHeader(HttpHeaders.AUTHORIZATION);
    if (StringUtils.isEmpty(jwtToken) || StringUtils.equalsIgnoreCase("NULL", jwtToken)) {
      return "";
    }
    try {
      Jws<Claims> claimsJws = Jwts.parser().setSigningKey(APP_SECRET).parseClaimsJws(jwtToken);
      Claims claims = claimsJws.getBody();
      return (String) claims.get("account");
    } catch (Exception e) {
      e.printStackTrace();
      return null;
    }
  }
}
