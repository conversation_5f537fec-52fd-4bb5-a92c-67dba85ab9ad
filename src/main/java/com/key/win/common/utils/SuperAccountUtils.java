package com.key.win.common.utils;

import com.key.win.utils.SpringSecurityUtils;
import com.key.win.utils.SpringUtils;
import org.springframework.core.env.Environment;

public class SuperAccountUtils {


  public static final String TEST_MENU_ID = "1539129186847752193"; //顶级【功能测试】菜单（菜单ID：1539129186847752193）
  public static final String DEBUG_ROLE_CODE = "debug"; //调试员的角色编码

  private static Environment environment = SpringUtils.getBean(Environment.class);

  public static boolean isSuperAccount() {
    String username = SpringSecurityUtils.getUserName();
    String superAccount = environment.getProperty("sf.super_account");
    if(username.equals(superAccount)){
      return true;
    }
    return false;
  }

}
