package com.key.win.runner;

import com.key.win.snmpserver.SnmpAgentService;
import com.key.win.snmpserver.model.ZHWGDevice;
import com.key.win.snmpserver.oid.SFOid;
import com.key.win.snmpserver.service.IZHWGDeviceService;
import com.key.win.snmpserver.trap.AlarmTrapService;
import com.key.win.utils.SpringUtils;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Order(3)
public class SNMPRunner implements ApplicationRunner {

  @Override
  public void run(ApplicationArguments args) throws Exception {
    log.info("成都10号线 SNMP Agent 开始加载节点信息.....>");
    SnmpAgentService snmpAgentService = SpringUtils.getBean(SnmpAgentService.class);
    AlarmTrapService alarmTrapService = SpringUtils.getBean(AlarmTrapService.class);

    IZHWGDeviceService zhwgService = SpringUtils.getBean(IZHWGDeviceService.class);

    String ip = "0.0.0.0";
    int port = 161;

    SFOid.snmp161 = snmpAgentService.startAgent(ip, port);
    SFOid.snmp162 = alarmTrapService.trapClientStart();
    log.info("成都10号线 SNMP Agent 记载完毕>>>>>>!!!!");

    log.info("成都10号线 SNMP Agent 加载综合网管的设备点表 >>>>>>>>");
    List<ZHWGDevice> zhwgDeviceList =
        zhwgService.list().stream()
            .filter(device -> StringUtils.isNoneBlank(device.getHostNum()))
            .collect(Collectors.toList());
    SFOid.zhwgDeviceMap.clear();
    SFOid.zhwgDeviceMap =
        zhwgDeviceList.stream()
            .collect(
                Collectors.toMap(
                    device ->
                        device.getStationId()
                            + "#"
                            + device.getHostNum()
                            + "#"
                            + device.getAntName(),
                    device -> device));
    log.info("成都10号线 SNMP Agent 加载综合网管的设备点表完毕，共加载数据{}条 >>>>>>>>", zhwgDeviceList.size());

    SFOid.zhwgAlarmTypeMap.clear();
    SFOid.zhwgAlarmTypeMap.put("固定主机", "SF_DEVICE");
    SFOid.zhwgAlarmTypeMap.put("天线", "SF_ANTENNA");
  }
}
