package com.key.win.runner;

import com.key.win.system.config.jobs.service.SFjobService;
import com.key.win.system.config.jobs.support.FutureMaps;
import com.key.win.utils.ServerChecker;
import com.key.win.utils.SpringUtils;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Component
@Order(2)
public class TaskInfoLoadRunner implements ApplicationRunner {

    @Autowired private Environment environment;
    private static final String MAIN = "main";
    private static final String BACKUP = "backup";

    // 标记当前节点是否正在运行定时任务
    private volatile boolean isJobsRunning = false;
    // 记录上一次检查时主节点是否运行
    private volatile boolean lastMasterStatus = false;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        String type = environment.getProperty("sf.tel.type");
        String targetServerIp = environment.getProperty("sf.tel.balanceNodeIp", "***********");
        Integer targetServerPort =
                environment.getProperty("sf.tel.balanceNodePort", Integer.class, 18080);

        log.info("[JOB]定时任务加载日志:当前启动节点为:{},启动类开始加载....", type);
        // 读取数据库定时调度配置
        if (type.equalsIgnoreCase(MAIN)) {
            String targetNotifyUrl =
                    "http://" + targetServerIp + ":" + targetServerPort + "/notify/task/boot";
            boolean isRunning = ServerChecker.isServerRunning(targetServerIp, targetServerPort);
            log.info("[JOB]备节点当前状态: {}", isRunning);
            if (isRunning) {
                RestTemplate restTemplate = new RestTemplate();
                Map<String, String> params = new HashMap<>();
                params.put("cmd", "stop");
                log.info("[JOB-Notify]通知备节点停止任务");
                String flag =
                        restTemplate.getForObject(targetNotifyUrl + "?cmd={cmd}", String.class, params);
                boolean b = Boolean.parseBoolean(flag);
                if (b) {
                    bootstrapJobs();
                    lastMasterStatus = true;
                    log.info("[JOB-Notify]备节点停止 {}", b ? "成功" : "失败");
                }
            } else {
                bootstrapJobs();
                log.info("[JOB-Notify]备节点未启动,本机任务加载 ");
            }
        } else if (type.equalsIgnoreCase(BACKUP)) {
            boolean isRunning = ServerChecker.isServerRunning(targetServerIp, targetServerPort);
            if (!isRunning) {
                bootstrapJobs();
                log.info("[JOB]{}:{} 目标服务[未]启动,加载本节点的定时调度任务完毕!!!", targetServerIp, targetServerPort);
            } else {
                log.info("[JOB]{}:{} 目标服务[正在]运行,忽略本节点定时任务启动!!!", targetServerIp, targetServerPort);
            }
        } else {
            log.warn("[JOB]当前sf.tel.type类型[{}]未被正确识别,请检查配置项", type);
            return;
        }
        log.info("[JOB]定时任务类加载完毕", type);
    }

    /** 定时检查主节点状态（每10秒检查一次，提高检测频率） */
    @Scheduled(fixedDelay = 10000, initialDelay = 10000)
    public void checkMasterNodeStatus() {
        String type = environment.getProperty("sf.tel.type");
        // 只有备节点才需要检查主节点状态
        if (!BACKUP.equalsIgnoreCase(type)) {
            return;
        }

        String targetServerIp = environment.getProperty("sf.tel.balanceNodeIp", "***********");
        Integer targetServerPort =
                environment.getProperty("sf.tel.balanceNodePort", Integer.class, 18080);

        boolean isMasterRunning = ServerChecker.isServerRunning(targetServerIp, targetServerPort);

        // 如果主节点状态发生变化
        if (lastMasterStatus != isMasterRunning) {
            log.info("[JOB]检测到主节点状态变化，原状态:{}，当前状态:{}", lastMasterStatus, isMasterRunning);

            if (isMasterRunning) {
                // 主节点启动了，如果备节点正在运行任务，则停止任务
                if (isJobsRunning) {
                    shutdownJobs();
                    isJobsRunning = false;
                    log.info("[JOB]主节点已启动，停止备节点定时任务");
                }
            } else {
                // 主节点宕机了，如果备节点没有运行任务，则启动任务
                if (!isJobsRunning) {
                    bootstrapJobs();
                    isJobsRunning = true;
                    log.info("[JOB]主节点已宕机，启动备节点定时任务");
                }
            }

            // 更新主节点状态记录
            lastMasterStatus = isMasterRunning;
        }

        // 额外检查：即使状态未变化，也要确保在主节点运行时备节点不运行任务
        if (isMasterRunning && isJobsRunning) {
            shutdownJobs();
            isJobsRunning = false;
            log.info("[JOB]确保主节点运行时停止备节点定时任务");
        }
    }

    private void bootstrapJobs() {
        SFjobService bean = SpringUtils.getBean(SFjobService.class);
        bean.bootstrap();
    }

    private void shutdownJobs() {
        // 取消所有已调度的任务
        FutureMaps.getFuturemaps()
                .values()
                .forEach(
                        future -> {
                            if (future != null && !future.isCancelled()) {
                                future.cancel(true);
                            }
                        });

        // 清空任务映射
        FutureMaps.getFuturemaps().clear();
        FutureMaps.getTaskIdList().clear();

        log.info("[JOB]已停止所有定时任务");
    }
}
