package com.key.win.runner;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.config.mqtt.MqttPushClient;
import com.key.win.config.mqtt.MqttSubClient;
import com.key.win.system.config.sf.model.SfGlobalConfig;
import com.key.win.system.config.sf.service.ISfGlobalConfigService;
import com.key.win.system.config.sf.support.SfGlobalConfigString;
import com.key.win.system.config.sf.support.SfGlobalConfigType;
import com.key.win.utils.SpringUtils;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Order(1)
public class MqInfoLoadRunner implements ApplicationRunner {

  @Override
  public void run(ApplicationArguments args) throws Exception {

    ISfGlobalConfigService configService = SpringUtils.getBean(ISfGlobalConfigService.class);
    LambdaQueryWrapper<SfGlobalConfig> lqw = new LambdaQueryWrapper<SfGlobalConfig>();
    lqw.eq(SfGlobalConfig::getType, SfGlobalConfigType.CONFIG_MQ_ADDR_SETTING.toString());
    SfGlobalConfig cfgBean = configService.getOne(lqw);
    if (cfgBean == null) {
      log.warn("[系统警告]:没有配置MQ连接地址,请检查配置信息???????  ");
      return;
    }
    String configString = StringUtils.defaultString(cfgBean.getConfig(), "{}");
    Map m = JSONArray.parseObject(configString, Map.class);
    Boolean bool = MapUtil.getBool(m, SfGlobalConfigString.ConfigMapKey_enabled, false);
    String ip = MapUtil.getStr(m, SfGlobalConfigString.ConfigMapKey_MQ_ADDR_IP);
    String port = MapUtil.getStr(m, SfGlobalConfigString.ConfigMapKey_MQ_ADDR_PORT);
    String username =
        MapUtil.getStr(m, SfGlobalConfigString.ConfigMapKey_MQ_USERNAME, "indoorSystem");
    String password =
        MapUtil.getStr(m, SfGlobalConfigString.ConfigMapKey_MQ_PASSWORD, "indoorSystem");
    if (!bool) {
      log.warn("[系统警告]:当前MQ连接地址{},没有开启配置项,?????", ip);
      return;
    } else {
      ip = "tcp://" + ip;
      MqttSubClient.getInstance(ip, Integer.parseInt(port), username, password);
      MqttPushClient.getInstance(ip, Integer.parseInt(port), username, password);
    }
  }
}
