package com.key.win.runner;

import com.key.win.system.config.jobs.service.SFjobService;
import com.key.win.system.config.jobs.support.FutureMaps;
import com.key.win.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/notify/task/*")
public class TaskNotifyController {

  @GetMapping("/boot")
  public boolean notify(@RequestParam String cmd) {
    log.info("[JOB-Notify]收到指令{}", cmd);
    if (cmd.equals("start")) {
      bootstrapJobs();
      return true;
    } else if (cmd.equals("stop")) {
      shutdownJobs();
      return true;
    } else {
      log.warn("错误的指令{}", cmd);
      return false;
    }
  }

  private void bootstrapJobs() {
    SFjobService bean = SpringUtils.getBean(SFjobService.class);
    bean.bootstrap();
  }

  /** 停止定时任务 */
  private void shutdownJobs() {
    // 取消所有已调度的任务
    FutureMaps.getFuturemaps()
        .values()
        .forEach(
            future -> {
              if (future != null && !future.isCancelled()) {
                future.cancel(true);
              }
            });

    // 清空任务映射
    FutureMaps.getFuturemaps().clear();
    FutureMaps.getTaskIdList().clear();

    log.info("[JOB]已停止所有定时任务");
  }
}
