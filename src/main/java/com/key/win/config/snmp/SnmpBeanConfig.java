package com.key.win.config.snmp;

import java.util.List;

import org.snmp4j.PDU;
import org.snmp4j.smi.OID;
import org.snmp4j.smi.VariableBinding;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SnmpBeanConfig {

  @Bean
  public PDU pduRequestBeanInit() {
    PDU request = new PDU();

    List<String> snmpOidList = SnmpTargetConfig.getOids();
    for (String oid : snmpOidList) {
      request.add(new VariableBinding(new OID(oid)));
    }
    request.setType(PDU.GET);
    return request;
  }
}
