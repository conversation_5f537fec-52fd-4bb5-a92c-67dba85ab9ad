package com.key.win.config.snmp;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.lang3.StringUtils;
import org.snmp4j.CommunityTarget;
import org.snmp4j.smi.Address;
import org.snmp4j.smi.GenericAddress;
import org.snmp4j.smi.OctetString;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.system.config.sf.model.SfGlobalConfig;
import com.key.win.system.config.sf.service.ISfGlobalConfigService;
import com.key.win.system.config.sf.support.SfGlobalConfigString;
import com.key.win.system.config.sf.support.SfGlobalConfigType;

import cn.hutool.core.map.MapUtil;

@Component
@SuppressWarnings("rawtypes")
public class SnmpTargetConfig implements InitializingBean {

  @Autowired private ISfGlobalConfigService iSfGlobalConfigService;

  private static final List<String> oids = new ArrayList<String>();

  public static List<String> getOids() {
    oids.clear();
    oids.add("*******.4.1.6603.********.1.1.2.1000001");
    oids.add("*******.4.1.6603.********.1.1.2.1000002");
    oids.add("*******.4.1.6603.********.1.1.2.1000003");
    oids.add("*******.4.1.6603.********.1.1.2.1000004");
    oids.add("*******.4.1.6603.********.1.1.2.1000005");
    return oids;
  }

  private static Map<String, CommunityTarget> targetMap =
      new ConcurrentHashMap<String, CommunityTarget>();

  public static Map<String, CommunityTarget> getTargetMap() {
    if (targetMap == null) {
      targetMap = new ConcurrentHashMap<String, CommunityTarget>();
    }
    return targetMap;
  }

  @Override
  public void afterPropertiesSet() throws Exception {
    LambdaQueryWrapper<SfGlobalConfig> lqw = new LambdaQueryWrapper<SfGlobalConfig>();
    lqw.eq(SfGlobalConfig::getType, SfGlobalConfigType.CONFIG_SNMP_ADDR.toString());
    SfGlobalConfig snmpConfig = iSfGlobalConfigService.getOne(lqw);
    if (snmpConfig == null) {
      return;
    }
    String configString = snmpConfig.getConfig();

    List<Map> configMap = JSONArray.parseArray(configString, Map.class);
    for (Map m : configMap) {
      String ip = MapUtil.getStr(m, SfGlobalConfigString.ConfigMapKey_SNMP_ADDR_IP);
      if (StringUtils.isBlank(ip)) {
        continue;
      }
      boolean enabled =
          MapUtil.getBool(m, SfGlobalConfigString.ConfigMapKey_SNMP_ADDR_ENABLED, false);
      if (!enabled) {
        continue;
      }
      String port = MapUtil.getStr(m, SfGlobalConfigString.ConfigMapKey_SNMP_ADDR_PORT, "161");
      String community =
          MapUtil.getStr(m, SfGlobalConfigString.ConfigMapKey_SNMP_ADDR_COMMUNITY, "public");
      Integer version = MapUtil.getInt(m, SfGlobalConfigString.ConfigMapKey_SNMP_ADDR_VERSION);

      if (version == null) {
        version = 2;
      } else if (version == 1) {
        version = 0;
      } else if (version == 2) {
        version = 1;
      } else if (version == 3) {
        version = 3;
      }

      Long timeOut = MapUtil.getLong(m, SfGlobalConfigString.ConfigMapKey_SNMP_ADDR_TIMEOUT, 5L);
      Integer retries = MapUtil.getInt(m, SfGlobalConfigString.ConfigMapKey_SNMP_ADDR_RETRIES, 2);

      CommunityTarget target = new CommunityTarget();
      Address address = GenericAddress.parse("udp:" + ip + "/" + port);
      target.setAddress(address);
      target.setCommunity(new OctetString(community));
      target.setVersion(version);
      target.setTimeout(timeOut * 60);
      target.setRetries(retries);
      SnmpTargetConfig.getTargetMap().put(ip + ":" + port, target);
    }
  }
}
