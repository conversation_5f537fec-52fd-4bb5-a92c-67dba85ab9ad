package com.key.win.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * @description: 系统线程池使用
 */
@Slf4j
@Configuration
public class ExecutePoolConfiguration {

  @Bean(name = "threadPoolTaskExecutor")
  public ThreadPoolTaskExecutor threadPoolTaskExecutor() {
    int curSystemThreads = Runtime.getRuntime().availableProcessors() * 2;
    if (log.isDebugEnabled()) {
      log.debug("===========系统可用线程池个数 " + curSystemThreads);
    }
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    // 设置核心线程数
    executor.setCorePoolSize(Runtime.getRuntime().availableProcessors());
    // 设置最大线程数
    executor.setMaxPoolSize(curSystemThreads);
    // 设置队列容量
    executor.setQueueCapacity(20);
    // 设置活跃时间
    executor.setKeepAliveSeconds(60);
    // 设置线程默认名称
    executor.setThreadNamePrefix("sfxt");
    // 设置拒绝策略
    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    // 等待所有任务结束在关闭线程池
    executor.setWaitForTasksToCompleteOnShutdown(true);
    return executor;
  }
}
