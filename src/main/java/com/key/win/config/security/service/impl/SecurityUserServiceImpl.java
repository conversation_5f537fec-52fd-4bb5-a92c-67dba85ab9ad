package com.key.win.config.security.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.config.security.service.SecurityUserService;
import com.key.win.system.core.dao.SysPermissionMapper;
import com.key.win.system.core.dao.SysUserMapper;
import com.key.win.system.core.dto.UserAndRole;
import com.key.win.system.core.model.SysUser;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/25 14:20
 * @description
 */
@Service
public class SecurityUserServiceImpl implements SecurityUserService {

  @Autowired private SysUserMapper sysUserMapper;

  @Autowired private SysPermissionMapper sysPermissionMapper;

  /** 根据用户名查找数据库，判断是否存在这个用户 */
  @Override
  public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {

    // 用户名必须是唯一的，不允许重复
    LambdaQueryWrapper<SysUser> lqwUser = new LambdaQueryWrapper<SysUser>();
    lqwUser.eq(SysUser::getAccount, username);
    SysUser sysUser = sysUserMapper.selectOne(lqwUser);

    if (sysUser == null) {
      throw new UsernameNotFoundException("用户不存在");
    }

    List<GrantedAuthority> grantedAuthorities = new ArrayList<>();
    List<UserAndRole> userRoleList = sysUserMapper.getUserInfoByAccount(sysUser.getAccount());
    userRoleList.stream()
        .distinct()
        .forEach(
            x -> {
              if (StringUtils.isNotBlank(x.getRoleCode())) {
                GrantedAuthority grantedAuthority = new SimpleGrantedAuthority(x.getRoleCode());
                grantedAuthorities.add(grantedAuthority);
              }
            });

    //        List<SysPermission> sysPermissions =
    // sysPermissionMapper.getUserRolesByUserId(sysUser.getId());

    return new User(
        sysUser.getAccount(),
        sysUser.getPassword(),
        sysUser.getEnabled(),
        sysUser.getNotExpired(),
        sysUser.getCredentialsNotExpired(),
        sysUser.getAccountNotLocked(),
        grantedAuthorities);
  }
}
