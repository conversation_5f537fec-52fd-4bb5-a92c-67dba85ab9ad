package com.key.win.config.security.filter;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.SecurityMetadataSource;
import org.springframework.security.access.intercept.AbstractSecurityInterceptor;
import org.springframework.security.access.intercept.InterceptorStatusToken;
import org.springframework.security.web.FilterInvocation;
import org.springframework.security.web.access.intercept.FilterInvocationSecurityMetadataSource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/28 16:47
 * @description 我的理解是这个过滤器能把前2步的操作给连接起来
 */
@Service
public class CustomizeAbstractSecurityInterceptor extends AbstractSecurityInterceptor
    implements Filter {

  @Autowired private FilterInvocationSecurityMetadataSource securityMetadataSource;

  /** 小科普 如果@Autowirte写在方法上，就会把方法的参数从IoC容器中获取到，并且执行当前方法 */
  @Autowired
  public void setMyAccessDecisionManager(CustomizeAccessDecisionManager accessDecisionManager) {
    super.setAccessDecisionManager(accessDecisionManager);
  }

  @Override
  public Class<?> getSecureObjectClass() {
    return FilterInvocation.class;
  }

  @Override
  public SecurityMetadataSource obtainSecurityMetadataSource() {
    return this.securityMetadataSource;
  }

  @Override
  public void doFilter(
      ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
      throws IOException, ServletException {
    FilterInvocation fi = new FilterInvocation(servletRequest, servletResponse, filterChain);
    invoke(fi);
  }

  public void invoke(FilterInvocation fi) throws IOException, ServletException {
    // fi里面有一个被拦截的url
    // 里面调用CustomizeFilterInvocationSecurityMetadataSource的getAttributes(Object
    // object)这个方法获取fi对应的所有权限
    // 再调用CustomizeAccessDecisionManager的decide方法来校验用户的权限是否足够
    InterceptorStatusToken token = super.beforeInvocation(fi);
    try {
      // 执行下一个拦截器
      fi.getChain().doFilter(fi.getRequest(), fi.getResponse());
    } finally {
      super.afterInvocation(token, null);
    }
  }
}
