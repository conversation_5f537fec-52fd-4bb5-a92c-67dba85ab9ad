package com.key.win.config.security;

import com.key.win.config.security.filter.CustomizeAbstractSecurityInterceptor;
import com.key.win.config.security.filter.CustomizeAccessDecisionManager;
import com.key.win.config.security.filter.CustomizeFilterInvocationSecurityMetadataSource;
import com.key.win.config.security.filter.JwtAuthenticationTokenFilter;
import com.key.win.config.security.service.SecurityUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import org.springframework.security.web.session.InvalidSessionStrategy;
import org.springframework.security.web.session.SessionInformationExpiredStrategy;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/25 10:27
 * @description SpringSecurity的配置类
 */
@Configuration
@EnableWebSecurity
public class MySecurityConfig extends WebSecurityConfigurerAdapter {

  @Autowired private SecurityUserService securityUserService;

  @Autowired private AuthenticationEntryPoint authenticationEntryPoint;

  @Autowired private AuthenticationFailureHandler authenticationFailureHandler;

  @Autowired private LogoutSuccessHandler logoutSuccessHandler;

  @Autowired private AuthenticationSuccessHandler authenticationSuccessHandler;

  @Autowired private SessionInformationExpiredStrategy sessionInformationExpiredStrategy;

  @Autowired private InvalidSessionStrategy customizeSessionInvalidStrategy;

  @Autowired private CustomizeAbstractSecurityInterceptor customizeAbstractSecurityInterceptor;

  @Autowired private CustomizeAccessDecisionManager customizeAccessDecisionManager;

  @Autowired
  private CustomizeFilterInvocationSecurityMetadataSource
      customizeFilterInvocationSecurityMetadataSource;

  @Autowired private CustomizeAccessDeniedHandler customizeAccessDeniedHandler;

  @Autowired private JwtAuthenticationTokenFilter jwtAuthenticationTokenFilter;

  @Autowired private CustomizeAuthenticationEntryPoint customizeAuthenticationEntryPoint;

  /* @Override
  public void configure(WebSecurity web) throws Exception {
  	super.configure(web);
  }
  */
  @Override
  public void configure(WebSecurity webSecurity) {
    webSecurity
        .ignoring()
        .antMatchers(
            "/ws/**", "/notify/task/**",
            "/log/export/**",
            "/alarm/export/**",
            "/realtime-alarm.html",
            "/index.html",
            "/api/realtime-alarm/**",
            "/api/snmp-config/**",
            "/snmp-config.html",
            "/topo/exportTopo/**",
            "/topo/importTopo/**",
            "/sf/config/logo/getLogo",
            "/sf/boot/version/changeLine/**");
  }

  /**
   * 对请求进行鉴权的配置
   *
   * @param http
   * @throws Exception
   */
  @Override
  protected void configure(HttpSecurity http) throws Exception {

    http.cors().and().csrf().disable();
    http.httpBasic().disable();

    http.authorizeRequests()
        .anyRequest()
        .authenticated()
        .and()
        .exceptionHandling()
        .authenticationEntryPoint(authenticationEntryPoint)
        .accessDeniedHandler(customizeAccessDeniedHandler)
        .and()
        .formLogin()
        .successHandler(authenticationSuccessHandler) // 	登录成功处理逻辑
        .failureHandler(authenticationFailureHandler) // 	登录失败处理逻辑
        .and()
        .logout()
        .logoutSuccessHandler(logoutSuccessHandler) // 	登出成功处理逻辑
        .clearAuthentication(true)
        .deleteCookies("JSESSIONID") // 	登出之后删除cookie
        .and()
        .sessionManagement() //	会话管理
        .sessionCreationPolicy(SessionCreationPolicy.STATELESS);
    //                    .invalidSessionStrategy(customizeSessionInvalidStrategy) // 会话失效
    //                    .maximumSessions(1)     //	同一账号同时登录最大用户数
    //                    .expiredSessionStrategy(sessionInformationExpiredStrategy);
    //
    //        http.authorizeRequests().
    //        withObjectPostProcessor(new ObjectPostProcessor<FilterSecurityInterceptor>() {
    //            @Override
    //            public <O extends FilterSecurityInterceptor> O postProcess(O o) {
    //                o.setAccessDecisionManager(customizeAccessDecisionManager);//访问决策管理器
    //
    // o.setSecurityMetadataSource(customizeFilterInvocationSecurityMetadataSource);//安全元数据源
    //                return o;
    //            }
    //        });

    http.addFilterBefore(jwtAuthenticationTokenFilter, UsernamePasswordAuthenticationFilter.class);
    //        .addFilterBefore(customizeAbstractSecurityInterceptor,
    // FilterSecurityInterceptor.class)
    //        .exceptionHandling().authenticationEntryPoint(customizeAuthenticationEntryPoint);

    http.headers().cacheControl();
  }

  @Override
  protected void configure(AuthenticationManagerBuilder auth) throws Exception {
    auth.userDetailsService(securityUserService);
  }

  /**
   * 默认开启密码加密，前端传入的密码Security会在加密后和数据库中的密文进行比对，一致的话就登录成功 所以必须提供一个加密对象，供security加密前端明文密码使用
   *
   * @return
   */
  @Bean
  PasswordEncoder passwordEncoder() {
    return new BCryptPasswordEncoder();
  }
}
