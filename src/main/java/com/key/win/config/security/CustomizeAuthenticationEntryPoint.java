package com.key.win.config.security;

import com.alibaba.fastjson.JSON;
import com.key.win.common.result.JsonResult;
import com.key.win.common.result.ResultCode;
import com.key.win.common.result.ResultTool;

import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/28 15:31
 * @description 用户未登录
 */
@Component
@SuppressWarnings("rawtypes")
public class CustomizeAuthenticationEntryPoint implements AuthenticationEntryPoint {
  @Override
  public void commence(
      HttpServletRequest httpServletRequest,
      HttpServletResponse httpServletResponse,
      AuthenticationException e)
      throws IOException, ServletException {
    JsonResult result = ResultTool.fail(ResultCode.TOKEN_NOT_FOUND);
    httpServletResponse.setContentType("text/json;charset=utf-8");
    httpServletResponse.getWriter().write(JSON.toJSONString(result));
  }
}
