package com.key.win.config.security;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.common.result.JsonResult;
import com.key.win.common.result.ResultCode;
import com.key.win.common.result.ResultTool;
import com.key.win.common.utils.JwtUtils;
import com.key.win.system.core.dao.SysUserMapper;
import com.key.win.system.core.model.SysRequestPath;
import com.key.win.system.core.model.SysUser;
import com.key.win.system.core.service.IRoleService;
import com.key.win.system.core.vo.RoleVO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/28 15:39
 * @description 登录成功
 */
@Component
@SuppressWarnings("rawtypes")
public class CustomizeAuthenticationSuccessHandler implements AuthenticationSuccessHandler {
  @Autowired SysUserMapper mapper;

  @Autowired private IRoleService roleService;

  @Override
  public void onAuthenticationSuccess(
      HttpServletRequest httpServletRequest,
      HttpServletResponse httpServletResponse,
      Authentication authentication)
      throws IOException, ServletException {
    // 更新用户表上次登录时间、更新人、更新时间等字段
    User userDetails = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    LambdaQueryWrapper<SysUser> lqw = new LambdaQueryWrapper<SysUser>();
    lqw.eq(SysUser::getAccount, userDetails.getUsername());
    SysUser sysUser = mapper.selectOne(lqw);
    sysUser.setLastLoginTime(new Date());
    sysUser.setUpdateTime(new Date());
    sysUser.setUpdateUser(sysUser.getId());
    mapper.updateById(sysUser);

    // 	根据用户的id和account生成token并返回
    String jwtToken = JwtUtils.getJwtToken(sysUser.getId().toString(), sysUser.getAccount());

    Map<String, Object> results = new HashMap<>();
    sysUser.setPassword("");

    List<String> roleCodeList = new ArrayList<String>();
    //	获取用户的角色信息
    List<RoleVO> userRoleList = roleService.rolesByUserId(sysUser.getId());
    // 	将roleCode为null的角色过滤之后返给前端使用
    List<RoleVO> userRoleListToPortal = new ArrayList<RoleVO>();
    userRoleList.stream()
        .forEach(
            x -> {
              if (StringUtils.isNotBlank(x.getRoleCode())) {
                roleCodeList.add(x.getRoleCode());
                userRoleListToPortal.add(x);
              }
            });

    /** 当前用户所有角色可访问资源菜单并集 */
    List<SysRequestPath> currentUserFullMenuList = roleService.resourcesByRoleIdList(roleCodeList);

    results.put("user", sysUser);
    results.put("token", jwtToken);
    results.put("roles", userRoleListToPortal);
    results.put("requestPath", currentUserFullMenuList);

    // 返回json数据
    JsonResult result = ResultTool.success(ResultCode.SUCCESS_login, results);
    // 处理编码方式，防止中文乱码的情况
    httpServletResponse.setContentType(MediaType.APPLICATION_JSON_VALUE);
    httpServletResponse.setCharacterEncoding("UTF-8");
    // 把Json数据放入HttpServletResponse中返回给前台
    httpServletResponse.getWriter().write(JSON.toJSONString(result));
  }
}
