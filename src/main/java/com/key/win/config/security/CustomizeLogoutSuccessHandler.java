package com.key.win.config.security;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.key.win.common.result.JsonResult;
import com.key.win.common.result.ResultCode;
import com.key.win.common.result.ResultTool;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/28 15:38
 * @description 退出登录
 */
@Component
@SuppressWarnings("rawtypes")
public class CustomizeLogoutSuccessHandler implements LogoutSuccessHandler {
  @Override
  public void onLogoutSuccess(
      HttpServletRequest httpServletRequest,
      HttpServletResponse httpServletResponse,
      Authentication authentication)
      throws IOException, ServletException {

    JsonResult result = ResultTool.success(ResultCode.SUCCESS_logout);
    httpServletResponse.setContentType(MediaType.APPLICATION_JSON_VALUE);
    httpServletResponse.setCharacterEncoding("UTF-8");
    httpServletResponse.getWriter().write(JSON.toJSONString(result));
  }
}
