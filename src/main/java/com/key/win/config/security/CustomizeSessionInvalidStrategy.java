package com.key.win.config.security;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.web.session.InvalidSessionStrategy;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.key.win.common.result.JsonResult;
import com.key.win.common.result.ResultCode;
import com.key.win.common.result.ResultTool;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/28 16:39
 * @description 管理session，但是暂时没生效
 */
@Component
@SuppressWarnings("rawtypes")
public class CustomizeSessionInvalidStrategy implements InvalidSessionStrategy {

  @Override
  public void onInvalidSessionDetected(HttpServletRequest request, HttpServletResponse response)
      throws IOException, ServletException {
    JsonResult result = ResultTool.fail(ResultCode.SESSION_INVALID);
    response.setContentType("text/json;charset=utf-8");
    response.getWriter().write(JSON.toJSONString(result));
  }
}
