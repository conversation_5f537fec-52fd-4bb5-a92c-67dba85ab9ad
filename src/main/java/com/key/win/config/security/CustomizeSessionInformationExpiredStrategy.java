package com.key.win.config.security;

import com.alibaba.fastjson.JSON;
import com.key.win.common.result.JsonResult;
import com.key.win.common.result.ResultCode;
import com.key.win.common.result.ResultTool;

import org.springframework.security.web.session.SessionInformationExpiredEvent;
import org.springframework.security.web.session.SessionInformationExpiredStrategy;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/28 16:39
 * @description 管理session，但是暂时没生效
 */
@Component
@SuppressWarnings("rawtypes")
public class CustomizeSessionInformationExpiredStrategy
    implements SessionInformationExpiredStrategy {
  @Override
  public void onExpiredSessionDetected(
      SessionInformationExpiredEvent sessionInformationExpiredEvent)
      throws IOException, ServletException {
    JsonResult result = ResultTool.fail(ResultCode.USER_ACCOUNT_USE_BY_OTHERS);
    HttpServletResponse httpServletResponse = sessionInformationExpiredEvent.getResponse();
    httpServletResponse.setContentType("text/json;charset=utf-8");
    httpServletResponse.getWriter().write(JSON.toJSONString(result));
  }
}
