package com.key.win.config.security;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.key.win.common.result.JsonResult;
import com.key.win.common.result.ResultCode;
import com.key.win.common.result.ResultTool;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/29 9:52
 * @description 没有权限
 */
@Component
@SuppressWarnings("rawtypes")
public class CustomizeAccessDeniedHandler implements AccessDeniedHandler {
  @Override
  public void handle(
      HttpServletRequest httpServletRequest,
      HttpServletResponse httpServletResponse,
      AccessDeniedException e)
      throws IOException, ServletException {
    JsonResult noPermission = ResultTool.fail(ResultCode.NO_PERMISSION);
    // 处理编码方式，防止中文乱码的情况
    httpServletResponse.setCharacterEncoding("UTF-8");
    httpServletResponse.setContentType(MediaType.APPLICATION_JSON_VALUE);
    // 塞到HttpServletResponse中返回给前台
    httpServletResponse.getWriter().write(JSON.toJSONString(noPermission));
  }
}
