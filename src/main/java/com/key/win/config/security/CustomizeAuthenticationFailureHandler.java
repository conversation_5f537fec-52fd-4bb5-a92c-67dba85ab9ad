package com.key.win.config.security;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.MediaType;
import org.springframework.security.authentication.AccountExpiredException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.CredentialsExpiredException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.key.win.common.result.JsonResult;
import com.key.win.common.result.ResultCode;
import com.key.win.common.result.ResultTool;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/28 15:37
 * @description 登录账户时失败的处理
 */
@Component
@SuppressWarnings("rawtypes")
public class CustomizeAuthenticationFailureHandler implements AuthenticationFailureHandler {

  @Override
  public void onAuthenticationFailure(
      HttpServletRequest httpServletRequest,
      HttpServletResponse httpServletResponse,
      AuthenticationException e)
      throws IOException, ServletException {
    // 返回json数据
    JsonResult result = null;
    if (e instanceof AccountExpiredException) {
      // 账号过期
      result = ResultTool.fail(ResultCode.USER_ACCOUNT_EXPIRED);
    } else if (e instanceof BadCredentialsException) {
      // 密码错误
      result = ResultTool.fail(ResultCode.USER_CREDENTIALS_ERROR);
    } else if (e instanceof CredentialsExpiredException) {
      // 密码过期
      result = ResultTool.fail(ResultCode.USER_CREDENTIALS_EXPIRED);
    } else if (e instanceof DisabledException) {
      // 账号不可用
      result = ResultTool.fail(ResultCode.USER_ACCOUNT_DISABLE);
    } else if (e instanceof LockedException) {
      // 账号锁定
      result = ResultTool.fail(ResultCode.USER_ACCOUNT_LOCKED);
    } else if (e instanceof InternalAuthenticationServiceException) {
      // 用户不存在
      result = ResultTool.fail(ResultCode.USER_ACCOUNT_NOT_EXIST);
    } else {
      // 其他错误
      result = ResultTool.fail(ResultCode.COMMON_FAIL);
    }
    // 处理编码方式，防止中文乱码的情况
    httpServletResponse.setContentType(MediaType.APPLICATION_JSON_VALUE);
    httpServletResponse.setCharacterEncoding("UTF-8");
    // 把Json数据放入到HttpServletResponse中返回给前台
    httpServletResponse.getWriter().write(JSON.toJSONString(result));
  }
}
