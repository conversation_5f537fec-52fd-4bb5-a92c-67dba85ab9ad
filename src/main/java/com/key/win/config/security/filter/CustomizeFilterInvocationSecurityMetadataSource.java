package com.key.win.config.security.filter;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.access.SecurityConfig;
import org.springframework.security.web.FilterInvocation;
import org.springframework.security.web.access.intercept.FilterInvocationSecurityMetadataSource;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;

import com.key.win.system.core.dao.SysPermissionMapper;
import com.key.win.system.core.service.IRoleService;
import com.key.win.system.core.vo.RequestPathVO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/28 16:48
 * @description 根据请求，查询数据库，看看这个请求是那些角色能访问
 */
@Component
@Slf4j
public class CustomizeFilterInvocationSecurityMetadataSource
    implements FilterInvocationSecurityMetadataSource {
  AntPathMatcher antPathMatcher = new AntPathMatcher();

  @Autowired SysPermissionMapper sysPermissionMapper;

  @Autowired IRoleService roleService;

  @Override
  public Collection<ConfigAttribute> getAttributes(Object o) throws IllegalArgumentException {
    // 	获取请求地址
    String requestPath = ((FilterInvocation) o).getRequestUrl();
    // 	查询具体某个接口的权限
    // 	List<SysPermission> permissionList =  sysPermissionMapper.selectListByPath(requestUrl);
    List<RequestPathVO> currentUrlNeedRolesList = roleService.rolesByRequestPath(requestPath);
    if (currentUrlNeedRolesList == null || currentUrlNeedRolesList.size() == 0) {
      // 	请求路径没有配置权限，表明该请求接口可以任意访问
      return null;
    }
    ArrayList<String> roleStrList = new ArrayList<>(currentUrlNeedRolesList.size());
    currentUrlNeedRolesList.stream()
        .forEach(
            x -> {
              roleStrList.add(x.getRoleCode());
            });
    return SecurityConfig.createList(roleStrList.toArray(new String[roleStrList.size()]));
  }

  @Override
  public Collection<ConfigAttribute> getAllConfigAttributes() {
    return null;
  }

  @Override
  public boolean supports(Class<?> aClass) {
    return true;
  }
}
