package com.key.win.config.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.TEL.bootstrap.TELService;
import com.key.win.biz.alarm.model.RealTimeWarn;
import com.key.win.biz.alarm.service.RealTimeWarnSevice;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.config.mqtt.MqttPushClient;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SfSystemRedisKeyExpirationEventMessageListener
    extends KeyExpirationEventMessageListener {

  @Resource private Environment environment;
  @Resource private RealTimeWarnSevice realTimeWarnSevice;
  @Resource private BelongUnitService belongUnitService;

  public SfSystemRedisKeyExpirationEventMessageListener(
      RedisMessageListenerContainer listenerContainer) {
    super(listenerContainer);
  }

  @Override
  public void onMessage(Message message, byte[] pattern) {

    String _vl = environment.getProperty("sf.tel.enable");
    boolean vl = Boolean.parseBoolean(_vl);
    if (!vl) {
      log.info(
          "[SfSystemRedisKeyExpirationEventMessageListener]配置项{}当前值为{},未启用。如需启用请修改相应配置!",
          "sf.tel.enable",
          _vl);
      return;
    }

    String expiredKey = message.toString();
    expiredKey = expiredKey.replace("\"", "");
    try {
      boolean flag = StringUtils.startsWithIgnoreCase(expiredKey, "sf:alarm:report:");
      if (flag) {
        log.info("告警触发失效key:[{}]!", expiredKey);
        String[] split = expiredKey.split(":");
        String alarmBeanId = split[split.length - 1];

        LambdaQueryWrapper<RealTimeWarn> lqw = new LambdaQueryWrapper<>();
        lqw.eq(RealTimeWarn::getId, alarmBeanId);
        lqw.eq(RealTimeWarn::getAlarmStatus, GConfig.alarmStatus_realTime);
        lqw.eq(RealTimeWarn::getEnableFlag, true);
        RealTimeWarn alarmBean = realTimeWarnSevice.getOne(lqw);
        if (alarmBean == null) {
          return;
        } else {
          String reportMsg =
              TELService.packageMsgProactiveReporting(
                  belongUnitService, alarmBean, 1, TELService.reportMsgFF);
          if (StringUtils.isBlank(reportMsg)) {
            return;
          }
          MqttPushClient.getInstance()
              .publish2CsharpClient(GConfig.MQ_TOPIC_CSHARP_CLIENT_ALERT_INFO, reportMsg);
          log.info(
              "[(已经超过延迟时间)延迟模式开始上报告警],车站:{}监控主机:{},天线:{} ",
              alarmBean.getBelongStationName(),
              alarmBean.getHostNumber(),
              alarmBean.getNetworkName());
        }
      }

    } catch (Exception e) {
      e.printStackTrace();
      log.error("处理redis 过期的key异常：{}", expiredKey, e);
    }
  }
}
