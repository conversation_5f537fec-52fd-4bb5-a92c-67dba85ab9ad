package com.key.win.config.mqtt;

import cn.hutool.core.lang.UUID;
import com.key.win.mqtt.topic.consumer.MqttConsumerCallBack;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2021-12-08
 */
public class MqttSubClient {

    private static final Logger log = LoggerFactory.getLogger(MqttSubClient.class);
    public static String url = "";
    public static int port = 0;

    public static String MQTT_USERNAME = "";

    public static String MQTT_PASSWORD = "";

    public static String client_name =
            "sf_client_subscribe_" + UUID.randomUUID().toString().replace("-", "");

    public static int MQTT_TIMEOUT = 30;
    public static int MQTT_KEEPALIVE = 30;

    private MqttClient client;
    private static volatile MqttSubClient mqttClient = null;

    public static MqttSubClient getInstance(String url, int port, String username, String password) {
        boolean flag = false;
        log.debug("11.MqttSubClient获取MQTT实例.....");
        ;
        if (mqttClient == null) {
            log.debug("22.MqttSubClient 为 null,开始尝试建立连接.....");
            ;
            synchronized (MqttSubClient.class) {
                if (mqttClient == null) {
                    flag = true;
                    mqttClient = new MqttSubClient(url, port, username, password);
                    log.debug("33.MqttSubClient 创建成功");
                    ;
                }
            }
        }

        if (flag) {
            log.debug("44.返回MqttSubClient实例");
            ;
        } else {
            log.debug("44-4.获取MqttSubClient实例成功.");
            ;
        }
        return mqttClient;
    }

    public static MqttSubClient getInstance() {
        boolean flag = false;
        log.debug("11.MqttSubClient获取MQTT实例.....");
        ;
        if (mqttClient == null) {
            log.debug("22.MqttSubClient 为 null,开始尝试建立连接.....");
            ;
            synchronized (MqttSubClient.class) {
                if (mqttClient == null) {
                    flag = true;
                    mqttClient = new MqttSubClient();
                    log.debug("33.MqttSubClient 创建成功");
                    ;
                }
            }
        }

        if (flag) {
            log.debug("44.返回MqttSubClient实例");
            ;
        } else {
            log.debug("44-4.获取MqttSubClient实例成功.");
            ;
        }
        return mqttClient;
    }

    private MqttSubClient(String url, int port, String username, String password) {
        MqttSubClient.url = url;
        MqttSubClient.port = port;
        MqttSubClient.MQTT_USERNAME = username;
        MqttSubClient.MQTT_PASSWORD = password;
        connect(MqttSubClient.url, MqttSubClient.port);
    }

    private MqttSubClient() {
        connect();
    }

    private void connect() {
        try {
            client = new MqttClient(url + ":" + port, client_name, new MemoryPersistence());
            MqttConnectOptions option = new MqttConnectOptions();
            option.setCleanSession(true);
            option.setUserName(MQTT_USERNAME);
            option.setPassword(MQTT_PASSWORD.toCharArray());
            option.setConnectionTimeout(MQTT_TIMEOUT);
            option.setKeepAliveInterval(MQTT_KEEPALIVE);
            option.setAutomaticReconnect(true);
            try {
                client.setCallback(new MqttConsumerCallBack());
                client.connect(option);
                log.info("MQTT订阅客户端连接成功: {}", url + ":" + port);
            } catch (Exception e) {
                log.error("MQTT订阅客户端连接失败: {}", e.getMessage(), e);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void connect(String url, int port) {
        try {
            client = new MqttClient(url + ":" + port, client_name, new MemoryPersistence());
            MqttConnectOptions option = new MqttConnectOptions();
            option.setCleanSession(true);
            option.setUserName(MQTT_USERNAME);
            option.setPassword(MQTT_PASSWORD.toCharArray());
            option.setConnectionTimeout(MQTT_TIMEOUT);
            option.setKeepAliveInterval(MQTT_KEEPALIVE);
            option.setAutomaticReconnect(true);

            try {
                client.setCallback(new MqttConsumerCallBack());
                client.connect(option);
                log.info("MQTT订阅客户端连接成功: {}", url + ":" + port);
            } catch (Exception e) {
                log.error("MQTT订阅客户端连接失败: {}", e.getMessage(), e);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 订阅某个主题 qos默认为 0
     *
     * @param topic
     */
    public void subscribe(String topic) {
        subscribe(topic, 0);
    }

    public void unSubscribe(String topic) {
        try {
            client.unsubscribe(topic);
        } catch (MqttException e) {
            log.error("[订阅][Biz][Topic][{}]订阅失败,失败原因:{}", topic, e.getMessage());
            log.info("[订阅][Biz][Topic][{}]订阅失败,失败原因:{}", topic, e.getMessage());
            log.warn("[订阅][Biz][Topic][{}]订阅失败,失败原因:{}", topic, e.getMessage());
        }
    }

    /**
     * 订阅某个主题
     *
     * @param topic
     * @param qos
     */
    public void subscribe(String topic, int qos) {
        try {
            client.subscribe(topic, qos);
        } catch (Exception e) {
            log.error("[订阅][Biz][Topic][{}]订阅失败,失败原因:{}", topic, e.getMessage());
            log.info("[订阅][Biz][Topic][{}]订阅失败,失败原因:{}", topic, e.getMessage());
            log.warn("[订阅][Biz][Topic][{}]订阅失败,失败原因:{}", topic, e.getMessage());
        }
    }
}
