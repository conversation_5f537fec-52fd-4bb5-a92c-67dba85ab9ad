package com.key.win.config.mqtt;

import cn.hutool.core.lang.UUID;
import com.key.win.utils.ByteUtil;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.eclipse.paho.client.mqttv3.MqttTopic;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2021-12-08
 */
public class MqttPushClient {

    private static final Logger log = LoggerFactory.getLogger(MqttPushClient.class);
    public static String url = "";
    public static int port = 0;

    public static String MQTT_USERNAME = "";

    public static String MQTT_PASSWORD = "";

    public static String client_name =
            "sf_client_publish_" + UUID.randomUUID().toString().replace("-", "");

    public static int MQTT_TIMEOUT = 30;
    public static int MQTT_KEEPALIVE = 30;
    private static volatile MqttPushClient mqttClient = null;
    private MqttClient client;

    private MqttPushClient() {
        connect();
    }

    private MqttPushClient(String url, int port, String username, String password) {
        MqttPushClient.url = url;
        MqttPushClient.port = port;
        MqttPushClient.MQTT_USERNAME = username;
        MqttPushClient.MQTT_PASSWORD = password;

        connect(url, port);
    }

    public static MqttPushClient getInstance() {
        boolean flag = false;
        log.debug("1.获取MQTT实例.....");
        if (mqttClient == null) {
            log.debug("2.mqttClient 为 null,开始尝试建立连接.....");
            ;
            synchronized (MqttPushClient.class) {
                if (mqttClient == null) {
                    flag = true;
                    mqttClient = new MqttPushClient();
                    log.debug("3.mqttClient 创建成功");
                    ;
                }
            }
        }

        if (flag) {
            log.debug("4.返回Client实例");
        } else {
            log.debug("4-4.获取client实例成功.");
        }
        return mqttClient;
    }

    public static MqttPushClient getInstance(String url, int port, String username, String password) {
        boolean flag = false;
        log.debug("1.获取MQTT实例.....");
        if (mqttClient == null) {
            log.debug("2.mqttClient 为 null,开始尝试建立连接.....");
            ;
            synchronized (MqttPushClient.class) {
                if (mqttClient == null) {
                    flag = true;
                    mqttClient = new MqttPushClient(url, port, username, password);
                    log.debug("3.mqttClient 创建成功");
                    ;
                }
            }
        }

        if (flag) {
            log.debug("4.返回Client实例");
            ;
        } else {
            log.debug("4-4.获取client实例成功.");
            ;
        }
        return mqttClient;
    }

    private void connect() {
        try {
            client = new MqttClient(url + ":" + port, client_name, new MemoryPersistence());
            MqttConnectOptions option = new MqttConnectOptions();
            option.setCleanSession(true);
            option.setUserName(MQTT_USERNAME);
            option.setPassword(MQTT_PASSWORD.toCharArray());
            option.setConnectionTimeout(MQTT_TIMEOUT);
            option.setKeepAliveInterval(MQTT_KEEPALIVE);
            option.setAutomaticReconnect(true);
            option.setMaxReconnectDelay(30000); // 设置最大重连延迟为60秒

            // 设置遗嘱消息
            option.setWill("will_topic", "close".getBytes(), 1, true);
            // 设置遗嘱消息
            option.setWill("will_topic", "close".getBytes(), 1, true);
            try {
                client.connect(option);
                log.info("MQTT发布客户端连接成功: {}", url + ":" + port);
            } catch (Exception e) {
                log.error("MQTT发布客户端连接失败: {}", e.getMessage(), e);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void connect(String url, int port) {
        try {
            client = new MqttClient(url + ":" + port, client_name, new MemoryPersistence());
            MqttConnectOptions option = new MqttConnectOptions();
            option.setCleanSession(true);
            option.setUserName(MQTT_USERNAME);
            option.setPassword(MQTT_PASSWORD.toCharArray());
            option.setConnectionTimeout(MQTT_TIMEOUT);
            option.setKeepAliveInterval(MQTT_KEEPALIVE);
            option.setAutomaticReconnect(true);
            option.setMaxReconnectDelay(30000); // 设置最大重连延迟为60秒

            // 设置遗嘱消息
            try {
                try {
                    client.connect(option);
                    log.info("MQTT发布客户端连接成功: {}", url + ":" + port);
                } catch (Exception e) {
                    log.error("MQTT发布客户端连接失败: {}", e.getMessage(), e);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void publishProbeInfo(String topic, String data) {
        publishString(topic, data);
    }

    public void publishDeviceOnlineOrOffline(String topic, String data) {
        publishString(topic, data);
    }

    public void publishDeviceProbeLinkCheck(String topic, String data) {
        publishString(topic, data);
    }

    public void publish2CsharpClient(String topic, String data) {
        if (StringUtils.isBlank(data)) {
            log.warn("上报给Topic:{}的信息为empty,只记录日志不多余上报!", topic);
            return;
        }
        publishString(topic, data);
    }

    public void publishDeviceProbeSignalStrengthCheck(String topic, String data) {
        publishString(topic, data);
    }

    public void publishString(String topic, String data) {
        if (StringUtils.isBlank(data)) {
            log.error("发送给{}当前报文内容不存在,忽略本次发送", topic);
            return;
        }
        MqttMessage message = new MqttMessage();
        message.setQos(0);
        message.setRetained(false);
        byte[] dataByte = data.getBytes();
        message.setPayload(dataByte);

        if (client == null) {
            log.error("当前EMQX-client is NULL.请检查相关配置.");
            return;
        }

        MqttTopic mqttTopic = client.getTopic(topic);
        if (null == mqttTopic) {
            log.error("Topic Not Exist");
        }
        try {
            log.debug("即将执行发布指令:{},deviceId:{}", data, topic);
            mqttTopic.publish(message);

            log.debug("发布完毕,线程睡眠10s");
            Thread.sleep(1);
            log.debug("线程恢复");

            //      token.waitForCompletion();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 发布主题，用于通知<br>
     * 默认qos为1 非持久化
     *
     * @param topic
     * @param data
     */
    public void publish(String topic, String data) {
        publish(1, false, topic, data);
    }

    /**
     * 发布
     *
     * @param topic
     * @param data
     * @param qos
     * @param retained
     */
    public void publish(int qos, boolean retained, String topic, String data) {
        if (StringUtils.isBlank(data)) {
            log.error("当前报文存在问题,忽略本次发送");
            return;
        }
        MqttMessage message = new MqttMessage();
        message.setQos(qos);
        message.setRetained(retained);
        byte[] dataByte = ByteUtil.hexStringToByteArray(data);
        message.setPayload(dataByte);

        if (client == null) {
            log.error("当前EMQX-client is NULL.请检查相关配置.");
            return;
        }

        MqttTopic mqttTopic = client.getTopic(topic);
        if (null == mqttTopic) {
            log.error("Topic Not Exist");
        }
        try {
            log.debug("即将执行发布指令:{},deviceId:{}", data, topic);
            mqttTopic.publish(message);
            log.debug("发布完毕,线程睡眠10s");
            Thread.sleep(1);
            log.debug("线程恢复");

            //      token.waitForCompletion();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 不用等待
     *
     * @param qos
     * @param retained
     * @param topic
     * @param data
     */
    public void publishWaitTime(int qos, boolean retained, String topic, String data, long wait) {
        MqttMessage message = new MqttMessage();
        message.setQos(qos);
        message.setRetained(retained);
        byte[] dataByte = ByteUtil.hexStringToByteArray(data);
        message.setPayload(dataByte);

        if (client == null) {
            log.error("当前EMQX-client is NULL.请检查相关配置.");
            return;
        }

        MqttTopic mqttTopic = client.getTopic(topic);
        if (null == mqttTopic) {
            log.error("Topic Not Exist");
        }
        try {
            log.debug("即将执行发布指令:{},deviceId:{}", data, topic);
            mqttTopic.publish(message);

            if (wait <= 0) {
                wait = 100;
            }
            log.debug("发布完毕,线程睡眠{}s", wait / 1000);
            Thread.sleep(wait);
            log.debug("线程恢复");

            //      token.waitForCompletion();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
