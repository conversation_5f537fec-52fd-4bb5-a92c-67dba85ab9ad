package com.key.win.system.aop.aspect;

import java.util.List;

import javax.annotation.Resource;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.common.web.Result;
import com.key.win.config.RedisUtil;
import com.key.win.mqtt.websocket.server.WebSocketServer;
import com.key.win.system.aop.annotation.MqPubCheck;
import com.key.win.utils.SpringSecurityUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * Title: SystemControllerLog
 *
 * <p>Description: 切点类
 */
@Slf4j
@Aspect
@Component
@SuppressWarnings("all")
public class MqPubCheckAspect {

  @Resource private BelongUnitService belongUnitService;

  @Resource private RedisUtil redisUtil;

  private static final String key = "MQ_ANNOTATION_MQPUBCHECKASPECT_KEY";

  @Pointcut("@annotation(com.key.win.system.aop.annotation.MqPubCheck)")
  private void pubCheck() {}

  /**
   * @Description 前置通知 用于拦截Controller层记录用户的操作
   *
   * @date 2018年9月3日 10:38
   */
  @Around("pubCheck()")
  public void proxyPubCmd(ProceedingJoinPoint joinPoint) throws Throwable {

    Object[] args = joinPoint.getArgs(); // 切面方法的请求参数

    String deviceId = args[0].toString();
    String cacheKey = key + ":" + deviceId;

    MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
    MqPubCheck mqPubCheckAno = methodSignature.getMethod().getDeclaredAnnotation(MqPubCheck.class);

    String cmdName = mqPubCheckAno.cmdName(); // Mq命令类型
    long waitTimeSecend = mqPubCheckAno.waitTimeSecend(); // redis缓存时长
    boolean needCalculate = mqPubCheckAno.needCalculate();

    if (needCalculate) {
      waitTimeSecend = waitTime(deviceId);
      if (waitTimeSecend == -500) {
        Result<Object> succeed = Result.succeed("设备主机重复,终止执行命令!");
        WebSocketServer.sendInfo(JSON.toJSONString(succeed), SpringSecurityUtils.getUserName());
      }
      return;
    }

    log.info("当前准备执行的指令是:[{}],设备编号:[{}]", cmdName, deviceId);
    if (redisUtil.hasKey(cacheKey)) {
      Object cmdNameObj = redisUtil.get(cacheKey);
      long expire = redisUtil.getExpire(cacheKey);
      log.info("检查不通过×>>>>>>>>>>设备{}正在执行{}指令,请[{}]秒后再试", deviceId, cmdNameObj, expire);
      Result<Object> succeed =
          Result.succeed("设备[" + deviceId + "]正在执行[" + cmdNameObj + "]指令,请[" + expire + "]秒后再试");
      WebSocketServer.sendInfo(JSON.toJSONString(succeed), SpringSecurityUtils.getUserName());
      return;
    } else {
      log.info("检查通过√>>>>>>>>>即将开始执行{}指令>>>>>>>>>", cmdName);
      redisUtil.set(cacheKey, cmdName, waitTimeSecend);
      joinPoint.proceed(args);
    }
  }

  @After("pubCheck()")
  public void afterPubCheck() {
    log.info(">>>>>>>>>>>>指令下发结束!");
  }

  public long waitTime(String deviceId) {
    LambdaQueryWrapper<BelongUnit> lqw = new LambdaQueryWrapper<BelongUnit>();
    lqw.eq(BelongUnit::getHostNum, deviceId);
    List<BelongUnit> unitList = belongUnitService.list(lqw);
    if (unitList.size() != 1) {
      return -500;
    }
    BelongUnit belongUnit = unitList.get(0);
    int startPower = Integer.parseInt(belongUnit.getStartPower());
    int endPower = Integer.parseInt(belongUnit.getEndPower());

    // 结束功率-起始功率+1 * 10s 就是从 开始功率递增到结束功率 所耗时长
    int waitTime = (endPower - startPower + 1) * 7;
    return waitTime;
  }
}
