package com.key.win.system.aop.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SfLog {

  /**
   * 模块名称
   *
   * @return
   */
  String module() default "";
  /**
   * 描述
   *
   * @return
   */
  String description() default "";
  /**
   * 执行动作名称
   *
   * @return
   */
  String actionName() default "";

  LogType logType();
}
