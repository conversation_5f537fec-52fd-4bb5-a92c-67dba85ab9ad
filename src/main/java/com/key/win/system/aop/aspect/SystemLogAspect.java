// package com.key.win.system.aop.aspect;
//
// import java.lang.reflect.Method;
// import java.util.Date;
//
// import javax.annotation.Resource;
// import javax.servlet.http.HttpServletRequest;
//
// import org.apache.commons.lang3.StringUtils;
// import org.aspectj.lang.JoinPoint;
// import org.aspectj.lang.ProceedingJoinPoint;
// import org.aspectj.lang.annotation.Around;
// import org.aspectj.lang.annotation.Aspect;
// import org.aspectj.lang.annotation.Pointcut;
// import org.aspectj.lang.reflect.MethodSignature;
// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
// import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
// import org.springframework.stereotype.Component;
//
// import com.alibaba.fastjson.JSON;
// import com.alibaba.fastjson.JSONObject;
// import com.key.win.biz.log.model.OperationLog;
// import com.key.win.biz.log.service.OperationLogService;
// import com.key.win.system.aop.annotation.LogAnnotation;
// import com.key.win.utils.DateUtils;
// import com.key.win.utils.IPUtils;
// import com.key.win.utils.SpringSecurityUtils;
//
//
/// **
// * Title: SystemControllerLog
// * <p>
// * Description: 切点类
// */
// @Aspect
// @Component
// @SuppressWarnings("all")
// public class SystemLogAspect {
//    //注入Service用于把日志保存数据库，实际项目入库采用队列做异步
//    @Resource
//    private OperationLogService operationLogService;
//    //本地异常日志记录对象
//    private static final Logger logger = LoggerFactory.getLogger(SystemLogAspect.class);
//
//    @Pointcut("@annotation(com.key.win.system.aop.annotation.LogAnnotation)")
//    private void permissionCheck() {
//    }
//
//    @Resource
//    private HttpServletRequest request;
//
//    @Resource
//    private ThreadPoolTaskExecutor poolTaskExecutor;
//
//    /**
//     * @Description 前置通知  用于拦截Controller层记录用户的操作
//     * @date 2018年9月3日 10:38
//     */
//
//    @Around("permissionCheck()")
//    public Object permissionCheck(ProceedingJoinPoint joinPoint) throws Throwable {
//
//        Object result = null;
//        String ip = IPUtils.getIpAddr(request);
//        OperationLog operationLog = new OperationLog();
//        operationLog.setOperatIp(ip);
//        //*========控制台输出=========*//
//        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
//        LogAnnotation logAnnotation =
// methodSignature.getMethod().getDeclaredAnnotation(LogAnnotation.class);
//        try {
//            result = joinPoint.proceed();
//            // 实时告警业务
//            dealWithRealWarn(logAnnotation, methodSignature, operationLog, result);
//            operationLog.setUsername(SpringSecurityUtils.getUserName());
//            dealWithLoginAndLogOut(logAnnotation, methodSignature, operationLog, result);
//        } catch (Exception e) {
//
//        }
//
//        JSONObject jsonResult = JSONObject.parseObject(JSON.toJSONString(result));
//        String code = jsonResult.getString("code");
//        //*========数据库日志=========*//
//
//        operationLog.setCreateTime(DateUtils.dateTimeToStr(new Date()));
//        operationLog.setModule(logAnnotation.module() + ":" +
// methodSignature.getDeclaringTypeName() + "/"
//                + methodSignature.getName());
//
//        if ("0".equals(code)) {
//            operationLog.setFlag("成功");
//        } else {
//            operationLog.setFlag("失败");
//            String errorMessage = jsonResult.getString("msg");
//            operationLog.setContent(errorMessage);
//        }
//        poolTaskExecutor.setThreadNamePrefix("sfxt");
//        poolTaskExecutor.execute(new Thread(() -> {
//            //保存数据库
//            operationLogService.save(operationLog);
//            logger.debug("==============日志记录成功==============");
//        }));
//
//        return result;
//    }
//
//    private void dealWithLoginAndLogOut(LogAnnotation logAnnotation, MethodSignature
// methodSignature, OperationLog operationLog, Object result) {
//        if ("".equals(logAnnotation.module())) {
//            if ("login".equals(methodSignature.getName())) {
//                operationLog.setType("2");
//                operationLog.setContent("登录");
//            } else {
//                return;
//            }
//
//            JSONObject jsonResult = JSONObject.parseObject(JSON.toJSONString(result));
//            String stringData = jsonResult.getString("data");
//            if (StringUtils.isNotBlank(stringData)) {
//                JSONObject jsonData = JSONObject.parseObject(stringData);
//
// operationLog.setUsername(JSONObject.parseObject(jsonData.getString("user")).getString("username"));
//                operationLog.setContent("登录");
//                operationLog.setOperatResult(("成功"));
//            }
//        }
//    }
//
//    private void dealWithRealWarn(LogAnnotation logAnnotation, MethodSignature methodSignature,
// OperationLog operationLog, Object result) {
//        if ("basic-management".equals(logAnnotation.module())) {
//            if ("makeSuerWarn".equals(methodSignature.getName())) {
//                operationLog.setType("1");
//                operationLog.setContent("确认告警");
//            } else if ("clearWarn".equals(methodSignature.getName())) {
//                operationLog.setType("1");
//                operationLog.setContent("清除告警");
//            } else if ("deleteWarnById".equals(methodSignature.getName())) {
//                operationLog.setType("1");
//                operationLog.setContent("删除告警");
//            } else {
//                return;
//            }
//
//            JSONObject jsonResult = JSONObject.parseObject(JSON.toJSONString(result));
//            String stringData = jsonResult.getString("data");
//            if (StringUtils.isNotBlank(stringData)) {
//                JSONObject jsonData = JSONObject.parseObject(stringData);
//                operationLog.setOperationObject(jsonData.getString("alarmName"));
//                operationLog.setRemark("车站名称：" + jsonData.getString("belongStaionName"));
//                operationLog.setPid(jsonData.getString("id"));
//                operationLog.setOperatResult(("成功"));
//            }
//        }
//    }
//
//    /**
//     *
//     */
//    public static String getServiceMethodDescription(JoinPoint joinPoint) throws Exception {
//        String targetName = joinPoint.getTarget().getClass().getName();
//        String methodName = joinPoint.getSignature().getName();
//        Object[] arguments = joinPoint.getArgs();
//        Class targetClass = Class.forName(targetName);
//        Method[] methods = targetClass.getMethods();
//        String description = "";
//        for (Method method : methods) {
//            if (method.getName().equals(methodName)) {
//                Class[] clazzs = method.getParameterTypes();
//                if (clazzs.length == arguments.length) {
//                    description = method.getAnnotation(LogAnnotation.class).description();
//                    break;
//                }
//            }
//        }
//        return description;
//    }
// }
