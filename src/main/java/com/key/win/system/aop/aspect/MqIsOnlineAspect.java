package com.key.win.system.aop.aspect;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.config.RedisUtil;
import com.key.win.mqtt.Scheduling.config.runnable.DeviceCmdCallbackRunnable;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.mqtt.mq.cmd.EmqRequestService;
import com.key.win.mqtt.websocket.server.WebSocketServer;
import com.key.win.system.aop.annotation.MqIsOnlineCheck;
import com.key.win.utils.SpringSecurityUtils;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

/**
 * Title: SystemControllerLog
 *
 * <p>Description: 切点类
 */
@Slf4j
@Aspect
@Component
@SuppressWarnings("all")
public class MqIsOnlineAspect {

  @Resource private RedisUtil redisUtil;

  @Resource private BelongUnitService belongUnitService;

  @Autowired private EmqRequestService emqRequestService;

  @Resource private ThreadPoolTaskExecutor poolTaskExecutor;

  @Pointcut("@annotation(com.key.win.system.aop.annotation.MqIsOnlineCheck)")
  private void onlineCheck() {}

  @Around("onlineCheck()")
  public void proxyPubCmd(ProceedingJoinPoint joinPoint) throws Throwable {

    Object[] args = joinPoint.getArgs(); // 切面方法的请求参数
    String deviceId = args[0].toString();

    MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
    MqIsOnlineCheck onlineCheck =
        methodSignature.getMethod().getDeclaredAnnotation(MqIsOnlineCheck.class);

    boolean needCallback = onlineCheck.needCallback();
    String cmdName = onlineCheck.cmdName(); // Mq命令类型
    String cmdType = onlineCheck.cmdType();
    String validateCacheBaseKey = onlineCheck.validateCacheKey();
    long waitTime = waitTime(cmdType, deviceId);
    String cacheKey = validateCacheBaseKey + deviceId;

    boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + deviceId);
    if (!clientIsOnline) {
      belongUnitService.offline(deviceId);
      WebSocketServer.send(
          "设备[" + deviceId + "]当前处于离线状态,请检查网路链接!", SpringSecurityUtils.getUserName());
    } else {
      belongUnitService.online(deviceId);

      Object cmdNameObj = null;
      if (redisUtil.hasKey(cacheKey)) {
        cmdNameObj = redisUtil.get(cacheKey);
        long expire = redisUtil.getExpire(cacheKey);
        log.info("[Check][指令唯一性检查]设备{}正在执行[{}]指令,请[{}]秒后再试!", deviceId, cmdNameObj, expire);
        String msg = "设备[" + deviceId + "]正在执行[" + cmdNameObj + "]指令,请[" + expire + "]秒后再试";
        WebSocketServer.send(msg, SpringSecurityUtils.getUserName());
        return;
      }

      log.info("目标任务执行中,开始等待后续操作,等待时间:{}秒", waitTime);
      if (needCallback) {
        log.info("当前准备执行指令的设备编号:[{}]", deviceId);
        joinPoint.proceed(args);
        redisUtil.set(cacheKey, cmdName, waitTime);
        Class<? extends Runnable> callback = onlineCheck.callback();
        DeviceCmdCallbackRunnable newInstance = (DeviceCmdCallbackRunnable) callback.newInstance();
        newInstance.setWsId(SpringSecurityUtils.getUserName());
        newInstance.setWaitTimeSecond(waitTime);

        Thread thread = new Thread(newInstance);
        poolTaskExecutor.execute(thread);
        log.info("后续操作已移交给子线程执行,线程执行耗时约{}秒", waitTime);
      } else {
        joinPoint.proceed(args);
        redisUtil.set(cacheKey, cmdName, waitTime);
      }
      WebSocketServer.send("[" + cmdName + "]:指令已经下发", SpringSecurityUtils.getUserName());
    }
  }

  private long waitTime(String deviceId) {
    LambdaQueryWrapper<BelongUnit> lqw = new LambdaQueryWrapper<BelongUnit>();
    lqw.eq(BelongUnit::getHostNum, deviceId);
    List<BelongUnit> unitList = belongUnitService.list(lqw);
    if (unitList.size() != 1) {
      return -500;
    }
    BelongUnit belongUnit = unitList.get(0);
    int startPower = Integer.parseInt(belongUnit.getStartPower());
    int endPower = Integer.parseInt(belongUnit.getEndPower());

    // 结束功率-起始功率+1 * 10s 就是从 开始功率递增到结束功率 所耗时长
    long waitTime = (endPower - startPower + 1) * GConfig.getDeviceReadingWaitTimeSecond();
    return waitTime;
  }

  private long waitTime(String cmdType, String deviceId) {
    if (cmdType.equals(GConfig.CMD_TYPE_DEVICE_ARGUMENTINFO)) {
      return GConfig.getDeviceInfoAsyncWaitTimeSecond();
    } else if (cmdType.equals(GConfig.CMD_TYPE_FAULT)) {
      return GConfig.getDeviceFaultWaitTimeSecond();
    } else if (cmdType.equals(GConfig.CMD_TYPE_PATHLOSS)) {
      return waitTime(deviceId);
    } else if (cmdType.equals(GConfig.CMD_TYPE_DEVICE_REBOOT)) {
      return GConfig.getDeviceRebootWaitTimeSecond();
    } else {
      return 0;
    }
  }
}
