package com.key.win.system.aop.aspect;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.key.win.biz.log.model.OperationLog;
import com.key.win.biz.log.service.OperationLogService;
import com.key.win.system.aop.annotation.LogType;
import com.key.win.system.aop.annotation.SfLog;
import com.key.win.utils.DateUtils;
import com.key.win.utils.IPUtils;
import com.key.win.utils.SpringSecurityUtils;

/**
 * Title: SystemControllerLog
 *
 * <p>Description: 切点类
 */
@Aspect
@Component
@SuppressWarnings("all")
public class SfLogAspect {
  // 注入Service用于把日志保存数据库，实际项目入库采用队列做异步
  @Resource private OperationLogService operationLogService;
  // 本地异常日志记录对象
  private static final Logger logger = LoggerFactory.getLogger(SfLogAspect.class);

  @Pointcut("@annotation(com.key.win.system.aop.annotation.SfLog)")
  private void permissionCheck() {}

  @Resource private HttpServletRequest request;

  @Resource private ThreadPoolTaskExecutor poolTaskExecutor;

  /**
   * @Description 前置通知 用于拦截Controller层记录用户的操作
   *
   * @date 2018年9月3日 10:38
   */
  @Around("permissionCheck()")
  public Object permissionCheck(ProceedingJoinPoint joinPoint) throws Throwable {

    Object result = null;
    String ip = IPUtils.getIpAddr(request);
    // 方法参数值
    Object[] args = joinPoint.getArgs();
    Object deviceId = args[0];

    MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
    // 参数名称
    String[] parameterNames = methodSignature.getParameterNames();

    Map<String, Object> paramMap = new HashMap<String, Object>();
    for (int i = 0; i < args.length; i++) {
      paramMap.put(parameterNames[i], args[i]);
    }

    SfLog sfLog = methodSignature.getMethod().getDeclaredAnnotation(SfLog.class);
    String actionName = sfLog.actionName();
    String module = sfLog.module();
    LogType logType = sfLog.logType();
    result = joinPoint.proceed();

    OperationLog operationLog = new OperationLog();
    operationLog.setOperatIp(ip);
    operationLog.setContent(actionName); // 日志记录内容
    operationLog.setOperationObject(String.valueOf(deviceId));
    operationLog.setUsername(SpringSecurityUtils.getUserName());
    operationLog.setCreateTime(DateUtils.dateTimeToStr(new Date()));
    operationLog.setModule(module);
    operationLog.setType(logType.toString());
    if (!logType.equals(LogType.AuthLog)) {
      operationLog.setParams(JSON.toJSONString(paramMap));
    } else {
      operationLog.setParams("--");
    }

    if (result != null) {
      JSONObject jsonResult = JSONObject.parseObject(JSON.toJSONString(result));
      String code = jsonResult.getString("code");
      if ("0".equals(code)) {
        operationLog.setFlag("成功");
      } else {
        operationLog.setFlag("失败");
      }
      operationLog.setOperatResult(JSON.toJSONString(result));
    } else {
      operationLog.setFlag("成功");
      operationLog.setOperatResult("操作成功");
    }

    poolTaskExecutor.setThreadNamePrefix("sfxt");
    poolTaskExecutor.execute(
        new Thread(
            () -> {
              operationLogService.save(operationLog);
            }));

    return result;
  }
}
