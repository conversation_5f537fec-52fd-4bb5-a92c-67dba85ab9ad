package com.key.win.system.aop.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.TYPE})
public @interface MqIsOnlineCheck {

  boolean needCallback() default false;

  Class<? extends Runnable> callback() default Runnable.class;

  long callbackWaitTimeSecend() default 120;

  String cmdName() default "";

  String cmdType() default "";

  String validateCacheKey() default "";
}
