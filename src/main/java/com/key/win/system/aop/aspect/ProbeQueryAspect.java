package com.key.win.system.aop.aspect;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.key.win.biz.baseInfo.model.Probe;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.biz.baseInfo.service.ProbeService;
import com.key.win.config.RedisUtil;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.mqtt.mq.cmd.QueryCommandService;
import com.key.win.mqtt.mq.cmd.EmqRequestService;
import com.key.win.mqtt.websocket.server.WebSocketServer;
import com.key.win.system.aop.annotation.ProbePreQuery;
import com.key.win.utils.SpringSecurityUtils;

import java.util.*;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import static org.apache.naming.SelectorContext.prefix;

/**
 * 探针操作前置的探针查询
 *
 * <p>Description: 切点类
 */
@Slf4j
@Aspect
@Component
@SuppressWarnings("all")
public class ProbeQueryAspect {

  @Resource private RedisUtil redisUtil;

  @Resource private BelongUnitService belongUnitService;

  @Resource private ProbeService probeService;

  @Autowired private EmqRequestService emqRequestService;

  @Autowired private QueryCommandService queryCommandService;

  @Resource private ThreadPoolTaskExecutor poolTaskExecutor;

  @Pointcut("@annotation(com.key.win.system.aop.annotation.ProbePreQuery)")
  private void probeQuery() {}

  @Around("probeQuery()")
  public void proxyPubCmd(ProceedingJoinPoint joinPoint) throws Throwable {

    Object[] args = joinPoint.getArgs(); // 切面方法的请求参数
    String deviceId = args[0].toString();

    MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
    ProbePreQuery queryAno = methodSignature.getMethod().getDeclaredAnnotation(ProbePreQuery.class);

    String cmdName = queryAno.cmdName(); // Mq命令类型
    String cmdType = queryAno.cmdType();
    String validateCacheBaseKey = queryAno.validateCacheKey();
    long waitTime = 0;
    String cacheKey = validateCacheBaseKey + deviceId;

    boolean clientIsOnline = emqRequestService.clientIsOnline("reader_" + deviceId);
    if (!clientIsOnline) {
      belongUnitService.offline(deviceId);
      WebSocketServer.send(
          "设备[" + deviceId + "]当前设备处于离线状态,请检查网路链接!", SpringSecurityUtils.getUserName());
    } else {
      belongUnitService.online(deviceId);
      waitTime = waitTime(cmdType, deviceId);
      if (redisUtil.hasKey(cacheKey)) {
        Object cmdNameObj = redisUtil.get(cacheKey);
        long expire = redisUtil.getExpire(cacheKey);
        log.info("[Check][指令唯一性检查]设备{}正在执行[{}]指令,请[{}]秒后再试!", deviceId, cmdNameObj, expire);
        String msg = "设备[" + deviceId + "]正在执行[" + cmdNameObj + "]指令,请[" + expire + "]秒后再试";
        WebSocketServer.send(msg, SpringSecurityUtils.getUserName());
        return;
      }
      probeService.removeAllProbeByHostNumber(deviceId);
      queryCommandService.lookDeviceProbe(deviceId);
      joinPoint.proceed(args);
      redisUtil.set(cacheKey, cmdName, waitTime);
    }
  }

  private long waitTime(String deviceId) {
    LambdaQueryWrapper<BelongUnit> lqw = new LambdaQueryWrapper<BelongUnit>();
    lqw.eq(BelongUnit::getHostNum, deviceId);
    List<BelongUnit> unitList = belongUnitService.list(lqw);
    if (unitList.size() != 1) {
      return -500;
    }
    BelongUnit belongUnit = unitList.get(0);


    String startPowerStr = belongUnit.getStartPower();
    String endPowerStr = belongUnit.getEndPower();
    boolean flag1 = StringUtils.isBlank(startPowerStr);
    boolean flag2 = StringUtils.isBlank(endPowerStr);
    if(flag1){
      startPowerStr = "1";
    }
    if(flag2){
      endPowerStr = "5";
    }
    int startPower = Integer.parseInt(startPowerStr);
    int endPower = Integer.parseInt(endPowerStr);

    // 结束功率-起始功率+1 * 10s 就是从 开始功率递增到结束功率 所耗时长
    long waitTime = (endPower - startPower + 1) * GConfig.getDeviceReadingWaitTimeSecond();
    return waitTime;
  }

  private long waitTime(String cmdType, String deviceId) {
    if (cmdType.equals(GConfig.CMD_TYPE_DEVICE_ARGUMENTINFO)) {
      return GConfig.getDeviceInfoAsyncWaitTimeSecond();
    } else if (cmdType.equals(GConfig.CMD_TYPE_FAULT)) {
      return GConfig.getDeviceFaultWaitTimeSecond();
    } else if (cmdType.equals(GConfig.CMD_TYPE_PATHLOSS)) {
      return waitTime(deviceId);
    } else {
      return 0;
    }
  }

  private void updateAnt(String deviceId) {
    UpdateWrapper<Probe> lqw = new UpdateWrapper<Probe>();
    lqw.set("val_key", "temp");
    lqw.eq("host_num", deviceId);
    probeService.update(lqw);
  }

  //  探针信息改变触发
  @Pointcut(
      "execution(* com.key.win.biz.baseInfo.service.ProbeService.*(..))&&!execution(* com.key.win.biz.baseInfo.service.ProbeService.list*(..))")
  public void pointChange() {}

  @AfterReturning(value = "pointChange()", returning = "result")
  private void afterProbe(JoinPoint joinPoint, Object result) {
    List<Probe> list = probeService.list();
    //    删除所有探针信息缓存
    for (int i = 0; i < list.size(); i++) {
      Set<String> keys = redisUtil.getListKey(list.get(i).getHostNumber());
      Iterator it = keys.iterator();
      while (it.hasNext()) {
        String key = (String) it.next();
        redisUtil.del(key);
      }
    }
    //    添加最新探针数据至缓存
    for (int i = 0; i < list.size(); i++) {
      Probe probe = list.get(i);
      redisUtil.set(
          probe.getHostNumber() + probe.getNumber(),
          probe.getProbeStatus() + ":" + probe.getLost());
    }
  }
}
