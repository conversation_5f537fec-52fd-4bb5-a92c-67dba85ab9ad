package com.key.win.system.aop.event;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.context.ApplicationListener;
import org.springframework.security.authentication.event.AbstractAuthenticationFailureEvent;
import org.springframework.security.authentication.event.AuthenticationFailureBadCredentialsEvent;
import org.springframework.security.authentication.event.AuthenticationFailureCredentialsExpiredEvent;
import org.springframework.security.authentication.event.AuthenticationFailureDisabledEvent;
import org.springframework.security.authentication.event.AuthenticationFailureExpiredEvent;
import org.springframework.security.authentication.event.AuthenticationFailureLockedEvent;
import org.springframework.security.authentication.event.AuthenticationFailureProviderNotFoundEvent;
import org.springframework.security.authentication.event.AuthenticationFailureProxyUntrustedEvent;
import org.springframework.security.authentication.event.AuthenticationFailureServiceExceptionEvent;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.alibaba.fastjson.JSON;
import com.key.win.biz.log.model.OperationLog;
import com.key.win.biz.log.service.OperationLogService;
import com.key.win.system.aop.annotation.LogType;
import com.key.win.utils.DateUtils;
import com.key.win.utils.SpringSecurityUtils;

import cn.hutool.extra.servlet.ServletUtil;

@Component
public class AuthenticationFailureListener
    implements ApplicationListener<AbstractAuthenticationFailureEvent> {

  @Resource private OperationLogService operationLogService;

  @Override
  public void onApplicationEvent(AbstractAuthenticationFailureEvent event) {
    String message;
    if (event instanceof AuthenticationFailureBadCredentialsEvent) {
      // 提供的凭据是错误的，用户名或者密码错误
      message = "提供的凭据是错误的，用户名或者密码错误";
    } else if (event instanceof AuthenticationFailureCredentialsExpiredEvent) {
      // 验证通过，但是密码过期
      message = "验证通过，但是密码过期";
    } else if (event instanceof AuthenticationFailureDisabledEvent) {
      // 验证过了但是账户被禁用
      message = "验证过了但是账户被禁用";
    } else if (event instanceof AuthenticationFailureExpiredEvent) {
      // 验证通过了，但是账号已经过期
      message = "验证通过了，但是账号已经过期";
    } else if (event instanceof AuthenticationFailureLockedEvent) {
      // 账户被锁定
      message = "账户被锁定";
    } else if (event instanceof AuthenticationFailureProviderNotFoundEvent) {
      // 配置错误，没有合适的AuthenticationProvider来处理登录验证
      message = "配置错误";
    } else if (event instanceof AuthenticationFailureProxyUntrustedEvent) {
      // 代理不受信任，用于Oauth、CAS这类三方验证的情形，多属于配置错误
      message = "代理不受信任";
    } else if (event instanceof AuthenticationFailureServiceExceptionEvent) {
      // 其他任何在AuthenticationManager中内部发生的异常都会被封装成此类
      message = "内部发生的异常";
    } else {
      message = "其他未知错误";
    }

    Object username = event.getAuthentication().getPrincipal();
    Object password = event.getAuthentication().getCredentials();
    String clientIP =
        ServletUtil.getClientIP(
            Objects.requireNonNull(
                ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                    .getRequest()),
            "");

    Map<String, Object> body = new HashMap<String, Object>();

    OperationLog operationLog = new OperationLog();
    operationLog.setOperatIp(clientIP);
    operationLog.setContent("LoginFailure"); // 日志记录内容
    operationLog.setOperationObject(
        String.format("username:%s; pass:%s; message:%s", username, password, message));
    operationLog.setUsername(String.valueOf(username));
    operationLog.setCreateTime(DateUtils.dateTimeToStr(new Date()));
    operationLog.setModule("室分系统管理");
    operationLog.setType(LogType.AuthLog.toString());

    body.put("userName", username);
    body.put("clientIP", clientIP);
    body.put("pswd", password);
    operationLog.setParams(JSON.toJSONString(body));

    operationLog.setFlag("成功");
    operationLog.setOperatResult("登录失败");
    operationLogService.save(operationLog);
  }
}
