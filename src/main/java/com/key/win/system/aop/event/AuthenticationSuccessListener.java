package com.key.win.system.aop.event;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.context.ApplicationListener;
import org.springframework.security.authentication.event.AuthenticationSuccessEvent;
import org.springframework.security.core.userdetails.User;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.alibaba.fastjson.JSON;
import com.key.win.biz.log.model.OperationLog;
import com.key.win.biz.log.service.OperationLogService;
import com.key.win.system.aop.annotation.LogType;
import com.key.win.utils.DateUtils;
import com.key.win.utils.SpringSecurityUtils;

import cn.hutool.extra.servlet.ServletUtil;

@Component
public class AuthenticationSuccessListener
    implements ApplicationListener<AuthenticationSuccessEvent> {

  @Resource private OperationLogService operationLogService;

  @Override
  public void onApplicationEvent(AuthenticationSuccessEvent event) {

    User user = (User) event.getAuthentication().getPrincipal();

    String clientIP =
        ServletUtil.getClientIP(
            Objects.requireNonNull(
                ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                    .getRequest()),
            "");

    Map<String, Object> body = new LinkedHashMap<String, Object>();

    OperationLog operationLog = new OperationLog();
    operationLog.setOperatIp(clientIP);
    operationLog.setContent("Login"); // 日志记录内容
    operationLog.setOperationObject(String.format("username:%s; 登录成功", user.getUsername()));
    operationLog.setUsername(user.getUsername());
    operationLog.setCreateTime(DateUtils.dateTimeToStr(new Date()));
    operationLog.setModule("室分系统管理");
    operationLog.setType(LogType.AuthLog.toString());

    body.put("userName", user.getUsername());
    body.put("clientIP", clientIP);
    body.put("pswd", "***");
    operationLog.setParams(JSON.toJSONString(body));

    operationLog.setFlag("成功");
    operationLog.setOperatResult("登录成功");
    operationLogService.save(operationLog);
  }
}
