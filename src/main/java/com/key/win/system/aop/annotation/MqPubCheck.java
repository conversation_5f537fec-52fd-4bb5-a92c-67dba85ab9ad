package com.key.win.system.aop.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface MqPubCheck {

  /**
   * 描述
   *
   * @return
   */
  String description() default "";

  /**
   * 命令名称
   *
   * @return
   */
  String cmdName() default "";

  /**
   * 缓存等待时长
   *
   * @return
   */
  long waitTimeSecend() default 10;

  /**
   * 是否需要计算
   *
   * @return
   */
  boolean needCalculate() default false;
}
