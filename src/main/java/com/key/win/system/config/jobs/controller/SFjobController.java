package com.key.win.system.config.jobs.controller;

import java.util.List;
import java.util.concurrent.ScheduledFuture;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.key.win.common.web.Result;
import com.key.win.mqtt.Scheduling.config.support.TaskMap;
import com.key.win.system.config.jobs.enums.JobStatus;
import com.key.win.system.config.jobs.model.SFjobs;
import com.key.win.system.config.jobs.service.SFjobService;
import com.key.win.system.config.jobs.support.FutureMaps;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/system/config/job/*")
public class SFjobController {

  @Autowired private SFjobService sfjobService;

  @Autowired private ThreadPoolTaskScheduler threadPoolTaskScheduler;

  @PostMapping("/start/{id}")
  public Result<Boolean> start(@PathVariable String id) {
    SFjobs job = sfjobService.getById(id);
    if (job == null) {
      return Result.failed(false, "数据库配置不存在");
    }
    ScheduledFuture<?> future = FutureMaps.getFuturemaps().get(job.getTaskId());
    if (future != null) {
      return Result.failed(false, "任务已启动中");
    }
    future =
        threadPoolTaskScheduler.schedule(
            TaskMap.getRunnable(job.getTaskId()), new CronTrigger(job.getCron()));
    log.info("已添加JOB:{}", job.getTaskId());
    FutureMaps.getFuturemaps().put(job.getTaskId(), future);
    FutureMaps.getTaskIdList().put(job.getTaskId(), job.getTaskId());
    job.setState(JobStatus.START.getStatusCode());
    sfjobService.updateById(job);
    return Result.succeed(true, "任务启动成功");
  }

  @PostMapping("/stop/{id}")
  public Result<Boolean> stop(@PathVariable String id) {
    if (StringUtils.isBlank(id)) {
      return Result.failed(false, "任务ID不存在");
    }
    SFjobs job = sfjobService.getById(id);
    if (job == null) {
      return Result.failed(false, "数据库配置不存在");
    }
    ScheduledFuture<?> future = FutureMaps.getFuturemaps().get(job.getTaskId());
    if (future == null) {
      return Result.failed(false, "当前任务不存在");
    }
    boolean cancel = future.cancel(true);
    log.info("停止JOB:{}", cancel);
    if (cancel) {
      FutureMaps.getFuturemaps().remove(job.getTaskId());
      FutureMaps.getTaskIdList().remove(job.getTaskId());
      job.setState(JobStatus.STOP.getStatusCode());
      sfjobService.updateById(job);
      return Result.succeed(true, "任务停止成功");
    } else {
      return Result.failed(false, "任务停止失败,请重新尝试.");
    }
  }

  @PostMapping("/restart/{id}")
  public Result<Boolean> restart(@PathVariable String id) {
    if (StringUtils.isBlank(id)) {
      return Result.failed(false, "任务ID不存在");
    }

    SFjobs job = sfjobService.getById(id);
    if (job == null) {
      return Result.failed(false, "数据库配置不存在");
    }

    ScheduledFuture<?> future = FutureMaps.getFuturemaps().get(job.getTaskId());
    if (future == null) {
      return Result.failed(false, "目标重启任务不存在");
    }

    boolean cancel = false;
    try {
      cancel = future.cancel(true);
    } catch (Exception e) {
      log.error("轮训任务:{}", e.getMessage());
    }
    if (cancel) {
      log.info("任务取消成功");
      FutureMaps.getFuturemaps().remove(job.getTaskId());
      FutureMaps.getTaskIdList().remove(job.getTaskId());
      log.info("任务重启中...");
      future =
          threadPoolTaskScheduler.schedule(
              TaskMap.getRunnable(job.getTaskId()), new CronTrigger(job.getCron()));
      log.info("任务重启成功");
      FutureMaps.getFuturemaps().put(job.getTaskId(), future);
      FutureMaps.getTaskIdList().put(job.getTaskId(), job.getTaskId());
      job.setState(JobStatus.START.getStatusCode());
      sfjobService.updateById(job);
    }
    return Result.succeed(true, "任务重启成功");
  }

  @PostMapping("/changeTime")
  public Result<Boolean> changeCron(@RequestBody SFjobs jobs) {
    if (StringUtils.isBlank(jobs.getId())) {
      return Result.failed(false, "任务ID不存在");
    }

    if (StringUtils.isNoneBlank(jobs.getCron().trim())) {
      SFjobs dbJob = sfjobService.getById(jobs.getId());
      ScheduledFuture<?> future = FutureMaps.getFuturemaps().get(dbJob.getTaskId());
      if (future != null) {
        boolean cancel = false;
        cancel = future.cancel(true);
        if (cancel) {
          log.info("任务取消成功");
          FutureMaps.getFuturemaps().remove(dbJob.getTaskId());
          FutureMaps.getTaskIdList().remove(dbJob.getTaskId());
          log.info("任务重启中...");
          future =
              threadPoolTaskScheduler.schedule(
                  TaskMap.getRunnable(dbJob.getTaskId()), new CronTrigger(jobs.getCron().trim()));
          log.info("任务重启成功");
          FutureMaps.getFuturemaps().put(dbJob.getTaskId(), future);
          FutureMaps.getTaskIdList().put(dbJob.getTaskId(), dbJob.getTaskId());
          dbJob.setState(JobStatus.START.getStatusCode());
        }
      }
      dbJob.setCron(jobs.getCron().trim());
      boolean flag = sfjobService.updateById(dbJob);
      if (!flag) {
        return Result.failed(false, "更新时间失败");
      }
      return Result.succeed(true, "更新时间成功");
    }
    return Result.failed(false, "修改时间失败");
  }

  /**
   * 所有注册的任务ID列表
   *
   * @return
   */
  @PostMapping("/allTaskList")
  public Result<List<SFjobs>> allTaskList() {
    List<SFjobs> jobs = sfjobService.list();
    return Result.succeed(jobs, "获取任务列表");
  }
}
