package com.key.win.system.config.logoManager.model;

import java.io.Serializable;

import javax.persistence.Entity;
import javax.persistence.Id;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

@Data
@TableName("sf_config_logo")
@Entity(name = "sf_config_logo")
public class LogoInfo implements Serializable {
  /** */
  private static final long serialVersionUID = 1L;

  @Id private String id;

  private String producerName;

  private boolean enabled;

  private String remark;
}
