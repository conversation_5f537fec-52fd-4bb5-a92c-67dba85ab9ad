package com.key.win.system.config.jobs.enums;

public enum JobStatus {
  START(1, "任务启动"),
  STOP(0, "任务停止");

  private int statusCode;
  private String statusDesc;

  public int getStatusCode() {
    return statusCode;
  }

  public void setStatusCode(int statusCode) {
    this.statusCode = statusCode;
  }

  public String getStatusDesc() {
    return statusDesc;
  }

  public void setStatusDesc(String statusDesc) {
    this.statusDesc = statusDesc;
  }

  private JobStatus(int statusCode, String statusDesc) {
    this.statusCode = statusCode;
    this.statusDesc = statusDesc;
  }
}
