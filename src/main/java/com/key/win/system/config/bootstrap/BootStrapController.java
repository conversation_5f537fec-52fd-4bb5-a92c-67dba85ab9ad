package com.key.win.system.config.bootstrap;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.biz.baseInfo.model.BaseLine;
import com.key.win.biz.baseInfo.service.BaseLineService;
import com.key.win.common.result.ResultCode;
import com.key.win.common.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.transaction.Transactional;

@Slf4j
@RestController
@RequestMapping("/sf/boot/version/*")
public class BootStrapController {

    @Resource
    private BootStrapService bootStrapService;
    @Resource
    private BaseLineService baseLineService;

    @PostMapping("/changeLine/{lineName}")
    @Transactional
    public Result<String> changeLineInfo(@PathVariable String lineName) {

        if (StringUtils.isBlank(lineName)) {
            return Result.failed("请在前端页面URLConfig.js中配置页面加载线路");
        }
        String lineId = "";
        if (lineName.equals("ziyang")) {
            lineId = "ziyang";
        } else if (lineName.equals("line8")) {
            lineId = "08";
        } else if (lineName.equals("line10")) {
            lineId = "10";
        } else if (lineName.equals("line13")) {
            lineId = "13";
        } else if (lineName.equals("line17")) {
            lineId = "17";
        } else if (lineName.equals("line18")) {
            lineId = "18";
        } else if (lineName.equals("line19")) {
            lineId = "19";
        } else if (lineName.equals("line27")) {
            lineId = "27";
        } else if (lineName.equals("line30")) {
            lineId = "30";
        }else if (lineName.equals("ningbo8")) {
            lineId = "08";
        }else if(lineName.equals("xmline4")){
            lineId = "xm04";
        }else if(lineName.equals("xmline6")){
            lineId = "xm06";
        }

        LambdaQueryWrapper<BaseLine> lqwLine = new LambdaQueryWrapper<>();
        lqwLine.eq(BaseLine::getLineId, lineId);
        BaseLine lineBean = baseLineService.getOne(lqwLine, false);

        if (lineBean == null) {
            return Result.failed(ResultCode.NO_MATCHING_DATA_FOUND_IN_THE_DATABASE.getCode(), ResultCode.NO_MATCHING_DATA_FOUND_IN_THE_DATABASE.getMessage());
        }

        log.info("启动参数:{},加载线路ID为:{}", lineName, lineId);

        boolean flag = bootStrapService.changeLineData(lineId);
        if (flag) {
            return Result.succeed("加载数据成功,请刷新页面.");
        } else {
            return Result.failed("数据加载失败.!");
        }
    }
}
