package com.key.win.system.config.sf.model;

import javax.persistence.Entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.key.win.common.web.MybatisID;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "SF_GLOBAL_CONFIG")
@TableName("SF_GLOBAL_CONFIG")
public class SfGlobalConfig extends MybatisID {

  private String name;

  private String type;

  private String config;

  private String remark;
}
