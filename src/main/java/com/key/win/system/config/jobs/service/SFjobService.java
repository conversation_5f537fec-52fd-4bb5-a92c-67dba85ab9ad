package com.key.win.system.config.jobs.service;

import java.util.List;
import java.util.concurrent.ScheduledFuture;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.key.win.mqtt.Scheduling.config.support.TaskMap;
import com.key.win.system.config.jobs.dao.SFjobDao;
import com.key.win.system.config.jobs.model.SFjobs;
import com.key.win.system.config.jobs.support.FutureMaps;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class SFjobService extends ServiceImpl<SFjobDao, SFjobs> {

  @Autowired private ThreadPoolTaskScheduler threadPoolTaskScheduler;

  public SFjobs getTaskByTaskId(String taskId) {
    LambdaQueryWrapper<SFjobs> lqw = new LambdaQueryWrapper<SFjobs>();
    lqw.eq(SFjobs::getTaskId, taskId);
    return this.getOne(lqw);
  }

  public void bootstrap() {
    List<SFjobs> jobs = this.list();
    log.info("系统启动:读取定时调度配置任务");
    jobs.forEach(
        x -> {
          if (x.getState() == 1) {
            ScheduledFuture<?> future = FutureMaps.getFuturemaps().get(x.getTaskId());
            if (future == null) {
              future =
                  threadPoolTaskScheduler.schedule(
                      TaskMap.getRunnable(x.getTaskId()), new CronTrigger(x.getCron()));
            }
            FutureMaps.getFuturemaps().put(x.getTaskId(), future);
            FutureMaps.getTaskIdList().put(x.getTaskId(), x.getTaskId());
            log.info("定时调度任务:{}启动成功", x.getTaskName());
          }
        });
    log.info("系统启动:读取完毕!");
  }
}
