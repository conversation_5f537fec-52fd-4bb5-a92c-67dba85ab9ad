package com.key.win.system.config.jobs.model;

import java.io.Serializable;

import javax.persistence.Entity;
import javax.persistence.Id;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

@TableName("sf_system_job")
@Entity(name = "sf_system_job")
@Data
public class SFjobs implements Serializable {
  /** */
  private static final long serialVersionUID = 1L;

  @Id @TableId private String id;

  private String cron;

  private String taskName;

  private String taskId;

  private int state;
}
