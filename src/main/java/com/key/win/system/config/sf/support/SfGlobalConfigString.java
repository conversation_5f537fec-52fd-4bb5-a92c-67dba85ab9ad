package com.key.win.system.config.sf.support;

public class SfGlobalConfigString {

  /** 设备离线告警时长Key */
  public static final String ConfigMapKey_deviceOffLineTime = "deviceOfflineTime";

  /** mq地址配置 */
  public static final String ConfigMapKey_MQ_ADDR_IP = "ip";

  public static final String ConfigMapKey_MQ_ADDR_PORT = "port";
  public static final String ConfigMapKey_MQ_USERNAME = "username";
  public static final String ConfigMapKey_MQ_PASSWORD = "password";

  /** snmp地址配置 */
  public static final String ConfigMapKey_SNMP_ADDR_IP = "ip";

  public static final String ConfigMapKey_SNMP_ADDR_PORT = "port";
  public static final String ConfigMapKey_SNMP_ADDR_COMMUNITY = "community";
  public static final String ConfigMapKey_SNMP_ADDR_VERSION = "version";
  public static final String ConfigMapKey_SNMP_ADDR_TIMEOUT = "timeOut";
  public static final String ConfigMapKey_SNMP_ADDR_RETRIES = "retries";
  public static final String ConfigMapKey_SNMP_ADDR_ENABLED = "enabled";

  /** JOB启用规则配置 */
  public static final String ConfigMapKey_JOB_ID = "jobId";

  public static final String ConfigMapKey_JOB_ENABLED = "enabled";
  public static final String ConfigMapKey_JOB_CRON = "cron";

  /** 信源强度偏离值KEY */
  public static final String ConfigMapKey_ANT_deviationValue = "deviationValue";

  /** 是否启用 */
  public static final String ConfigMapKey_enabled = "enabled";
}
