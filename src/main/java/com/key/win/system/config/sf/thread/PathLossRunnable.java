package com.key.win.system.config.sf.thread;

import com.alibaba.fastjson.JSON;
import com.key.win.biz.baseInfo.model.Probe;
import com.key.win.biz.baseInfo.model.BelongUnit;
import com.key.win.biz.baseInfo.service.BelongUnitService;
import com.key.win.config.RedisUtil;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.mqtt.Scheduling.config.support.TaskMap;
import com.key.win.mqtt.mq.cmd.QueryCommandService;
import com.key.win.mqtt.mq.cmd.EmqRequestService;
import com.key.win.system.config.jobs.enums.JobStatus;
import com.key.win.system.config.jobs.model.SFjobs;
import com.key.win.system.config.jobs.service.SFjobService;
import com.key.win.system.config.jobs.support.FutureMaps;
import com.key.win.utils.SpringUtils;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ScheduledFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronTrigger;

@Slf4j
public class PathLossRunnable implements Runnable {

  @Override
  public void run() {

    RedisUtil redisUtil = SpringUtils.getBean(RedisUtil.class);
    RedisTemplate<String, Object> redisTemplate = redisUtil.getRedisTemplate();

    SFjobService sfjobService = SpringUtils.getBean(SFjobService.class);
    EmqRequestService emqRequestService = SpringUtils.getBean(EmqRequestService.class);
    BelongUnitService belongUnitService = SpringUtils.getBean(BelongUnitService.class);
    QueryCommandService queryCommandService = SpringUtils.getBean(QueryCommandService.class);
    ThreadPoolTaskScheduler threadPoolTaskScheduler =
        SpringUtils.getBean(ThreadPoolTaskScheduler.class);

    log.info("1.开始执行[信号强度采集]功能>>>>");
    // 停止所有已经开启的定时任务
    Map<String, ScheduledFuture<?>> futureMap = FutureMaps.getFuturemaps();
    log.info("2.即将关闭所有的已启动的定时任务,当前存在的定时任务个数:{}>>>>", futureMap.size());
    futureMap
        .keySet()
        .forEach(
            x -> {
              log.info("2-[{}].开始关闭{}任务>>>>", x, x);
              SFjobs job = sfjobService.getTaskByTaskId(x);
              ScheduledFuture<?> scheduledFuture = futureMap.get(x);
              if (scheduledFuture != null) {
                scheduledFuture.cancel(true);
                log.info("2-[{}].已经关闭{}任务>>>>", x, x);
              }
              job.setState(JobStatus.STOP.getStatusCode());
              log.info("3.数据库状态已更新>>>>", x, x);
              sfjobService.updateById(job);
            });
    futureMap.clear();
    log.info("4.所有定时任务关闭完毕>>>>");

    log.info("5.开始执行[信源收集]>>>>");
    // 开始执行3次路损收集任务
    List<BelongUnit> belongUnitList = belongUnitService.list();
    if (belongUnitList.size() > 0) {
      FutureMaps.addPathLossExecuteTimes1();
    }
    for (int i = 0; i < 1; i++) {
      long waitTime = 0;
      log.info("5.第{}次开始>>>>", (i + 1));
      for (BelongUnit belongUnit : belongUnitList) {
        String hostNumber = belongUnit.getHostNum();
        boolean isOnline = emqRequestService.clientIsOnline("reader_" + hostNumber);
        if (isOnline) {
          queryCommandService.accurateReadingByDeviceId(hostNumber);

          int startPower = Integer.parseInt(belongUnit.getStartPower());
          int endPower = Integer.parseInt(belongUnit.getEndPower());
          waitTime += (endPower - startPower + 1) * GConfig.getDeviceReadingWaitTimeSecond();
        } else {
          log.info("5.当前监控主机{}不在线，跳过!", hostNumber);
        }
      }
      try {
        log.info("5.信源强度采集中,等待耗时约{}秒...", waitTime);
        Thread.sleep(waitTime * 1000);
      } catch (InterruptedException e) {
        throw new RuntimeException(e);
      }
      log.info("5.第{}次执行完毕,等待{}s>>>>", (i + 1), waitTime);
    }

    log.info("6.收集任务执行完毕,开始恢复定时任务>>>>");
    for (String taskId : FutureMaps.getTaskIdList().keySet()) {
      log.info("7-[{}].开始恢复[{}]任务>>>>", taskId, taskId);
      SFjobs job = sfjobService.getTaskByTaskId(taskId);
      ScheduledFuture<?> future =
          threadPoolTaskScheduler.schedule(
              TaskMap.getRunnable(taskId), new CronTrigger(job.getCron()));
      FutureMaps.getFuturemaps().put(job.getTaskId(), future);
      log.info("7-[{}].恢复[{}]完毕>>>>", taskId, taskId);

      job.setState(JobStatus.START.getStatusCode());
      sfjobService.updateById(job);
      log.info("8.数据库状态已更新[END]");
    }
    Set<String> keys = redisTemplate.keys(GConfig.probeKey + "*");
    for (String key : keys) {
      String[] keySplitArray = key.split(":");

      String hostNumber = keySplitArray[3];
      String sid = keySplitArray[4];

      Map<String, Object> probeInfoMap =
          redisUtil.hmget2(GConfig.probeKey + hostNumber + ":" + sid);
      String probeJsonString = JSON.toJSONString(probeInfoMap);
      Probe ant = JSON.parseObject(probeJsonString, Probe.class);

      String probePathLossKey = GConfig.probeKeyPathLoss + hostNumber + ":" + sid;
      int lost = ant.getLost();
      redisTemplate.opsForZSet().add(probePathLossKey, FutureMaps.getPathLossExecuteTimes(), lost);
    }
  }
}
