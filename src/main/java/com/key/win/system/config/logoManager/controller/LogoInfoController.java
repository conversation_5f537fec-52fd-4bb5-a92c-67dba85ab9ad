package com.key.win.system.config.logoManager.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.common.web.Result;
import com.key.win.system.config.logoManager.model.LogoInfo;
import com.key.win.system.config.logoManager.service.LogoInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/sf/config/logo/*")
public class LogoInfoController {

  @Autowired private LogoInfoService logoService;

  @PostMapping("/upload")
  public Object uploadLogo(@RequestBody LogoInfo logoInfo) {

    boolean isBlank = StringUtils.isBlank(logoInfo.getProducerName());
    if (isBlank) {
      return Result.failed(false, "供应商名称不存在");
    }
    boolean flag = logoService.save(logoInfo);
    if (flag) {
      return Result.succeed(flag, "更新成功");
    } else {
      return Result.failed(flag, "更新失败");
    }
  }

  @PostMapping("/enable/{id}")
  public Object enableLogo(@PathVariable String id) {

    LogoInfo dbLogoInfo = logoService.getById(id);
    if (null == dbLogoInfo) {
      return Result.failed(false, "Logo信息不存在");
    }
    dbLogoInfo.setEnabled(true);
    boolean flag = logoService.updateById(dbLogoInfo);
    if (flag) {
      return Result.succeed(flag, "启用成功");
    } else {
      return Result.failed(flag, "启用失败");
    }
  }

  @PostMapping("/getLogo")
  public Object getLogo() {
    LambdaQueryWrapper<LogoInfo> lqwLogo = new LambdaQueryWrapper<LogoInfo>();
    lqwLogo.eq(LogoInfo::isEnabled, true);
    LogoInfo logoInfo = logoService.getOne(lqwLogo);
    return logoInfo;
  }
}
