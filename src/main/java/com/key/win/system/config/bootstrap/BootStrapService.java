package com.key.win.system.config.bootstrap;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.key.win.biz.baseInfo.model.BaseLine;
import com.key.win.biz.baseInfo.service.BaseLineService;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class BootStrapService {

  @Resource private BaseLineService baseLineService;

  @Transactional
  public boolean changeLineData(String lineId) {
    LambdaQueryWrapper<BaseLine> lqwBaseLine = new LambdaQueryWrapper<>();
    lqwBaseLine.eq(BaseLine::getLineId, lineId);
    BaseLine lineBean = baseLineService.getOne(lqwBaseLine);

    if (lineBean != null) {
      try {
        UpdateWrapper<BaseLine> lqw = new UpdateWrapper<>();
        lqw.set("line_operation_state", "0");
        baseLineService.update(lqw);

        UpdateWrapper<BaseLine> lqw2 = new UpdateWrapper<>();
        lqw2.set("line_operation_state", "1");
        lqw2.eq("line_id", lineBean.getLineId());
        baseLineService.update(lqw2);
        log.info("数据已经更新,请重新刷新页面!");
        return true;
      } catch (Exception e) {
        log.error("数据更新失败!!");
        throw new RuntimeException(e);
      }
    }
    return false;
  }
}
