package com.key.win.system.config.jobs.support;

import com.key.win.system.config.sf.thread.PathLossRunnable;
import java.lang.Thread.State;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ScheduledFuture;

public class FutureMaps {

  private static final Map<String, ScheduledFuture<?>> futureMaps =
      new HashMap<String, ScheduledFuture<?>>();
  public static Map<String, String> taskIdMap = new HashMap<>();
  public static int pathLossExecuteTimes = 0;
  private static Thread pathLossThread = new Thread(new PathLossRunnable());

  public static Thread getPathLossThread() {
    if (pathLossThread == null || pathLossThread.getState() == State.TERMINATED) {
      pathLossThread = new Thread(new PathLossRunnable());
    }
    return pathLossThread;
  }

  public static Map<String, ScheduledFuture<?>> getFuturemaps() {
    return futureMaps;
  }

  public static Map<String, String> getTaskIdList() {
    return taskIdMap;
  }

  public static int getPathLossExecuteTimes() {
    return pathLossExecuteTimes;
  }

  public static void addPathLossExecuteTimes1() {

    if (pathLossExecuteTimes >= 3) {
      pathLossExecuteTimes = 0;
    }
    FutureMaps.pathLossExecuteTimes += 1;
  }

  public static void resetPathLossExecuteTime() {
    pathLossExecuteTimes = 0;
  }
}
