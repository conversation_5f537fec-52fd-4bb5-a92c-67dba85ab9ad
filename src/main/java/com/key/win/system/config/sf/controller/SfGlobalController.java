package com.key.win.system.config.sf.controller;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.common.web.Result;
import com.key.win.mqtt.Scheduling.config.support.GConfig;
import com.key.win.system.config.jobs.support.FutureMaps;
import com.key.win.system.config.sf.model.SfGlobalConfig;
import com.key.win.system.config.sf.service.ISfGlobalConfigService;
import com.key.win.system.config.sf.support.SfGlobalConfigString;
import com.key.win.system.config.sf.support.SfGlobalConfigType;
import java.lang.Thread.State;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/sf/config/global/*")
public class SfGlobalController {

  @Autowired private ISfGlobalConfigService sfGlobalConfigService;

  @PostMapping("/load")
  public Result<List<SfGlobalConfig>> getConfigs() {
    List<SfGlobalConfig> configList = sfGlobalConfigService.list();
    return Result.succeed(configList, "配置列表获取完毕");
  }

  /**
   * 设备告警离线时间设置
   *
   * @param config
   */
  @PostMapping("/set/offlineTime")
  public Result<SfGlobalConfig> setDeviceOfflineTime(@RequestBody Map<String, Object> config) {

    if (config == null) {
      config = new ConcurrentHashMap<>();
    }

    Long time =
        MapUtil.getLong(
            config, SfGlobalConfigString.ConfigMapKey_deviceOffLineTime, GConfig.getOfflineMin());
    Boolean enabled = MapUtil.getBool(config, SfGlobalConfigString.ConfigMapKey_enabled, false);

    config = new LinkedHashMap<String, Object>();
    config.put(SfGlobalConfigString.ConfigMapKey_deviceOffLineTime, time);
    config.put(SfGlobalConfigString.ConfigMapKey_enabled, enabled);

    String configString = JSON.toJSONString(config);

    LambdaQueryWrapper<SfGlobalConfig> lqw = new LambdaQueryWrapper<SfGlobalConfig>();
    lqw.eq(
        SfGlobalConfig::getType, SfGlobalConfigType.CONFIG_DEVICE_OFFLINE_TIME_SETTING.toString());
    SfGlobalConfig cfgBean = sfGlobalConfigService.getOne(lqw);

    if (cfgBean == null) {
      SfGlobalConfig bean =
          SfGlobalConfig.builder()
              .remark("设备离线时长配置")
              .name("设备离线监测时长")
              .config(configString)
              .type(SfGlobalConfigType.CONFIG_DEVICE_OFFLINE_TIME_SETTING.toString())
              .build();
      sfGlobalConfigService.save(bean);
    } else {
      cfgBean.setConfig(configString);
      sfGlobalConfigService.updateById(cfgBean);
    }
    return Result.succeed(cfgBean, "保存成功");
  }

  /**
   * 信源强度偏离值设置
   *
   * @param config
   */
  @PostMapping("/set/deviationValue")
  public Result<SfGlobalConfig> setDeviationValue(@RequestBody Map<String, Object> config) {

    if (config == null) {
      config = new LinkedHashMap<>();
    }

    Long deviationValue =
        MapUtil.getLong(
            config,
            SfGlobalConfigString.ConfigMapKey_ANT_deviationValue,
            GConfig.getAntDeviationValue());
    Boolean enabled = MapUtil.getBool(config, SfGlobalConfigString.ConfigMapKey_enabled, false);

    config = new LinkedHashMap<String, Object>();
    config.put(SfGlobalConfigString.ConfigMapKey_ANT_deviationValue, deviationValue);
    config.put(SfGlobalConfigString.ConfigMapKey_enabled, enabled);

    String configString = JSON.toJSONString(config);

    LambdaQueryWrapper<SfGlobalConfig> lqw = new LambdaQueryWrapper<SfGlobalConfig>();
    lqw.eq(
        SfGlobalConfig::getType, SfGlobalConfigType.CONFIG_ANT_DEVIATION_VALUE_SETTING.toString());
    SfGlobalConfig cfgBean = sfGlobalConfigService.getOne(lqw);

    if (cfgBean == null) {
      SfGlobalConfig bean =
          SfGlobalConfig.builder()
              .remark("信源偏离值配置")
              .name("信源偏离值配置")
              .config(configString)
              .type(SfGlobalConfigType.CONFIG_ANT_DEVIATION_VALUE_SETTING.toString())
              .build();
      sfGlobalConfigService.save(bean);
    } else {
      cfgBean.setConfig(configString);
      sfGlobalConfigService.updateById(cfgBean);
    }
    return Result.succeed(cfgBean, "保存成功");
  }

  /**
   * 设备Mq的连接地址
   *
   * @param config
   */
  @PostMapping("/set/mqAddr")
  public Result<SfGlobalConfig> setMqAddress(@RequestBody Map<String, Object> config) {

    String ip = MapUtil.getStr(config, SfGlobalConfigString.ConfigMapKey_MQ_ADDR_IP, "127.0.0.1");
    String port = MapUtil.getStr(config, SfGlobalConfigString.ConfigMapKey_MQ_ADDR_PORT, "1883");
    Boolean enabled = MapUtil.getBool(config, SfGlobalConfigString.ConfigMapKey_enabled, false);
    String username =
        MapUtil.getStr(config, SfGlobalConfigString.ConfigMapKey_MQ_USERNAME, "indoorSystem");
    String password =
        MapUtil.getStr(config, SfGlobalConfigString.ConfigMapKey_MQ_PASSWORD, "indoorSystem");

    config = new LinkedHashMap<String, Object>();
    config.put(SfGlobalConfigString.ConfigMapKey_MQ_ADDR_IP, ip);
    config.put(SfGlobalConfigString.ConfigMapKey_MQ_ADDR_PORT, port);
    config.put(SfGlobalConfigString.ConfigMapKey_enabled, enabled);
    config.put(SfGlobalConfigString.ConfigMapKey_MQ_USERNAME, username);
    config.put(SfGlobalConfigString.ConfigMapKey_MQ_PASSWORD, password);

    String configString = JSON.toJSONString(config);

    LambdaQueryWrapper<SfGlobalConfig> lqw = new LambdaQueryWrapper<SfGlobalConfig>();
    lqw.eq(SfGlobalConfig::getType, SfGlobalConfigType.CONFIG_MQ_ADDR_SETTING.toString());
    SfGlobalConfig cfgBean = sfGlobalConfigService.getOne(lqw);

    if (cfgBean == null) {
      SfGlobalConfig bean =
          SfGlobalConfig.builder()
              .remark("MQ连接地址配置")
              .name("MQ地址配置")
              .config(configString)
              .type(SfGlobalConfigType.CONFIG_MQ_ADDR_SETTING.toString())
              .build();
      sfGlobalConfigService.save(bean);
    } else {
      cfgBean.setConfig(configString);
      sfGlobalConfigService.updateById(cfgBean);
    }
    return Result.succeed(cfgBean, "保存成功");
  }

  /**
   * 设备SNMP的连接地址
   *
   * @param configList
   */
  @PostMapping("/set/snmpAddr")
  public Result<SfGlobalConfig> setSnmpAddress(@RequestBody List<Map<String, Object>> configList) {

    List<Map> configStringList = new ArrayList<Map>();

    for (Map config : configList) {
      String ip =
          MapUtil.getStr(config, SfGlobalConfigString.ConfigMapKey_SNMP_ADDR_IP, "127.0.0.1");
      String port =
          MapUtil.getStr(config, SfGlobalConfigString.ConfigMapKey_SNMP_ADDR_PORT, "1883");
      Boolean enabled = MapUtil.getBool(config, SfGlobalConfigString.ConfigMapKey_enabled, false);
      String community =
          MapUtil.getStr(config, SfGlobalConfigString.ConfigMapKey_SNMP_ADDR_COMMUNITY, "public");
      int version = MapUtil.getInt(config, SfGlobalConfigString.ConfigMapKey_SNMP_ADDR_VERSION, 2);
      int retries = MapUtil.getInt(config, SfGlobalConfigString.ConfigMapKey_SNMP_ADDR_RETRIES, 2);
      long timeout =
          MapUtil.getLong(config, SfGlobalConfigString.ConfigMapKey_SNMP_ADDR_TIMEOUT, 5L);

      Map<String, Object> configMap = new LinkedHashMap<String, Object>();

      configMap.put("index", UUID.fastUUID().toString());
      configMap.put(SfGlobalConfigString.ConfigMapKey_SNMP_ADDR_IP, ip);
      configMap.put(SfGlobalConfigString.ConfigMapKey_SNMP_ADDR_PORT, port);
      configMap.put(SfGlobalConfigString.ConfigMapKey_SNMP_ADDR_COMMUNITY, community);
      configMap.put(SfGlobalConfigString.ConfigMapKey_SNMP_ADDR_VERSION, version);
      configMap.put(SfGlobalConfigString.ConfigMapKey_SNMP_ADDR_TIMEOUT, timeout);
      configMap.put(SfGlobalConfigString.ConfigMapKey_SNMP_ADDR_RETRIES, retries);
      configMap.put(SfGlobalConfigString.ConfigMapKey_SNMP_ADDR_ENABLED, enabled);
      configStringList.add(configMap);
    }

    String jsonString = JSON.toJSONString(configStringList);

    LambdaQueryWrapper<SfGlobalConfig> lqw = new LambdaQueryWrapper<SfGlobalConfig>();
    lqw.eq(SfGlobalConfig::getType, SfGlobalConfigType.CONFIG_SNMP_ADDR.toString());
    SfGlobalConfig cfgBean = sfGlobalConfigService.getOne(lqw);

    if (cfgBean == null) {
      SfGlobalConfig bean =
          SfGlobalConfig.builder()
              .remark("SNMP连接地址配置")
              .name("SNMP地址配置")
              .config(jsonString)
              .type(SfGlobalConfigType.CONFIG_SNMP_ADDR.toString())
              .build();
      sfGlobalConfigService.save(bean);
    } else {
      cfgBean.setConfig(jsonString);
      sfGlobalConfigService.updateById(cfgBean);
    }
    return Result.succeed(cfgBean, "保存成功");
  }

  /** 信号强度采集功能 */
  @PostMapping("/acquisition/signalStrength")
  public Result<Map> signalStrengthAcquisition() throws InterruptedException {
    Map<String, Object> resultMap = new LinkedHashMap<>();
    log.info("当前任务执行次数:{}", FutureMaps.getPathLossExecuteTimes());
    if (FutureMaps.getPathLossExecuteTimes() >= 3) {
      resultMap.put("executeTimes", FutureMaps.getPathLossExecuteTimes() + 1);
      resultMap.put("msg", "当前路损收集次数" + FutureMaps.getPathLossExecuteTimes() + "次,请重置计数器.");
      return Result.failedWith(resultMap, -2, "返回结果");
    }
    State state = FutureMaps.getPathLossThread().getState();
    log.info("当前线程任务状态:{}", state);

    log.info("是否有任务执行中?:{}", FutureMaps.getPathLossThread().isAlive());
    if (FutureMaps.getPathLossThread().isAlive()) {
      resultMap.put("threadStatus", FutureMaps.getPathLossThread().isAlive());
      resultMap.put("msg", "任务正在执行中,请稍后重试");
      return Result.failedWith(resultMap, -1, "返回结果");
    }

    resultMap.put("threadStatus", "running");
    resultMap.put(
        "msg", "耗时较长,已移交到后台程序执行.当前采集第" + (FutureMaps.getPathLossExecuteTimes() + 1) + "次");
    FutureMaps.getPathLossThread().start();
    return Result.succeed(resultMap, "返回结果");
  }

  /**
   * 重置计数器
   *
   * @return 重置计数器结果
   */
  @PostMapping("/acquisition/reset")
  public Result<Map> signalReset() {
    FutureMaps.resetPathLossExecuteTime();
    return Result.succeed("重置成功");
  }
}
