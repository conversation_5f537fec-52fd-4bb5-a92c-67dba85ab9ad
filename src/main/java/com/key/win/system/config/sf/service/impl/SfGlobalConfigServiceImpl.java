package com.key.win.system.config.sf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.key.win.system.config.sf.dao.SfGlobalConfigDao;
import com.key.win.system.config.sf.model.SfGlobalConfig;
import com.key.win.system.config.sf.service.ISfGlobalConfigService;
import com.key.win.system.config.sf.support.SfGlobalConfigType;
import org.springframework.stereotype.Service;

@Service
public class SfGlobalConfigServiceImpl extends ServiceImpl<SfGlobalConfigDao, SfGlobalConfig>
    implements ISfGlobalConfigService {

  @Override
  public SfGlobalConfig getConfigByType(String configType) {
    LambdaQueryWrapper<SfGlobalConfig> lqw = new LambdaQueryWrapper<SfGlobalConfig>();
    lqw.eq(SfGlobalConfig::getType, configType);
    SfGlobalConfig cfgBean = this.getOne(lqw);
    return cfgBean;
  }
}
