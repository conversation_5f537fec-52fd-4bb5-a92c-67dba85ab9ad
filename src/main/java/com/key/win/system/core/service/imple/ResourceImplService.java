package com.key.win.system.core.service.imple;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import javax.transaction.Transactional;

import com.key.win.common.utils.SuperAccountUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.security.core.userdetails.User;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.key.win.common.web.MybatiesPageServiceTemplate;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import com.key.win.system.core.dao.SysRequestPathMapper;
import com.key.win.system.core.dao.SysRequestPathPermissionRelationMapper;
import com.key.win.system.core.dao.SysRoleRequestPathRelationMapper;
import com.key.win.system.core.dao.SysUserMapper;
import com.key.win.system.core.dto.ResourceTreeDTO;
import com.key.win.system.core.dto.SysRequestPathDTO;
import com.key.win.system.core.model.SysRequestPath;
import com.key.win.system.core.model.SysRequestPathPermissionRelation;
import com.key.win.system.core.model.SysRoleRequestPathRelation;
import com.key.win.system.core.model.SysUser;
import com.key.win.system.core.service.IResourceService;
import com.key.win.utils.BeanConvertUtils;
import com.key.win.utils.SpringBeanUtilsExt;
import com.key.win.utils.SpringSecurityUtils;

@Service("sysResourceService")
@Transactional
public class ResourceImplService extends ServiceImpl<SysRequestPathMapper, SysRequestPath>
    implements IResourceService {
  @Autowired private SysRequestPathMapper requestPathMapper;
  @Autowired private SysRequestPathPermissionRelationMapper requestPathPermissionRelationMapper;

  @Autowired private SysRoleRequestPathRelationMapper roleRequestPathRelationMapper;

  @Autowired private SysUserMapper userMapper;

  /** 新增资源 */
  @Override
  public Result<Boolean> saveRequestPath(SysRequestPathDTO requestPathDTO) {
    LambdaQueryWrapper<SysRequestPath> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(SysRequestPath::getUrl, requestPathDTO.getUrl());
    queryWrapper.eq(SysRequestPath::getEnable, 1);
    queryWrapper.eq(SysRequestPath::getType, requestPathDTO.getType());
    SysRequestPath sysRequestPath = requestPathMapper.selectOne(queryWrapper);
    // 如果这条资源不存在则新增
    if (sysRequestPath == null) {
      sysRequestPath = BeanConvertUtils.convertTo(requestPathDTO, SysRequestPath::new);
      // 设置基本字段信息
      sysRequestPath.setCreateTime(new Date());
      sysRequestPath.setUpdateTime(new Date());
      // 获取当前登录用户
      SysUser currentUser = getCurrentUser();
      if (currentUser != null) {
        // 当前登录用户的id
        sysRequestPath.setCreateUser(currentUser.getId());
        sysRequestPath.setUpdateUser(currentUser.getId());
      }
      requestPathMapper.insert(sysRequestPath);
      return Result.succeed("新增资源成功");
    }
    return Result.failed("该资源已存在");
  }

  /** 分页查询资源信息 */
  @Override
  public PageResult<SysRequestPath> queryResourcesByPage(PageRequest<SysRequestPath> pageRequest) {
    MybatiesPageServiceTemplate<SysRequestPath, SysRequestPath> page =
        new MybatiesPageServiceTemplate<SysRequestPath, SysRequestPath>(this.baseMapper) {
          @Override
          protected Wrapper<SysRequestPath> constructWrapper(SysRequestPath requestPath) {
            LambdaQueryWrapper<SysRequestPath> wrapper = new LambdaQueryWrapper<>();
            if (requestPath == null) {
              return wrapper;
            }
            wrapper.like(
                SysRequestPath::getRequestPathName, pageRequest.getT().getRequestPathName());
            return wrapper;
          }
        };
    PageResult<SysRequestPath> pageResult = page.doPagingQuery(pageRequest);
    return pageResult;
  }

  /** 修改资源信息 */
  @Override
  public void updateRequestPath(SysRequestPathDTO requestPathDTO) {
    SysRequestPath sysRequestPath = requestPathMapper.selectById(requestPathDTO.getId());
    // 权限信息存在才能修改
    if (sysRequestPath != null) {
      sysRequestPath.setUpdateTime(new Date());
      // 修改人为当前登录用户
      SysUser currentUser = getCurrentUser();
      if (currentUser != null) {
        sysRequestPath.setUpdateUser(currentUser.getId());
      }
      SpringBeanUtilsExt.copyProperties(requestPathDTO, sysRequestPath);
      requestPathMapper.updateById(sysRequestPath);
    }
  }

  /**
   * 根据当前登录用户账号获取当前登录用户
   *
   * @return 当前登录用户
   */
  private SysUser getCurrentUser() {
    String currentUserName = SpringSecurityUtils.getUserName();

    LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(SysUser::getAccount, currentUserName);
    SysUser currentUser = userMapper.selectOne(queryWrapper);
    return currentUser;
  }

  /** 根据资源id删除资源 解除资源角色关系 解除权限资源关系 */
  @Override
  public void deleteRequestPathById(String id) {
    // 根据资源id删除资源
    requestPathMapper.deleteById(id);
    // 解除权限资源关系
    LambdaQueryWrapper<SysRequestPathPermissionRelation> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(SysRequestPathPermissionRelation::getUrlId, id);
    requestPathPermissionRelationMapper.delete(wrapper);
    // 解除角色资源关系
    LambdaQueryWrapper<SysRoleRequestPathRelation> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(SysRoleRequestPathRelation::getRequestPathId, id);
    roleRequestPathRelationMapper.delete(queryWrapper);
  }

  /** 递归查询资源列表 */
  @Override
  public List<ResourceTreeDTO> queryResourcesTree() {
    return getResourceTreeDTOS("-1");
  }

  /** 页面展示资源tree */
  @Override
  public List<ResourceTreeDTO> showResourcesTree() {
    // rootId  主目录id  o 从一级目录查询
    return getResourceTreeDTOS("0");
  }

  /**
   * 根据主目录id查询资源tree
   *
   * @param rootId 主目录id
   */
  private List<ResourceTreeDTO> getResourceTreeDTOS(String rootId) {
    // 查询顶级资源路径
    LambdaQueryWrapper<SysRequestPath> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(SysRequestPath::getParentId, rootId);
    wrapper.orderByAsc(SysRequestPath::getSort);
    //2024-07-04添加功能
    if(!SuperAccountUtils.isSuperAccount()){
      wrapper.ne(SysRequestPath::getId,SuperAccountUtils.TEST_MENU_ID); //过滤顶级【功能测试】菜单（菜单ID：1539129186847752193）
    }
    List<SysRequestPath> requestPathList = requestPathMapper.selectList(wrapper);
    if (CollectionUtils.isNotEmpty(requestPathList)) {
      List<ResourceTreeDTO> resourceTreeDTOList =
          BeanConvertUtils.convertListTo(requestPathList, ResourceTreeDTO::new);
      // 递归查询资源列表
      queryChildResourcesList(resourceTreeDTOList);
      return resourceTreeDTOList;
    }
    return null;
  }

  /** 迭代查询根目录下资源列表 */
  private void queryChildResourcesList(List<ResourceTreeDTO> resourceTreeDTOList) {
    for (ResourceTreeDTO resourceTreeDTO : resourceTreeDTOList) {
      // 根据资源id查询下属资源
      LambdaQueryWrapper<SysRequestPath> queryWrapper = new LambdaQueryWrapper<>();
      queryWrapper.eq(SysRequestPath::getParentId, resourceTreeDTO.getId());
      queryWrapper.orderByAsc(SysRequestPath::getSort);
      List<SysRequestPath> requestPaths = requestPathMapper.selectList(queryWrapper);
      if (CollectionUtils.isNotEmpty(requestPaths)) {
        List<ResourceTreeDTO> resourceTreeDTOS =
            BeanConvertUtils.convertListTo(requestPaths, ResourceTreeDTO::new);
        // 迭代查询目录下资源
        queryChildResourcesList(resourceTreeDTOS);
        resourceTreeDTO.setChildrenUrl(resourceTreeDTOS);
      }
    }
  }
}
