package com.key.win.system.core.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 角色-权限关联关系表
 *
 * <AUTHOR>
 * @since 2022-03-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("sys_role_permission_relation")
public class SysRolePermissionRelation implements Serializable {

  private static final long serialVersionUID = 1L;

  /** 主键id */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private String id;

  /** 角色id */
  private String roleId;

  /** 权限id */
  private String permissionId;
}
