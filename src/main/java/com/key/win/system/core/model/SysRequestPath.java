package com.key.win.system.core.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.key.win.system.core.dto.ResourceTreeDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 请求路径
 *
 * <AUTHOR>
 * @since 2022-03-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("sys_request_path")
public class SysRequestPath implements Serializable {

  private static final long serialVersionUID = 1L;

  /** 主键id */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private String id;

  /** 父目录id 0 主目录 */
  private String parentId;

  /** 上级目录名称 */
  private String parentName;

  /** 请求路径 */
  private String url;

  /** 请求路径名称 */
  private String requestPathName;

  /** 路径描述 */
  private String description;

  /** 排序 */
  private String sort;

  /** 路径类型 1 一级目录, 2 菜单，3 按钮 */
  private int type;

  /** 是否启用 0 启用 1禁用 */
  private Boolean enable;

  /** 创建时间 */
  private Date createTime;

  /** 修改时间 */
  private Date updateTime;

  /** 创建人 */
  private String createUser;

  /** 修改人 */
  private String updateUser;

  /** 目录图标 */
  private String iconClassName;

  /** ElementUI tree展示的名称 */
  @TableField(exist = false)
  private String label;

  /** 子路径列表 */
  @TableField(exist = false)
  private List<ResourceTreeDTO> childrenUrl;

  /** tree组件 节点名称取值 从 路径名称获取 */
  public String getLabel() {
    return requestPathName;
  }
}
