package com.key.win.system.core.vo;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class RequestPathVO implements Serializable {
  private static final long serialVersionUID = 1L;

  private String id;

  /** 请求路径名称 */
  private String requestPathName;

  /** 上级目录名称 */
  private String parentName;

  /** 排序 */
  private String sort;

  /** 请求路径 */
  private String url;

  /** 父目录id 0 主目录 */
  private String parentId;

  /** 路径描述 */
  private String description;

  /** 路径类型 1 一级目录, 2 菜单，3 按钮 */
  private int type;

  /** 是否启用 1 启用 */
  private Boolean enable;

  /** 目录图标 */
  private String iconClassName;

  /** 角色名 */
  private String roleName;

  /** 角色说明 */
  private String roleDescription;

  /** 角色值 */
  private String roleCode;
}
