package com.key.win.system.core.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/30 14:19
 * @description 用户信息 用于返回给前段的实体类
 */
@Data
public class UserInfo implements Serializable {

  private static final long serialVersionUID = 1L;

  private String id;

  /** 用户的昵称 */
  private String userName;

  /** 用户账号 */
  private String account;

  /** 上一次登录时间 */
  private Date lastLoginTime;

  /** 账号是否可用。默认为1（可用） */
  private Boolean enabled;

  /** 是否过期。默认为1（没有过期） */
  private Boolean notExpired;

  /** 账号是否锁定。默认为1（没有锁定） */
  private Boolean accountNotLocked;

  /** 证书（密码）是否过期。默认为1（没有过期） */
  private Boolean credentialsNotExpired;

  /** 创建时间 */
  private Date createTime;

  /** 修改时间 */
  private Date updateTime;

  /** 创建人 */
  private Integer createUser;

  /** 修改人 */
  private Integer updateUser;

  /** 用户的角色身份，等于与一个系统可能拥有superAdmin、admin、user */
  private String[] roleCodes;

  /** 角色名字 */
  private String roleName;

  /** 用户的头像，数据库暂时没有，用死的 */
  private String avatar;
}
