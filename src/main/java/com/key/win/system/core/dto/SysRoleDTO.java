package com.key.win.system.core.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/** 角色DTO */
@Data
public class SysRoleDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  private String id;

  /** 角色名 */
  private String roleName;

  /** 角色说明 */
  private String roleDescription;

  /** 角色值 */
  private String roleCode;

  /** 是否启用 0 启用 1禁用 */
  private Boolean enable;

  /** 根据权限范围排序 */
  private String sort;

  /** 创建时间 */
  private Date createTime;

  /** 修改时间 */
  private Date updateTime;

  /** 创建人 */
  private String createUser;

  /** 修改人 */
  private String updateUser;

  /** 资源id集合 */
  private List<String> requestPathIds;
}
