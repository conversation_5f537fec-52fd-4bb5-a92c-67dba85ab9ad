package com.key.win.system.core.controller;

import java.util.List;

import com.key.win.system.core.service.IRoleService;
import com.key.win.system.core.vo.RequestPathVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import com.key.win.system.core.dto.ResourceTreeDTO;
import com.key.win.system.core.dto.SysRequestPathDTO;
import com.key.win.system.core.model.SysRequestPath;
import com.key.win.system.core.service.IResourceService;

/** 资源相关的controller */
@RestController
@RequestMapping("/sys/requestPath/*")
public class SysRequestPathController {
  @Autowired private IResourceService resourceService;

  @Autowired private IRoleService roleService;

  /** 新增资源 */
  @PostMapping("/saveRequestPath")
  public Result<Boolean> saveRequestPath(@RequestBody SysRequestPathDTO requestPathDTO) {
    if (requestPathDTO == null) {
      return Result.failed("资源信息不能为空");
    }
    return resourceService.saveRequestPath(requestPathDTO);
  }

  /** 修改资源信息 */
  @PostMapping("/updateRequestPath")
  public Result<Boolean> updateRequestPath(@RequestBody SysRequestPathDTO requestPathDTO) {
    if (requestPathDTO == null || requestPathDTO.getId() == null) {
      return Result.failed("修改资源信息失败");
    }

    resourceService.updateRequestPath(requestPathDTO);
    return Result.succeed("修改资源信息成功");
  }

  /** 根据资源id删除资源 解除资源角色关系 解除权限资源关系 */
  @PostMapping("/deleteRequestPath/{id}")
  public Result<Boolean> deleteRequestPathById(@PathVariable("id") String id) {
    boolean notBlank = StringUtils.isNotBlank(id);
    if (notBlank) {
      resourceService.deleteRequestPathById(id);
      return Result.succeed("删除资源信息成功");
    }
    return Result.failed("删除资源信息失败");
  }

  //    /**
  //     * 根据角色查询角色所拥有的资源ID
  //     * @param roleId
  //     * @return
  //     */
  //    @PostMapping("/queryResourceIdByRoleId/{roleId}")
  //    public Result<List<RequestPathVO>> queryResourceIdByRoleId(@PathVariable("roleId") String
  // roleId){
  //        List<RequestPathVO> requestPathVOS = roleService.resourcesByRoleId(roleId);
  //        return Result.succeed(requestPathVOS,"角色所含资源加载完毕");
  //
  //    }

  /** 菜单管理对于新增|编辑时，加载的全量菜单列表 */
  @PostMapping("/queryResourcesTree")
  public Result<List<ResourceTreeDTO>> queryResourcesTree() {
    List<ResourceTreeDTO> requestPathTreeList = resourceService.queryResourcesTree();
    return Result.succeed(requestPathTreeList, "查询资源列表成功");
  }

  /** 菜单管理界面的tree列表界面API */
  @PostMapping("/showResourcesByName")
  public Result<List<ResourceTreeDTO>> showResourcesTree() {
    List<ResourceTreeDTO> requestPathTreeList = resourceService.showResourcesTree();
    return Result.succeed(requestPathTreeList, "查询资源列表成功");
  }

  /** 分页查询资源信息 */
  @PostMapping("/queryResourcesByPage")
  public PageResult<SysRequestPath> queryResourcesByPage(
      @RequestBody PageRequest<SysRequestPath> pageRequest) {
    return resourceService.queryResourcesByPage(pageRequest);
  }
}
