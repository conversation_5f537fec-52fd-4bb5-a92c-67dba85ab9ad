package com.key.win.system.core.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.key.win.common.web.Result;
import com.key.win.system.core.dto.SysRoleDTO;
import com.key.win.system.core.model.SysRequestPath;
import com.key.win.system.core.model.SysRole;
import com.key.win.system.core.vo.PermissionVO;
import com.key.win.system.core.vo.RequestPathVO;
import com.key.win.system.core.vo.RoleRequestPathVO;
import com.key.win.system.core.vo.RoleVO;

/**
 * 角色基础服务类 提供CRUD
 *
 * <AUTHOR>
 */
public interface IRoleService extends IService<SysRole> {

  /** 1.根据角色Id删除角色 2.解除角色菜单关系 3.解除角色权限关系 */
  void deleteRoleById(String roleId);

  /** 新增角色 */
  Result<Boolean> saveRole(SysRoleDTO sysRoleDTO);

  /** 修改角色信息 */
  void updateRole(SysRoleDTO sysRoleDTO);

  /**
   * 根据角色id查询当前角色下的权限列表
   *
   * @param roleId 角色Id
   * @return 权限列表
   */
  List<PermissionVO> permissionsByRole(String roleId);

  /**
   * 根据用户id查询当前用户下的角色列表
   *
   * @param userId 用户id
   * @return 角色列表
   */
  List<RoleVO> rolesByUserId(String userId);

  /**
   * 根据角色id查询角色下可访问的URL地址
   *
   * @param roleId
   * @return
   */
  List<RoleRequestPathVO> resourcesByRoleId(String roleId);

  /**
   * 根据角色ID列表获取 所有角色可访问资源【并集】
   *
   * @return
   */
  List<SysRequestPath> resourcesByRoleIdList(List<String> roleIds);

  /**
   * 根据请求路径匹配能访问的角色
   *
   * @param requestPath
   * @return
   */
  List<RequestPathVO> rolesByRequestPath(String requestPath);

  /** 查询所有角色列表 */
  List<RoleVO> roleList();
}
