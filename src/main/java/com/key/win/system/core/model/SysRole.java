package com.key.win.system.core.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户角色表
 *
 * <AUTHOR>
 * @since 2022-03-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("sys_role")
public class SysRole implements Serializable {

  private static final long serialVersionUID = 1L;

  /** 主键id */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private String id;

  /** 角色名 */
  private String roleName;

  /** 角色说明 */
  private String roleDescription;

  /** 角色值 */
  private String roleCode;

  /** 是否启用 0 启用 1禁用 */
  private Boolean enable;

  /** 根据权限范围排序 */
  private String sort;

  /** 创建时间 */
  private Date createTime;

  /** 修改时间 */
  private Date updateTime;

  /** 创建人 */
  private String createUser;

  /** 修改人 */
  private String updateUser;

  /** 资源id集合 */
  @TableField(exist = false)
  private List<String> requestPathIds;
}
