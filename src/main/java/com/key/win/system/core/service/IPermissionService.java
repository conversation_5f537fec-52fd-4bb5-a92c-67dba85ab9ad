package com.key.win.system.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.system.core.dto.SysPermissionDTO;
import com.key.win.system.core.model.SysPermission;
import com.key.win.system.core.vo.PermissionVO;

import java.util.List;

public interface IPermissionService extends IService<SysPermission> {

  /** 此处添加业务相关的方法或者需要特殊处理的业务逻辑 */
  List<PermissionVO> resourcesByPermissionId(String permissionId);

  void savePermission(SysPermissionDTO permissionDTO);

  void updatePermissionById(SysPermissionDTO permissionDTO);

  void deletePermissionById(Integer permissionId);

  PageResult<PermissionVO> queryPermissionByPage(PageRequest<SysPermissionDTO> pageRequest);
}
