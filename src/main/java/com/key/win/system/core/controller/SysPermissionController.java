package com.key.win.system.core.controller;

import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import com.key.win.system.core.dto.SysPermissionDTO;
import com.key.win.system.core.service.IPermissionService;
import com.key.win.system.core.vo.PermissionVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/** 权限相关的controller */
@RestController
@RequestMapping("/sys/permission/*")
public class SysPermissionController {

  @Autowired private IPermissionService permissionService;

  /**
   * 根据权限id查询当前权限下的资源列表
   *
   * @param permissionId 权限Id
   * @return 资源列表
   */
  @PostMapping("/resourcesByPermission/{permissionId}")
  public Result<List<PermissionVO>> resourcesByPermissionId(
      @PathVariable("permissionId") String permissionId) {

    boolean permissionIdIsBlank = StringUtils.isNotBlank(permissionId);
    if (permissionIdIsBlank) {
      List<PermissionVO> resources = permissionService.resourcesByPermissionId(permissionId);
      return Result.succeed(resources, "资源列表获取成功");
    } else {
      return Result.failed("未获取到资源相关信息");
    }
  }

  /** 新增权限 */
  @PostMapping("/savePermission")
  public Result<Boolean> savePermission(@RequestBody SysPermissionDTO permissionDTO) {
    if (permissionDTO == null) {
      return Result.failed("权限信息不能为空");
    }
    permissionService.savePermission(permissionDTO);
    return Result.succeed("权限添加成功");
  }

  /** 修改权限信息 */
  @PostMapping("/updatePermission")
  public Result<Boolean> updatePermissionById(@RequestBody SysPermissionDTO permissionDTO) {
    if (permissionDTO == null || permissionDTO.getId() == null) {
      return Result.failed("修改权限信息失败");
    }
    permissionService.updatePermissionById(permissionDTO);
    return Result.succeed("修改权限信息成功");
  }

  /**
   * 1.根据权限Id删除权限信息 2.解除角色权限关系 3.解除权限资源关系
   *
   * @param id 权限Id
   */
  @PostMapping("/deletePermission/{id}")
  public Result<Boolean> deletePermissionById(@PathVariable("id") Integer id) {
    if (id == null) {
      return Result.failed("删除权限信息失败");
    }
    permissionService.deletePermissionById(id);
    return Result.succeed("删除权限信息成功");
  }

  /** 分页查询权限信息 */
  @PostMapping("/queryPermissionByPage")
  public PageResult<PermissionVO> queryPermissionByPage(
      @RequestBody PageRequest<SysPermissionDTO> pageRequest) {
    return permissionService.queryPermissionByPage(pageRequest);
  }
}
