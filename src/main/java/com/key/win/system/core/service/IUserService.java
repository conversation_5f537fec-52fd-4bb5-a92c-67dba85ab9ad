package com.key.win.system.core.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import com.key.win.system.core.dto.SysUserDTO;
import com.key.win.system.core.dto.UserAndRole;
import com.key.win.system.core.dto.UserPasswordDTO;
import com.key.win.system.core.model.SysUser;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/30 14:08
 * @description
 */
public interface IUserService extends IService<SysUser> {

  List<UserAndRole> getUserInfoByAccount(String memberAccountByJwtToken);

  /** 新增用户 */
  void save(SysUserDTO sysUserDTO);

  /** 删除用户 */
  void deleteByUserId(String id);

  /** 修改用户信息 */
  void updateUser(SysUserDTO sysUserDTO);

  /** 分页查询用户信息 */
  PageResult<SysUser> getUserinfoByPage(PageRequest<SysUser> pageRequest);

  /** 重置密码 */
  void updateUserPassword(SysUserDTO sysUserDTO);

  /** 修改用户密码 */
  Result<Boolean> checkAndChangePassword(UserPasswordDTO passwordDTO);
}
