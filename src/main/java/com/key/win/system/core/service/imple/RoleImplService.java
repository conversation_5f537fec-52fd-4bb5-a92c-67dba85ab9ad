package com.key.win.system.core.service.imple;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.key.win.common.result.ResultCode;
import com.key.win.common.utils.SuperAccountUtils;
import com.key.win.common.web.Result;
import com.key.win.system.core.dao.SysRoleMapper;
import com.key.win.system.core.dao.SysRoleRequestPathRelationMapper;
import com.key.win.system.core.dao.SysUserMapper;
import com.key.win.system.core.dao.SysUserRoleRelationMapper;
import com.key.win.system.core.dto.SysRoleDTO;
import com.key.win.system.core.model.*;
import com.key.win.system.core.service.IRoleService;
import com.key.win.system.core.vo.PermissionVO;
import com.key.win.system.core.vo.RequestPathVO;
import com.key.win.system.core.vo.RoleRequestPathVO;
import com.key.win.system.core.vo.RoleVO;
import com.key.win.utils.BeanConvertUtils;
import com.key.win.utils.SpringSecurityUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;

@Service("sysRoleService")
@Transactional
public class RoleImplService extends ServiceImpl<SysRoleMapper, SysRole> implements IRoleService {
  /** 注入用户的mapper层接口，查询数据库 */
  @Autowired private SysRoleMapper roleMapper;

  @Autowired private SysUserRoleRelationMapper userRoleRelationMapper;

  @Autowired private SysUserMapper userMapper;
  @Autowired private SysRoleRequestPathRelationMapper roleRequestPathRelationMapper;

  /**
   * 1.根据角色Id删除角色 2.解除用户角色关系 3.解除角色菜单关系
   *
   * @param roleId 角色Id
   */
  @Override
  public void deleteRoleById(String roleId) {
    roleMapper.deleteById(roleId);
    // 解除用户角色关系
    LambdaQueryWrapper<SysUserRoleRelation> urw = new LambdaQueryWrapper<>();
    urw.eq(SysUserRoleRelation::getRoleId, roleId);
    userRoleRelationMapper.delete(urw);
    // 解除角色菜单关系
    LambdaQueryWrapper<SysRoleRequestPathRelation> rpw = new LambdaQueryWrapper<>();
    rpw.eq(SysRoleRequestPathRelation::getRoleId, roleId);
    roleRequestPathRelationMapper.delete(rpw);
  }

  // 新增角色
  @Override
  public Result<Boolean> saveRole(SysRoleDTO sysRoleDTO) {
    // 查询db，验证角色信息是否存在
    LambdaQueryWrapper<SysRole> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(SysRole::getRoleCode, sysRoleDTO.getRoleCode()).eq(SysRole::getEnable, 1);
    SysRole sysRole = roleMapper.selectOne(queryWrapper);
    // 不存在则新增
    if (sysRole == null) {
      sysRole = BeanConvertUtils.convertTo(sysRoleDTO, SysRole::new);
      // 设置基本字段信息
      sysRole.setCreateTime(new Date());
      sysRole.setUpdateTime(new Date());
      // 获取当前登录用户
      SysUser currentUser = getCurrentUser();
      if (currentUser != null) {
        // 当前登录用户的id
        sysRole.setCreateUser(currentUser.getId());
        sysRole.setUpdateUser(currentUser.getId());
      }
      sysRole = BeanConvertUtils.convertTo(sysRoleDTO, SysRole::new);
      roleMapper.insert(sysRole);
      // 角色资源关系表中保存数据
      List<String> requestPathIds = sysRoleDTO.getRequestPathIds();
      boolean notEmpty = CollectionUtils.isNotEmpty(requestPathIds);
      if (notEmpty) {
        for (String requestPathId : requestPathIds) {
          SysRoleRequestPathRelation roleRequestPath = new SysRoleRequestPathRelation();
          roleRequestPath.setRoleId(sysRole.getId());
          roleRequestPath.setRequestPathId(requestPathId);
          roleRequestPathRelationMapper.insert(roleRequestPath);
        }
      }
      return Result.succeed("新增角色成功");
    }
    return Result.failed(
        ResultCode.ROLE_CODE_ALREADY_EXIST.getCode(),
        ResultCode.ROLE_CODE_ALREADY_EXIST.getMessage());
  }

  // 修改角色信息
  @Override
  public void updateRole(SysRoleDTO sysRoleDTO) {
    SysRole sysRole = roleMapper.selectById(sysRoleDTO.getId());
    if (sysRole != null) {
      sysRole.setUpdateTime(new Date());
      // 修改人为当前登录用户
      SysUser currentUser = getCurrentUser();
      if (currentUser != null) {
        sysRole.setUpdateUser(currentUser.getId());
      }
      sysRole = BeanConvertUtils.convertTo(sysRoleDTO, SysRole::new);
      roleMapper.updateById(sysRole);
      // 保存角色资源关系    (先删除再保存)
      Map<String, Object> columnMap = new HashMap<String, Object>();
      columnMap.put("role_id", sysRoleDTO.getId());
      roleRequestPathRelationMapper.deleteByMap(columnMap);
      if (CollectionUtils.isNotEmpty(sysRoleDTO.getRequestPathIds())) {
        for (String requestPathId : sysRoleDTO.getRequestPathIds()) {
          SysRoleRequestPathRelation roleRequestPathRelation = new SysRoleRequestPathRelation();
          roleRequestPathRelation.setRoleId(sysRoleDTO.getId());
          roleRequestPathRelation.setRequestPathId(requestPathId);
          roleRequestPathRelationMapper.insert(roleRequestPathRelation);
        }
      }
    }
  }

  /**
   * 根据当前登录用户名获取当前登录用户
   *
   * @return 当前登录用户
   */
  private SysUser getCurrentUser() {
    String currentUserName = SpringSecurityUtils.getUserName();

    LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(SysUser::getAccount, currentUserName);
    SysUser currentUser = userMapper.selectOne(queryWrapper);
    return currentUser;
  }

  // 根据角色id查询当前角色下的权限列表
  @Override
  public List<PermissionVO> permissionsByRole(String roleId) {
    List<PermissionVO> permissions = roleMapper.permissionsByRole(roleId);
    return permissions;
  }

  // 根据用户id查询当前用户下的角色列表
  @Override
  public List<RoleVO> rolesByUserId(String userId) {
    List<RoleVO> roles = roleMapper.rolesByUserId(userId);
    getRecourseIds(roles);

    return roles;
  }

  /** 为角色赋值菜单id */
  private void getRecourseIds(List<RoleVO> roles) {
    // 查询角色菜单关系表
    for (RoleVO role : roles) {
      LambdaQueryWrapper<SysRoleRequestPathRelation> wrapper = new LambdaQueryWrapper<>();
      wrapper.eq(SysRoleRequestPathRelation::getRoleId, role.getId());
      List<SysRoleRequestPathRelation> sysRoleRequestPathRelations =
          roleRequestPathRelationMapper.selectList(wrapper);
      List<String> requestPathIds = new ArrayList<>();
      for (SysRoleRequestPathRelation sysRoleRequestPathRelation : sysRoleRequestPathRelations) {
        requestPathIds.add(sysRoleRequestPathRelation.getRequestPathId());
      }
      role.setRequestPathIds(requestPathIds);
    }
  }

  /** 根据角色id查询当前角色下的资源列表 */
  @Override
  public List<RoleRequestPathVO> resourcesByRoleId(String roleId) {
    List<RoleRequestPathVO> resourcesList = roleMapper.resourcesByRoleId(roleId);
    return resourcesList;
  }

  /** 查询所有角色 */
  @Override
  public List<RoleVO> roleList() {
    LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(SysRole::getEnable, 1);
    wrapper.orderByAsc(SysRole::getSort);
    // 2024-07-04添加功能
    if(!SuperAccountUtils.isSuperAccount()){
      wrapper.ne(SysRole::getRoleCode,SuperAccountUtils.DEBUG_ROLE_CODE);
    }
    List<SysRole> roleList = roleMapper.selectList(wrapper);

    List<RoleVO> roleVOList = BeanConvertUtils.convertListTo(roleList, RoleVO::new);
    getRecourseIds(roleVOList);
    return roleVOList;
  }

  @Override
  public List<RequestPathVO> rolesByRequestPath(String requestPath) {
    List<RequestPathVO> needRoleList = roleMapper.rolesByRequestPath(requestPath);
    return needRoleList;
  }

  @Override
  public List<SysRequestPath> resourcesByRoleIdList(List<String> roleIds) {
    return roleMapper.resourcesByRoleIdList(roleIds);
  }
}
