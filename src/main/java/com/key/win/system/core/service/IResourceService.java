package com.key.win.system.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import com.key.win.system.core.dto.SysRequestPathDTO;
import com.key.win.system.core.dto.ResourceTreeDTO;
import com.key.win.system.core.model.SysRequestPath;

import java.util.List;

public interface IResourceService extends IService<SysRequestPath> {

  /** 修改资源信息 */
  void updateRequestPath(SysRequestPathDTO requestPathDTO);

  /** 根据资源id删除资源 解除资源角色关系 解除权限资源关系 */
  void deleteRequestPathById(String id);

  /** 查询资源tree */
  List<ResourceTreeDTO> queryResourcesTree();

  /** 首页展示资源tree */
  List<ResourceTreeDTO> showResourcesTree();

  /** 新增资源 */
  Result<Boolean> saveRequestPath(SysRequestPathDTO requestPathDTO);

  /** 分页查询资源信息 */
  PageResult<SysRequestPath> queryResourcesByPage(PageRequest<SysRequestPath> pageRequest);
}
