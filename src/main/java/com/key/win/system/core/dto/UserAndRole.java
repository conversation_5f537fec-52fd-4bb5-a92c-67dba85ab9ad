package com.key.win.system.core.dto;

import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/30 14:35
 * @description 用户表和角色表的联查实体类
 */
@Data
@AllArgsConstructor
public class UserAndRole {

  /** 用户的昵称 */
  private String userName;

  /** 用户的角色身份，等于与一个系统可能拥有superAdmin、admin、user */
  private String roleCode;

  /** 角色名字 */
  private String roleName;

  @Override
  public boolean equals(Object o) {

    if (this == o) {
      return true;
    }

    if (o == null || getClass() != o.getClass()) {
      return false;
    }

    UserAndRole ur = (UserAndRole) o;
    return Objects.equals(roleCode, ur.getRoleCode());
  }

  @Override
  public int hashCode() {
    return Objects.hash(roleCode);
  }
}
