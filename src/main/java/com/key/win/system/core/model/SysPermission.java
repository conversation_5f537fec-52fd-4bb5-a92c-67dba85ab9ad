package com.key.win.system.core.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 权限表
 *
 * <AUTHOR>
 * @since 2022-03-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("sys_permission")
public class SysPermission implements Serializable {

  private static final long serialVersionUID = 1L;

  /** 主键id */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private String id;

  /** 权限code */
  private String permissionCode;

  /** 权限名 */
  private String permissionName;
}
