package com.key.win.system.core.service.imple;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.key.win.common.result.ResultCode;
import com.key.win.common.utils.SuperAccountUtils;
import com.key.win.common.web.MybatiesPageServiceTemplate;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.common.web.Result;
import com.key.win.system.core.dao.SysRoleMapper;
import com.key.win.system.core.dao.SysUserMapper;
import com.key.win.system.core.dao.SysUserRoleRelationMapper;
import com.key.win.system.core.dto.SysUserDTO;
import com.key.win.system.core.dto.UserAndRole;
import com.key.win.system.core.dto.UserPasswordDTO;
import com.key.win.system.core.model.SysUser;
import com.key.win.system.core.model.SysUserRoleRelation;
import com.key.win.system.core.service.IUserService;
import com.key.win.utils.BeanConvertUtils;
import com.key.win.utils.SpringSecurityUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/30 14:08
 * @description
 */
@Service
@Transactional
public class UserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements IUserService {

  /** 注入用户的mapper层接口，查询数据库 */
  @Autowired private SysUserMapper userMapper;

  @Autowired private PasswordEncoder passwordEncoder;

  @Autowired private SysUserRoleRelationMapper userRoleRelationMapper;

  @Autowired private SysRoleMapper roleMapper;

  @Override
  public List<UserAndRole> getUserInfoByAccount(String memberAccountByJwtToken) {
    List<UserAndRole> userAndRole = userMapper.getUserInfoByAccount(memberAccountByJwtToken);
    return userAndRole;
  }

  /** 新增用户 */
  @Override
  public void save(SysUserDTO sysUserDTO) {
    SysUser sysUser = userMapper.selectById(sysUserDTO.getId());
    if (sysUser == null) {
      // 新增用户到DB
      sysUser = BeanConvertUtils.convertTo(sysUserDTO, SysUser::new);
      // 补全用户信息
      sysUser.setPassword(passwordEncoder.encode(sysUserDTO.getPassword()));
      sysUser.setLastLoginTime(new Date());
      sysUser.setCreateTime(new Date());
      sysUser.setUpdateTime(new Date());

      // 获取当前登录用户
      SysUser currentUser = getCurrentUser();
      if (currentUser != null) {
        // 当前登录用户的id
        sysUser.setCreateUser(currentUser.getId());
        sysUser.setUpdateUser(currentUser.getId());
      }
      userMapper.insert(sysUser);
      // 绑定用户角色关系
      for (String roleId : sysUserDTO.getRoleIds()) {
        SysUserRoleRelation userRoleRelation = new SysUserRoleRelation();
        userRoleRelation.setUserId(sysUser.getId());
        userRoleRelation.setRoleId(roleId);
        userRoleRelationMapper.insert(userRoleRelation);
      }
    }
  }

  /**
   * 根据用户ID删除用户 解除用户角色关系
   *
   * @param id 用户id集合
   */
  @Override
  public void deleteByUserId(String id) {
    userMapper.deleteById(id);
    // 解除用户角色关系
    LambdaQueryWrapper<SysUserRoleRelation> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(SysUserRoleRelation::getUserId, id);
    userRoleRelationMapper.delete(wrapper);
  }

  /** 修改用户信息 */
  @Override
  public void updateUser(SysUserDTO sysUserDTO) {
    SysUser sysUser = userMapper.selectById(sysUserDTO.getId());
    if (sysUser != null) {
      SysUser currentUser = getCurrentUser();
      if (currentUser != null) {
        sysUserDTO.setUpdateUser(currentUser.getId());
      }
      sysUserDTO.setUpdateTime(new Date());
      sysUser = BeanConvertUtils.convertTo(sysUserDTO, SysUser::new);
      userMapper.updateById(sysUser);
    }
    // 修改用户角色关系  (先删除再保存)
    Map<String, Object> columnMap = new HashMap<String, Object>();
    columnMap.put("user_id", sysUserDTO.getId());
    userRoleRelationMapper.deleteByMap(columnMap);
    if (CollectionUtils.isNotEmpty(sysUserDTO.getRoleIds())) {
      for (String roleId : sysUserDTO.getRoleIds()) {
        SysUserRoleRelation userRoleRelation = new SysUserRoleRelation();
        userRoleRelation.setUserId(sysUserDTO.getId());
        userRoleRelation.setRoleId(roleId);
        userRoleRelationMapper.insert(userRoleRelation);
      }
    }
  }

  /**
   * 根据当前登录用户名获取当前登录用户
   *
   * @return 当前登录用户
   */
  private SysUser getCurrentUser() {
    // 从Security认证管理器中取出用户账号
    String currentUserName = SpringSecurityUtils.getUserName();

    LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
    // Security的用户名对应用户表的账号
    queryWrapper.eq(SysUser::getAccount, currentUserName);
    SysUser currentUser = userMapper.selectOne(queryWrapper);

    return currentUser;
  }

  /** 分页查询用户信息 */
  @Override
  public PageResult<SysUser> getUserinfoByPage(PageRequest<SysUser> pageRequest) {
    MybatiesPageServiceTemplate<SysUser, SysUser> page =
        new MybatiesPageServiceTemplate<SysUser, SysUser>(this.baseMapper) {
          @Override
          protected Wrapper<SysUser> constructWrapper(SysUser sysUser) {
            LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
            if (sysUser == null) {
              return queryWrapper;
            }
            if (StringUtils.isNotBlank(sysUser.getUserName())) {
              queryWrapper.like(SysUser::getUserName, sysUser.getUserName());
            }
            //2024-07-04添加功能
            if(!SuperAccountUtils.isSuperAccount()){
              queryWrapper.ne(SysUser::getAccount,SuperAccountUtils.DEBUG_ROLE_CODE);
            }
            return queryWrapper;
          }
        };
    PageResult<SysUser> userPageResult = page.doPagingQuery(pageRequest);
    // 返回用户对应的角色列表
    List<SysUser> sysUserList = userPageResult.getData();
    for (SysUser sysUser : sysUserList) {
      LambdaQueryWrapper<SysUserRoleRelation> queryWrapper = new LambdaQueryWrapper<>();
      queryWrapper.eq(SysUserRoleRelation::getUserId, sysUser.getId());
      List<SysUserRoleRelation> sysUserRoleRelationList =
          userRoleRelationMapper.selectList(queryWrapper);
      List<String> roleIds = new ArrayList<>();
      for (SysUserRoleRelation sysUserRoleRelation : sysUserRoleRelationList) {
        roleIds.add(sysUserRoleRelation.getRoleId());
      }
      sysUser.setRoleIds(roleIds);
    }
    return userPageResult;
  }

  /** 重置用户密码 */
  @Override
  public void updateUserPassword(SysUserDTO sysUserDTO) {
    SysUser sysUser = userMapper.selectById(sysUserDTO.getId());
    if (sysUser != null) {
      sysUser.setPassword(passwordEncoder.encode(sysUserDTO.getPassword()));
      userMapper.updateById(sysUser);
    }
  }

  /** 修改用户密码 */
  @Override
  public Result<Boolean> checkAndChangePassword(UserPasswordDTO passwordDTO) {
    SysUser sysUser = userMapper.selectById(passwordDTO.getId());
    // 校验用户输入的密码和db密码是否一致
    boolean matches = passwordEncoder.matches(passwordDTO.getOldPassword(), sysUser.getPassword());
    if (!matches) {
      return Result.failed(
          ResultCode.USER_CREDENTIALS_ERROR.getCode(),
          ResultCode.USER_CREDENTIALS_ERROR.getMessage());
    }
    sysUser.setPassword(passwordEncoder.encode(passwordDTO.getPassword()));
    userMapper.updateById(sysUser);
    return Result.succeed(
        ResultCode.SUCCESS_UPDATE_PASSWORD.getCode(),
        ResultCode.SUCCESS_UPDATE_PASSWORD.getMessage());
  }
}
