package com.key.win.system.core.controller;

import com.key.win.common.result.ResultCode;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.system.core.dto.SysUserDTO;
import com.key.win.system.core.dto.UserPasswordDTO;
import com.key.win.system.core.model.SysUser;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.key.win.common.web.Result;
import com.key.win.system.core.service.IUserService;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/05/31 15:56
 * @description 用户相关的controller
 */
@RestController
@RequestMapping("/sys/user/*")
public class SysUserController {

  @Autowired private IUserService userService;

  /** 新增用户 */
  @PostMapping("/saveUser")
  public Result<Boolean> saveUser(@RequestBody SysUserDTO sysUserDTO) {
    if (sysUserDTO == null) {
      return Result.failed("请求参数为空");
    }

    userService.save(sysUserDTO);

    return Result.succeed("添加成功");
  }

  /** 删除用户 */
  @PostMapping("/deleteUser/{id}")
  public Result<Boolean> deleteByUserId(@PathVariable("id") String id) {
    boolean notBlank = StringUtils.isNotBlank(id);
    if (notBlank) {
      userService.deleteByUserId(id);
      return Result.succeed("用户删除成功");
    }
    return Result.failed("用户不存在");
  }

  /** 修改用户信息 */
  @PostMapping("/updateUser")
  public Result<Boolean> updateUser(@RequestBody SysUserDTO sysUserDTO) {
    if (sysUserDTO == null || sysUserDTO.getId() == null) {

      return Result.failed("用户不存在");
    }

    userService.updateUser(sysUserDTO);
    return Result.succeed("用户信息修改成功");
  }

  /** 分页查询用户信息 */
  @PostMapping("/queryUserinfo")
  public PageResult<SysUser> queryUserinfoByPage(@RequestBody PageRequest<SysUser> pageRequest) {
    return userService.getUserinfoByPage(pageRequest);
  }

  /** 重置密码 */
  @PostMapping("/updatePassword")
  public Result<Boolean> updateUserPassword(@RequestBody SysUserDTO sysUserDTO) {
    boolean idBlank = StringUtils.isBlank(sysUserDTO.getId());
    boolean pdBlank = StringUtils.isBlank(sysUserDTO.getPassword());
    if (idBlank || pdBlank) {
      return Result.failed(
          ResultCode.USER_CREDENTIALS_ERROR.getCode(),
          ResultCode.USER_CREDENTIALS_ERROR.getMessage());
    }
    userService.updateUserPassword(sysUserDTO);
    return Result.succeed(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage());
  }

  /** 修改用户密码 */
  @PostMapping("/checkAndChangePassword")
  public Result<Boolean> checkAndChangePassword(@RequestBody UserPasswordDTO passwordDTO) {
    if (passwordDTO == null || passwordDTO.getId() == null) {
      return Result.failed(
          ResultCode.PARAM_IS_BLANK.getCode(), ResultCode.PARAM_IS_BLANK.getMessage());
    }
    return userService.checkAndChangePassword(passwordDTO);
  }
}
