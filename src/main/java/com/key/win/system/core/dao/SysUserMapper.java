package com.key.win.system.core.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.key.win.system.core.dto.UserAndRole;
import com.key.win.system.core.model.SysUser;

/**
 * 用户表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-03-28
 */
@Repository
public interface SysUserMapper extends BaseMapper<SysUser> {

  List<UserAndRole> getUserInfoByAccount(
      @Param("memberAccountByJwtToken") String memberAccountByJwtToken);
}
