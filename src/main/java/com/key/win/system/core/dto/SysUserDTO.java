package com.key.win.system.core.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/** 用户DTO */
@Data
public class SysUserDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  private String id;

  /** 账号 */
  private String account;

  /** 用户名 */
  private String userName;

  /** 用户密码 */
  private String password;

  /** 上一次登录时间 */
  private Date lastLoginTime;

  /** 账号是否可用。默认为1（可用） */
  private Boolean enabled;

  /** 是否过期。默认为1（没有过期） */
  private Boolean notExpired;

  /** 账号是否锁定。默认为1（没有锁定） */
  private Boolean accountNotLocked;

  /** 证书（密码）是否过期。默认为1（没有过期） */
  private Boolean credentialsNotExpired;

  /** 创建时间 */
  private Date createTime;

  /** 修改时间 */
  private Date updateTime;

  /** 创建人 */
  private String createUser;

  /** 修改人 */
  private String updateUser;

  /** 角色id集合 */
  private List<String> roleIds;
}
