package com.key.win.system.core.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/** 用户车站关联关系表 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("sys_user_station_relation")
public class SysUserStationRelation implements Serializable {

  private static final long serialVersionUID = 1L;

  /** 主键id */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private String id;

  /** 用户id */
  private String userId;

  /** 车站id */
  private String stationId;
}
