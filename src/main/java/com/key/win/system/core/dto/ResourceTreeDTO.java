package com.key.win.system.core.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResourceTreeDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /** 资源id */
  private String id;

  /** 请求路径名称 */
  private String requestPathName;

  /** 请求路径 */
  private String url;

  /** 上级目录id -1 主目录 */
  private String parentId;

  /** 上级目录名称 */
  private String parentName;

  /** 排序 */
  private String sort;

  /** 路径描述 */
  private String description;

  /** 路径类型 1 一级目录, 2 菜单，3 按钮 */
  private int type;

  /** 是否启用 0 启用 1禁用 */
  private Boolean enable;

  /** 目录图标 */
  private String iconClassName;

  /** ElementUI tree展示的名称 */
  private String label;

  /** 子路径列表 */
  private List<ResourceTreeDTO> childrenUrl;

  /** tree组件 节点名称取值 从 路径名称获取 */
  public String getLabel() {
    return requestPathName;
  }
}
