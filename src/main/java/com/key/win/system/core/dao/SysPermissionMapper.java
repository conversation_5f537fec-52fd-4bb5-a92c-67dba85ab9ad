package com.key.win.system.core.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.key.win.system.core.model.SysPermission;

import com.key.win.system.core.vo.PermissionVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 权限表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-03-28
 */
@Repository
public interface SysPermissionMapper extends BaseMapper<SysPermission> {

  /** 找到当前用户的权限 */
  List<SysPermission> getUserRolesByUserId(String id);

  List<SysPermission> selectListByPath(@Param("requestUrl") String requestUrl);

  List<PermissionVO> resourcesByPermissionId(@Param("permissionId") String permissionId);
}
