package com.key.win.system.core.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/** 角色-资源关联关系表 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("sys_role_request_path_relation")
public class SysRoleRequestPathRelation implements Serializable {
  private static final long serialVersionUID = 1L;

  /** 主键id */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private String id;

  /** 角色id */
  private String roleId;

  /** 资源id */
  private String requestPathId;
}
