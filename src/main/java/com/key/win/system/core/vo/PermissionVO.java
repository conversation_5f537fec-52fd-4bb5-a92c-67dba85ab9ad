package com.key.win.system.core.vo;

import lombok.Data;

import java.io.Serializable;

/** 权限资源视图关系映射实体类 */
@Data
public class PermissionVO implements Serializable {
  private static final long serialVersionUID = 1L;

  private String id;

  /** 权限code */
  private String permissionCode;

  /** 权限名 */
  private String permissionName;

  private String urlId;

  private String requestPathDescription;
}
