package com.key.win.system.core.service.imple;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.common.web.MybatiesPageServiceTemplate;
import com.key.win.common.web.PageRequest;
import com.key.win.common.web.PageResult;
import com.key.win.system.core.dao.SysRequestPathPermissionRelationMapper;
import com.key.win.system.core.dao.SysRolePermissionRelationMapper;
import com.key.win.system.core.dto.SysPermissionDTO;
import com.key.win.system.core.model.SysRequestPathPermissionRelation;
import com.key.win.system.core.model.SysRolePermissionRelation;
import com.key.win.system.core.vo.PermissionVO;
import com.key.win.utils.BeanConvertUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.key.win.system.core.dao.SysPermissionMapper;
import com.key.win.system.core.model.SysPermission;
import com.key.win.system.core.service.IPermissionService;

import javax.transaction.Transactional;
import java.util.List;

@Service("sysPermissionService")
@Transactional
public class PermissionImplService extends ServiceImpl<SysPermissionMapper, SysPermission>
    implements IPermissionService {

  @Autowired private SysPermissionMapper permissionMapper;

  @Autowired private SysRolePermissionRelationMapper rolePermissionRelationMapper;

  @Autowired private SysRequestPathPermissionRelationMapper requestPathPermissionRelationMapper;

  /**
   * 根据权限id查询当前权限下的资源列表
   *
   * @param permissionId 权限Id
   * @return 资源列表
   */
  @Override
  public List<PermissionVO> resourcesByPermissionId(String permissionId) {
    List<PermissionVO> resources = permissionMapper.resourcesByPermissionId(permissionId);
    return resources;
  }

  /** 新增权限 */
  @Override
  public void savePermission(SysPermissionDTO permissionDTO) {
    // 查询db验证权限是否已存在
    LambdaQueryWrapper<SysPermission> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper
        .eq(SysPermission::getPermissionCode, permissionDTO.getPermissionCode())
        .eq(SysPermission::getPermissionName, permissionDTO.getPermissionName());
    SysPermission sysPermission = permissionMapper.selectOne(queryWrapper);

    // 不存在则新增
    if (sysPermission == null) {
      sysPermission = BeanConvertUtils.convertTo(permissionDTO, SysPermission::new);
      permissionMapper.insert(sysPermission);
    }
  }

  /** 修改权限信息 */
  @Override
  public void updatePermissionById(SysPermissionDTO permissionDTO) {
    SysPermission sysPermission = permissionMapper.selectById(permissionDTO.getId());
    // 权限信息存在才能修改
    if (sysPermission != null) {
      sysPermission = BeanConvertUtils.convertTo(permissionDTO, SysPermission::new);
      permissionMapper.updateById(sysPermission);
    }
  }

  /**
   * 1.根据权限Id删除权限信息 2.解除角色权限关系 3.解除权限资源关系
   *
   * @param permissionId 权限Id
   */
  @Override
  public void deletePermissionById(Integer permissionId) {
    permissionMapper.deleteById(permissionId);
    // 解除角色权限关系
    LambdaQueryWrapper<SysRolePermissionRelation> rpWrapper = new LambdaQueryWrapper<>();
    rpWrapper.eq(SysRolePermissionRelation::getPermissionId, permissionId);
    rolePermissionRelationMapper.delete(rpWrapper);

    // 解除权限资源关系
    LambdaQueryWrapper<SysRequestPathPermissionRelation> prpWrapper = new LambdaQueryWrapper<>();
    prpWrapper.eq(SysRequestPathPermissionRelation::getPermissionId, permissionId);
    requestPathPermissionRelationMapper.delete(prpWrapper);
  }

  /** 分页查询权限信息 */
  @Override
  public PageResult<PermissionVO> queryPermissionByPage(PageRequest<SysPermissionDTO> pageRequest) {

    MybatiesPageServiceTemplate<SysPermissionDTO, PermissionVO> page =
        new MybatiesPageServiceTemplate<SysPermissionDTO, PermissionVO>(this.baseMapper) {
          @Override
          protected Wrapper<PermissionVO> constructWrapper(SysPermissionDTO permissionDTO) {
            LambdaQueryWrapper<PermissionVO> wrapper = new LambdaQueryWrapper<>();
            if (permissionDTO == null) {
              return wrapper;
            }
            if (StringUtils.isNotBlank(permissionDTO.getPermissionName())) {
              wrapper.eq(PermissionVO::getPermissionName, permissionDTO.getPermissionName());
            }
            if (StringUtils.isNotBlank(permissionDTO.getPermissionCode())) {
              wrapper.eq(PermissionVO::getPermissionCode, permissionDTO.getPermissionCode());
            }
            return wrapper;
          }
        };
    PageResult<PermissionVO> pageResult = page.doPagingQuery(pageRequest);
    return pageResult;
  }
}
