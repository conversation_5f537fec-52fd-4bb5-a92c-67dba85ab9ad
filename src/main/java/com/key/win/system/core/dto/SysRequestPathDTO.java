package com.key.win.system.core.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/** 资源DTO */
@Data
public class SysRequestPathDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  private String id;

  /** 请求路径名称 */
  private String requestPathName;

  /** 请求路径 */
  private String url;

  /** 路径描述 */
  private String description;

  /** 父目录id 0 主目录 */
  private String parentId;

  /** 上级目录名称 */
  private String parentName;

  /** 排序 */
  private String sort;

  /** 路径类型 1 一级目录, 2 菜单，3 按钮 */
  private int type;

  /** 是否启用 0 启用 1禁用 */
  private Boolean enable;

  /** 目录图标 */
  private String iconClassName;

  /** 创建时间 */
  private Date createTime;

  /** 修改时间 */
  private Date updateTime;

  /** 创建人 */
  private String createUser;

  /** 修改人 */
  private String updateUser;
}
