package com.key.win.system.core.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/** 角色权限视图关系映射实体类 */
@Data
public class RoleVO implements Serializable {

  private static final long serialVersionUID = 1L;

  /** 角色id */
  private String id;

  private String roleName;

  private String roleCode;

  private String roleDescription;

  /** 根据权限范围排序 */
  private String sort;

  /** 是否启用 1 启用 */
  private Boolean enable;

  /** 资源id集合 */
  private List<String> requestPathIds;
}
