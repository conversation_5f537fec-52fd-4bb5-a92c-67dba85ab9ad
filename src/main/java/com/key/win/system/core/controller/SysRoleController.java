package com.key.win.system.core.controller;

import com.key.win.common.web.Result;
import com.key.win.system.core.dto.SysRoleDTO;
import com.key.win.system.core.model.SysRole;
import com.key.win.system.core.service.IRoleService;
import com.key.win.system.core.vo.*;
import com.key.win.utils.BeanConvertUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/** 角色相关的controller */
@RestController
@RequestMapping("/sys/role/*")
public class SysRoleController {
  @Autowired private IRoleService roleService;

  /**
   * 1.根据角色Id删除角色 2.解除角色菜单关系 3.解除角色权限关系
   *
   * @param id 角色Id
   */
  @PostMapping("/deleteRole/{id}")
  public Result<Boolean> deleteRoleById(@PathVariable("id") String id) {
    boolean notBlank = StringUtils.isNotBlank(id);

    if (notBlank) {
      roleService.deleteRoleById(id);
      return Result.succeed("删除角色信息成功");
    }
    return Result.failed("删除角色信息失败");
  }

  /**
   * 根据角色id查询当前角色下的权限列表
   *
   * @param roleId 角色Id
   * @return 权限列表
   */
  @PostMapping("/permissionsByRole/{roleId}")
  public Result<List<PermissionVO>> permissionsByRole(@PathVariable("roleId") String roleId) {

    boolean roleIdIsBlank = StringUtils.isNotBlank(roleId);
    if (roleIdIsBlank) {
      List<PermissionVO> permissions = roleService.permissionsByRole(roleId);
      return Result.succeed(permissions, "权限列表获取成功");
    } else {
      return Result.failed("未获取到角色相关信息");
    }
  }

  /** 新增角色 */
  @PostMapping("/saveRole")
  public Result<Boolean> saveRole(@RequestBody SysRoleDTO sysRoleDTO) {
    if (sysRoleDTO == null) {
      return Result.failed("角色信息不能为空");
    }
    return roleService.saveRole(sysRoleDTO);
  }

  /** 修改角色信息 */
  @PostMapping("/updateRole")
  public Result<Boolean> updateRole(@RequestBody SysRoleDTO sysRoleDTO) {
    if (sysRoleDTO == null || sysRoleDTO.getId() == null) {
      return Result.failed("修改角色信息失败");
    }
    roleService.updateRole(sysRoleDTO);
    return Result.succeed("修改角色信息成功");
  }

  /**
   * 根据用户id查询当前用户下的角色列表
   *
   * @param userId 用户id
   * @return 角色列表
   */
  @PostMapping("/rolesByUser/{userId}")
  public Result<List<RoleVO>> rolesByUser(@PathVariable("userId") String userId) {
    if ("null".equals(userId)) {
      List<RoleVO> roleVOList = roleService.roleList();
      return Result.succeed(roleVOList, "角色列表获取成功");
    }
    List<RoleVO> roles = roleService.rolesByUserId(userId);
    return Result.succeed(roles, "角色列表获取成功");
  }

  /**
   * 根据角色id查询当前角色下的资源列表
   *
   * @param roleId 角色Id
   * @return 资源列表
   */
  @PostMapping("/resourcesByRole/{roleId}")
  public Result<List<RoleRequestPathVO>> resourcesByRoleId(@PathVariable("roleId") String roleId) {
    boolean roleIdIsBlank = StringUtils.isNotBlank(roleId);
    if (roleIdIsBlank) {
      List<RoleRequestPathVO> resources = roleService.resourcesByRoleId(roleId);
      return Result.succeed(resources, "资源列表获取成功");
    } else {
      return Result.failed("未获取到角色相关信息");
    }
  }
}
