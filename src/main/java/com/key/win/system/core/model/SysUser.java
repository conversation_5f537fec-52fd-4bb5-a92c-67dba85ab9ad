package com.key.win.system.core.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户表
 *
 * <AUTHOR>
 * @since 2022-03-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("sys_user")
public class SysUser implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private String id;

  /** 账号 */
  private String account;

  /** 用户名 */
  private String userName;

  /** 用户密码 */
  private String password;

  /** 上一次登录时间 */
  private Date lastLoginTime;

  /** 账号是否可用。默认为1（可用） */
  private Boolean enabled;

  /** 是否过期。默认为1（没有过期） */
  private Boolean notExpired;

  /** 账号是否锁定。默认为1（没有锁定） */
  private Boolean accountNotLocked;

  /** 证书（密码）是否过期。默认为1（没有过期） */
  private Boolean credentialsNotExpired;

  /** 创建时间 */
  private Date createTime;

  /** 修改时间 */
  private Date updateTime;

  /** 创建人 */
  private String createUser;

  /** 修改人 */
  private String updateUser;

  /** 角色id集合 */
  @TableField(exist = false)
  private List<String> roleIds;
}
