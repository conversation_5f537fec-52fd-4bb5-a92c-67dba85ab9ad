package com.key.win.system.core.dao;

import java.util.List;

import com.key.win.system.core.vo.RoleRequestPathVO;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.key.win.system.core.model.SysRequestPath;
import com.key.win.system.core.model.SysRole;
import com.key.win.system.core.vo.PermissionVO;
import com.key.win.system.core.vo.RequestPathVO;
import com.key.win.system.core.vo.RoleVO;

/**
 * 用户角色表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-03-28
 */
public interface SysRoleMapper extends BaseMapper<SysRole> {

  /**
   * 根据角色id查询权限列表
   *
   * @param roleId 角色id
   * @return
   */
  List<PermissionVO> permissionsByRole(@Param("roleId") String roleId);

  List<RoleVO> rolesByUserId(@Param("userId") String userId);

  List<RoleRequestPathVO> resourcesByRoleId(@Param("roleId") String roleId);

  List<SysRequestPath> resourcesByRoleIdList(@Param("roleIds") List<String> roleIds);

  List<RequestPathVO> rolesByRequestPath(@Param("requestPath") String requestPath);
}
