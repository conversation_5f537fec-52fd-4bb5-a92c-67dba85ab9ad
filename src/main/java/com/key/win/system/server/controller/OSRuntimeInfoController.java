package com.key.win.system.server.controller;

import java.util.List;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.key.win.system.server.po.Cpu;
import com.key.win.system.server.po.Server;
import com.key.win.system.server.po.SysFile;
import com.key.win.system.server.service.GetOSInfoService;

/**
 * @Description: TODO(这里用一句话描述这个类的作用) <AUTHOR> @Date 2019/8/9 10:15
 */
@RestController
@RequestMapping("/monitor/os/*")
public class OSRuntimeInfoController {

  Logger logger = LoggerFactory.getLogger(OSRuntimeInfoController.class);

  @Resource GetOSInfoService getOSInfoService;

  @GetMapping("/info")
  public Server getOSInfo() {
    Server server = getOSInfoService.getOSInfo();

    Cpu cpu = server.getCpu();
    // 进程数
    double processCount = cpu.getProcessCount();
    // CPU核数
    int cpuNum = cpu.getCpuNum();
    // 线程数
    double threadCount = cpu.getThreadCount();
    // CPU利用率
    double used = cpu.getUsed();

    List<SysFile> sysFiles = server.getSysFiles();

    return server;
  }
}
