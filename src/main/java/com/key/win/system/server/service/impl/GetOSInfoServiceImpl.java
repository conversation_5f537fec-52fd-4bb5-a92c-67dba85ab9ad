package com.key.win.system.server.service.impl;

import org.springframework.stereotype.Service;

import com.key.win.system.server.po.Server;
import com.key.win.system.server.service.GetOSInfoService;

/**
 * <AUTHOR> @Date 2019/8/9 11:11
 */
@Service
public class GetOSInfoServiceImpl implements GetOSInfoService {
  @Override
  public Server getOSInfo() {
    Server server = new Server();
    try {
      server.copyTo();
    } catch (Exception e) {
      e.printStackTrace();
    }
    return server;
  }
}
