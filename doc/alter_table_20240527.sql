ALTER TABLE belong_unit ADD COLUMN hardware_version VARCHAR(10) NOT NULL DEFAULT '' COMMENT '设备硬件代数（I代机，II代机）';
ALTER TABLE belong_unit ADD COLUMN install_way VARCHAR(10) NOT NULL DEFAULT 'indoor' COMMENT '安装方式（indoor-室内，outdoor-室外）';

ALTER TABLE belong_unit ADD COLUMN alarm_judge_count VARCHAR(10) NOT NULL DEFAULT '' COMMENT '告警判断次数';
ALTER TABLE belong_unit ADD COLUMN power_step VARCHAR(10) NOT NULL DEFAULT '' COMMENT '功率步进';


ALTER TABLE sf_topo_links ADD COLUMN linktext VARCHAR(20)  DEFAULT '' COMMENT '拓扑图的连接线文本';

/** 历史线损记录 **/
CREATE TABLE `his_ant_loss`  (
    `id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
    `equipment_type` varchar(20) DEFAULT '' COMMENT '设备类型',
    `ant_name` varchar(50) DEFAULT '' COMMENT '天线名称',
	`probe_id` varchar(100) DEFAULT  '' COMMENT '探针序号',
	`probe_sid` varchar(100) DEFAULT  '' COMMENT '探针编号',
	`belong_line_id` varchar(20) NULL DEFAULT '' COMMENT '线路ID',
	`belong_line_name` varchar(20) NULL DEFAULT '' COMMENT '线路名称',
	`belong_station_id` varchar(50) NULL DEFAULT '' COMMENT '车站ID编码',
    `belong_station_name` varchar(50)   NULL DEFAULT ''  COMMENT '车站名称',
    `host_number` varchar(20) NULL DEFAULT '' COMMENT '设备主机编码',
	`loss` varchar(5) NULL DEFAULT '' COMMENT '路损值',
	`remark` varchar(255) NULL DEFAULT ''  COMMENT '备注',
	`attr1` varchar(100) NULL DEFAULT '' COMMENT '扩展字段1',
	`attr2` varchar(100) NULL DEFAULT '' COMMENT '扩展字段2',
    `create_by` varchar(30)  NULL DEFAULT '' COMMENT '创建人',
    `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
	`is_deleted` TINYINT(5) NULL DEFAULT 0 COMMENT '状态值（0-正常，1-删除）',
    PRIMARY KEY (`id`) USING BTREE,
	INDEX `idx_host_ant_name`(`ant_name`,`host_number`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin ROW_FORMAT = Dynamic COMMENT = '历史天线路损值记录';


/* 新菜单，关联角色 SQL添加 */
INSERT INTO `sys_request_path`(`id`, `parent_id`, `parent_name`, `url`, `request_path_name`, `sort`, `description`, `type`, `enable`, `create_time`, `update_time`, `create_user`, `update_user`, `icon_class_name`) VALUES ('1794934124142698497', '1539482420313563137', '在线监测', '/HistoricalAntLoss', '历史线损', '', '历史天线路损记录', 2, 1, '2024-05-27 11:30:18', '2024-05-27 13:36:38', '1', '1', NULL);
INSERT INTO `sys_role_request_path_relation`(`id`, `role_id`, `request_path_id`) VALUES ('1794934234343841794', '1', '1794934124142698497');

INSERT INTO `sys_request_path`(`id`, `parent_id`, `parent_name`, `url`, `request_path_name`, `sort`, `description`, `type`, `enable`, `create_time`, `update_time`, `create_user`, `update_user`, `icon_class_name`) VALUES ('1794934124142696697', '1539431084213592066', '数据管理', '/OpticalSwitchPower', '光口监测', '', '光交换机光功率记录', 2, 1, '2024-05-27 11:30:18', '2024-05-27 13:36:38', '1', '1', NULL);
INSERT INTO `sys_role_request_path_relation`(`id`, `role_id`, `request_path_id`) VALUES ('1794934234343866794', '1', '1794934124142696697');


/** 光交换机信息**/

CREATE TABLE `optical_switch`  (
    `id` bigint(30) COLLATE utf8mb3_bin NOT NULL,

    `belong_line_id` varchar(20) NULL DEFAULT '' COMMENT '线路ID',
	`belong_line_name` varchar(20) NULL DEFAULT '' COMMENT '线路名称',
	`belong_station_id` varchar(50) NULL DEFAULT '' COMMENT '车站ID编码',
    `belong_station_name` varchar(50)   NULL DEFAULT ''  COMMENT '车站名称',
    `host_number` varchar(20) NULL DEFAULT '' COMMENT '设备主机编码',

	`port_1` varchar(20) DEFAULT  '' COMMENT '光交换机端口1',
	`port_2` varchar(20) DEFAULT  '' COMMENT '光交换机端口2',
	`port_3` varchar(20) DEFAULT  '' COMMENT '光交换机端口3',
	`port_4` varchar(20) DEFAULT  '' COMMENT '光交换机端口4',
	`port_5` varchar(20) DEFAULT  '' COMMENT '光交换机端口5',
	`port_6` varchar(20) DEFAULT  '' COMMENT '光交换机端口6',
	`port_7` varchar(20) DEFAULT  '' COMMENT '光交换机端口7',
	`port_8` varchar(20) DEFAULT  '' COMMENT '光交换机端口8',

	`receive_power_1` varchar(20) DEFAULT  '' COMMENT '光交换机光收功率端口1',
	`output_power_1` varchar(20) DEFAULT  '' COMMENT '光交换机光发功率端口1',
	`receive_power_2` varchar(20) DEFAULT  '' COMMENT '光交换机光收功率端口2',
	`output_power_2` varchar(20) DEFAULT  '' COMMENT '光交换机光发功率端口2',
	`receive_power_3` varchar(20) DEFAULT  '' COMMENT '光交换机光收功率端口3',
	`output_power_3` varchar(20) DEFAULT  '' COMMENT '光交换机光发功率端口3',
	`receive_power_4` varchar(20) DEFAULT  '' COMMENT '光交换机光收功率端口4',
	`output_power_4` varchar(20) DEFAULT  '' COMMENT '光交换机光发功率端口4',
	`receive_power_5` varchar(20) DEFAULT  '' COMMENT '光交换机光收功率端口5',
	`output_power_5` varchar(20) DEFAULT  '' COMMENT '光交换机光发功率端口5',
	`receive_power_6` varchar(20) DEFAULT  '' COMMENT '光交换机光收功率端口6',
	`output_power_6` varchar(20) DEFAULT  '' COMMENT '光交换机光发功率端口6',
	`receive_power_7` varchar(20) DEFAULT  '' COMMENT '光交换机光收功率端口7',
	`output_power_7` varchar(20) DEFAULT  '' COMMENT '光交换机光发功率端口7',
	`receive_power_8` varchar(20) DEFAULT  '' COMMENT '光交换机光收功率端口8',
	`output_power_8` varchar(20) DEFAULT  '' COMMENT '光交换机光发功率端口8',


    `create_by` varchar(30)  NULL DEFAULT '' COMMENT '创建人',
    `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
	`is_deleted` TINYINT(5) NULL DEFAULT 0 COMMENT '状态值（0-正常，1-删除）',
     PRIMARY KEY (`id`) USING BTREE,
	 INDEX `idx_station_name`(`belong_station_name`) USING BTREE,
	 UNIQUE INDEX `idx_host_number`(`host_number`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin ROW_FORMAT = Dynamic COMMENT = '光交换机信息';